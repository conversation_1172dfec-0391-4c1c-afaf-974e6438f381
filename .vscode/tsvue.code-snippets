{"Print to console": {"prefix": "tsvue", "description": "Basic vue typescript template", "body": ["<template lang=\"pug\">", "  .container", "    | component", "</template>", "", "<script lang=\"ts\">", "import { Component, Vue, Prop } from 'vue-property-decorator';", "", "@Component({", "  components: {},", "})", "export default class componentName extends Vue {", "  private state: string = '';", "", "  @Prop() private property!: string;", "", "  public mounted() {", "    this.fetchData();", "  }", "", "  public fetchData() {}", "}", "</script>", "", "<style lang=\"stylus\" scoped></style>", ""]}}