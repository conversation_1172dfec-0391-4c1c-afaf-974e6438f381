{"eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "vue", "typescript", "typescriptreact"], "eslint.options": {"extensions": [".js", ".vue", ".ts", ".tsx"]}, "eslint.packageManager": "yarn", "files.insertFinalNewline": true, "stylusSupremacy.insertColons": false, "stylusSupremacy.insertBraces": false, "stylusSupremacy.insertSemicolons": false, "stylusSupremacy.insertNewLineAroundBlocks": "root", "stylusSupremacy.insertNewLineAroundOthers": "root", "stylusSupremacy.insertNewLineAroundImports": "nested", "stylusSupremacy.sortProperties": "grouped", "languageStylus.useSeparator": false, "editor.formatOnSave": false, "editor.rulers": [120], "editor.codeActionsOnSave": {"source.fixAll": "explicit"}}