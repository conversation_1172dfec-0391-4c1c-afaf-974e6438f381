import BaseModel from '@/models/BaseModel';
import session from '@/models/session';

describe('Model session.ts', () => {
  let idCode: string = '';

  // 登录
  it('Should sign in successfully', async () => {
    const { status, data } = await session.thirdSignIn({
      thirdAuthId: '5d2ee7620047759f710bce75',
      account: '********',
      password: 'abcd1234',
    });
    Object.assign(session.request.defaults.headers, {
      Accept: 'application/json',
      'x-4d-school': 1,
      authorization: `Token ${data.token}`,
    });
    expect(status).toBe(201);
  });

  // checkToken
  it('Should check token successfully', async () => {
    const { status } = await session.checkToken();
    expect(status).toBe(201);
  });

  // 获取 id_code
  it('Should get id_code successfully', async () => {
    const { data } = await session.getIdCode();
    idCode = data.code;
    expect(data.code).toBeTruthy();
  });

  // 检查 id_code
  it('Should check id_code successfully', async () => {
    const { status } = await session.checkIdCode(idCode);
    expect(status).toBe(201);
  });

  // 退出
  it('Should sign out successfully', async () => {
    const { status } = await session.signOut();
    expect(status).toBe(204);
  });
});
