<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import LayoutDefault from './layouts/default.vue';
import { RouteConfig } from './interfaces/IRoute';
import sessionStore from './store/modules/session.store';
import wechatSdk from './utils/wechatSdk';

@Component({
  components: {
    LayoutDefault,
  },
})
export default class App extends Vue {
  checked: boolean = false;

  get layout() {
    return `layout-${this.$route.meta.layout || 'default'}`;
  }
  get keepAlive() {
    return !!this.$route.meta.keepAlive;
  }

  @Watch('$route', { immediate: true })
  routeChange(route: RouteConfig) {
    if (route && route.meta) {
      document.title = route.meta.title || '四维优校';
    }
  }

  async created() {
    await wechatSdk.register();
    this.checkToken();
  }

  async checkToken() {
    // 路由是否需要检查权限
    const route = this.$utils.getVueRouteByPath();
    if (route.path.includes('/login')) {
      this.checked = true;
      return;
    }
    if (route && route.meta && route.meta.requireAuth === false) {
      this.checked = true;
      return;
    }
    try {
      this.checked = false;
      await sessionStore.check();
      this.checked = true;
    } catch (error) {
      // 401 , request.js 会自动重定向到授权页
      window.setTimeout(() => {
        this.checked = true;
      }, 100);
    }
  }
}
</script>

<template lang="pug">
  //- 添加 touchstart 事件，解决 iOS :active 样式无效的问题
  #app(v-loading.large="!checked" @touchstart="")
    component(:is="layout" v-if="checked")
      keepAlive(:max="10")
        router-view(v-if="keepAlive")
      router-view(v-if="!keepAlive")
</template>

<style lang="stylus">
#app
  position relative
  overflow auto
  width 100%
  height 100%
  background rgba(245, 245, 245, 1)
  font-family -apple-system, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Microsoft YaHei, sans-serif
  -webkit-overflow-scrolling touch
  -webkit-font-smoothing antialiased
  -moz-osx-font-smoothing grayscale
</style>
