import { IFormItem } from '@/interfaces/IFormItem';
import { nationOptions } from './nations';

export const baseInfoForm: IFormItem[] = [
  {
    key: 'name',
    name: '姓名',
    layout: {
      component: 'input',
      placeholder: '如：张三',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'number',
    name: '学号',
    layout: {
      component: 'input',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'major_name',
    name: '专业',
    layout: {
      component: 'input',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'aclass',
    name: '班级',
    layout: {
      component: 'input',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'sex',
    name: '性别',
    layout: {
      component: 'select',
      required: true,
      placeholder: '请选择',
      options: [{ label: '男' }, { label: '女' }],
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'identity_id',
    name: '身份证号码',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'nation',
    name: '民族',
    layout: {
      component: 'select',
      placeholder: '请选择',
      required: true,
      options: nationOptions,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'birth',
    name: '出生年月',
    layout: { component: 'date', placeholder: '请选择', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'native_place',
    name: '籍贯',
    layout: {
      component: 'input',
      placeholder: '省 市(县)',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'home_address',
    name: '家庭地址',
    layout: {
      component: 'textarea',
      placeholder: '请输入',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'tel',
    name: '家庭电话',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      disabled: true,
      type: 'tel',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'phone',
    name: '手机号码',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      type: 'phone',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'postcode',
    name: '邮编',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      disabled: true,
      type: 'number',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'tel2',
    name: '其他常用联系方式',
    layout: {
      component: 'textarea',
      placeholder: '邮箱：<EMAIL>',
      required: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'high_school',
    name: '毕业学校',
    layout: {
      component: 'input',
      placeholder: '请选择',
      required: true,
      disabled: true,
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'high_school_job',
    name: '担任工作',
    layout: { component: 'input', placeholder: '请输入', required: false },
    model: { attr_type: 'string' },
  },
  {
    key: 'specialty',
    name: '特长爱好',
    layout: { component: 'textarea', placeholder: '请输入', required: false },
    model: { attr_type: 'string' },
  },
  {
    key: 'entrance_records',
    name: '升学考试成绩（总分）',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      disabled: true,
      type: 'number',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'reward_punish',
    name: '何时何地受过何种奖励或处分',
    layout: { component: 'textarea', placeholder: '请输入', required: false },
    model: { attr_type: 'string' },
  },
];

export const resumeForm: IFormItem[] = [
  {
    key: 'begin_date',
    name: '开始时间',
    layout: { component: 'date', placeholder: '请选择', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'en_date',
    name: '结束时间',
    layout: { component: 'date', placeholder: '请选择', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'school',
    name: '在何地何校读书',
    layout: { component: 'textarea', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'duty',
    name: '任何职务',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'witness',
    name: '证明人',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
];

export const familyInfoForm: IFormItem[] = [
  {
    key: 'nickname',
    name: '称呼',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'name',
    name: '姓名',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'age',
    name: '年龄',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'politics_status',
    name: '政治面貌',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'company',
    name: '所在单位',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'company_address',
    name: '单位地址',
    layout: { component: 'textarea', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
  {
    key: 'tel',
    name: '电话',
    layout: {
      component: 'input',
      placeholder: '请输入',
      required: true,
      type: 'tel',
    },
    model: { attr_type: 'string' },
  },
  {
    key: 'holiday',
    name: '休息日',
    layout: { component: 'input', placeholder: '请输入', required: true },
    model: { attr_type: 'string' },
  },
];
