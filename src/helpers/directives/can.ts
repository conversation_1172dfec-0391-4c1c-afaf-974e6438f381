import utils from '@/utils';

/**
 * 使用说明：
 * v-can:[模块名].[关系谓词]="[权限（数组/字符串）]"
 *
 * 1. v-can:meeting="'admin'" 【admin 权限才能显示】
 * 2. v-can:meeting="['admin', 'edit']" 【admin 或 edit 权限能显示】
 * 3. v-can:meeting.some="['admin', 'edit']" 【admin 或 edit 权限能显示，等价于 2】
 * 4. v-can:meeting.every="['admin', 'edit']" 【admin 且 edit 权限能显示】
 * 5. v-can:meeting.not="['admin', 'edit']" 【不是 admin 且 不是 edit 权限能显示】
 */

export default {
  inserted(el: HTMLElement, binding: any) {
    const powers = binding.value;
    const module = binding.arg;
    if (module && powers) {
      let mode: 'every' | 'some' | 'not' = 'some';
      if (binding.modifiers.every) {
        mode = 'every';
      } else if (binding.modifiers.not) {
        mode = 'not';
      }
      if (!utils.hasPermission(module, powers, mode)) {
        el.remove();
      }
    }
  },
};
