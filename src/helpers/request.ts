import Axios, { AxiosInstance } from 'axios';
import qs from 'qs';
import { Notify } from 'vant';
import { baseStore } from '@/store/modules/base.store';
import sessionStore from '@/store/modules/session.store';
import router from '@/router';
import utils from '@/utils';
import { merge } from 'lodash';
import { useSHA256Encrypt } from './useSHA256Encrypt';

const { encryptText, decryptText } = useSHA256Encrypt();

export default (): AxiosInstance => {
  const apiUrl = process.env.VUE_APP_API_DOMAIN || '';
  const rootPath = process.env.VUE_APP_API_ROOT_PATH || '/';
  const request = Axios.create({
    baseURL: apiUrl + rootPath,
    headers: {
      Accept: 'application/json',
    },
    paramsSerializer(params) {
      return qs.stringify(params, {
        encode: true,
        arrayFormat: 'brackets',
        skipNulls: true,
      });
    },
  });

  request.interceptors.request.use(
    config => {
      baseStore.SET_LOADING(true);
      if (sessionStore.token) {
        config.headers.Authorization = `Token ${sessionStore.token}`;
      }
      if (sessionStore.schoolId) {
        config.headers['x-4d-school'] = sessionStore.schoolId;
      }
      const requestUrl = config.url || '';
      if (
        !requestUrl.includes('/storage') &&
        !requestUrl.includes('auth/session') &&
        !requestUrl.includes('auth/password') &&
        !requestUrl.includes('oauth')
      ) {
        if (Object.keys(config.data || {}).length === 0) {
          const { ivBase64, encryptedText } = encryptText(JSON.stringify(config.params || {}));
          Object.assign(config.headers, {
            'iv-encrypt': true,
            'iv-decode': ivBase64,
          });

          config.params = {
            iv_encrypted: encryptedText,
          };
        } else {
          const { ivBase64, encryptedText } = encryptText(JSON.stringify(merge({}, config.params || {}, config.data)));

          Object.assign(config.headers, {
            'iv-encrypt': true,
            'iv-decode': ivBase64,
          });

          config.params = {};
          config.data = {
            iv_encrypted: encryptedText,
          };
        }
      }

      return config;
    },
    error => {
      baseStore.SET_LOADING(false);
      return Promise.reject(error);
    },
  );

  request.interceptors.response.use(
    response => {
      const decryptedText = decryptText(response.data.iv_encrypted, response.data.iv64);

      if (decryptedText) {
        const data = JSON.parse(decryptedText);
        if (Array.isArray(data)) {
          response.data = data;
        } else {
          Object.assign(response.data, JSON.parse(decryptedText));
        }
      }
      baseStore.SET_LOADING(false);
      return response;
    },
    error => {
      const decryptedText = decryptText(error.response.data.iv_encrypted, error.response.data.iv64);
      if (decryptedText) Object.assign(error.response.data, JSON.parse(decryptedText));

      const { response } = error;
      const errorMessage = response && response.data ? response.data.message : '';
      if (!response) {
        Notify(error.message);
      } else if (response.status === 500) {
        Notify('服务器异常');
      } else if (response.status === 401) {
        const { href } = window.location;
        if (href.includes('/studying/welcome')) {
          utils.setRedirectPath('/studying/welcome');
          router.replace('/oauth?redirectUrl=/studying/welcome').catch(() => {});
          // router.replace('/login?redirectUrl=/studying/welcome').catch(() => {});
        } else if (!href.includes('/login')) {
          // const redirectPath = utils.getRedirectPath(false);
          // router.replace('/wechat_auth').catch(() => {});
          router.replace(`/oauth`).catch(() => {});
        }
      } else if (response.status === 403) {
        Notify('无权限');
      } else if (response.status === 404) {
        Notify({
          type: 'warning',
          message: errorMessage || '访问资源不存在',
        });
      } else if (response.status === 400) {
        Notify(errorMessage);
      } else if (response.status === 422) {
        Notify(errorMessage);
      } else {
        Notify(error.message);
      }
      baseStore.SET_LOADING(false);
      return Promise.reject(error);
    },
  );
  return request;
};
