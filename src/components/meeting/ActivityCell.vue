<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import attendance, { Attendance, IAttendance } from '@/models/meeting/attendance';
import activityStore from '../../store/modules/meeting/activity.store';
import { IActivity, ActivityTypes, Activity } from '../../models/meeting/activity';

@Component({
  components: {},
})
export default class ActivityCell extends Vue {
  loading: boolean = false;
  applySuccess: boolean = false;

  @Model('change', {
    type: Object,
    default: () => ({
      meeting_attendance: {},
    }),
  })
  activity!: IActivity;

  get store() {
    return activityStore;
  }
  get can() {
    return Attendance.can(this.activity.meeting_attendance || {}, this.activity);
  }

  // 报名确认
  apply() {
    if (new Date() > new Date(this.activity.end_time!)) {
      this.$message.warning('会议活动已结束');
      return;
    }
    this.$dialog.confirm({
      title: '报名',
      message: '您确定要报名此次会议活动吗？',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          try {
            this.loading = true;
            const { data } = await attendance.apply(this.activity.id!);
            Object.assign(this.activity, { meeting_attendance: data });
            this.$emit('change', { ...this.activity, meeting_attendance: data });
            this.applySuccess = true;
            done();
            this.loading = false;
          } catch (error) {
            done();
            this.loading = false;
          }
        } else {
          done();
        }
      },
    });
  }
  handleClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<template lang="pug">
.activity-cell(@click="handleClick")
  .title
    span {{ activity.title }}
  .infos
    .property
      .label 时间
      span {{ activity.begin_time | format('MM/DD HH:mm') }} - {{ activity.end_time | format('MM/DD HH:mm') }}
    .property
      .label 地点
      span {{ activity.location }}
    .property
      .label 类型
      span {{ store.typeMap[activity.type].text }}
  .footer
    .statistic
      .item
        .label 已报名
        .count {{ activity.supposed_count }}
      .item
        .label 还可报名
        .count
          template(v-if="activity.limit_count")
            | {{ activity.balance }}
          template(v-else)
            | 不限
    .actions
      van-button(type="info" v-if="!applySuccess && can.apply" @click.stop="apply" :loading="loading")
        | 报名
      van-button(color="#CCCCCC" disabled v-else-if="activity.meeting_attendance.state")
        | {{ activity.meeting_attendance.state }}
</template>

<style lang="stylus" scoped>
.activity-cell
  margin-bottom 12px
  padding 16px 16px 0
  border-radius 4px
  background rgba(255, 255, 255, 1)
  font-weight 500
  font-size 14px
  .title
    color rgba(56, 56, 56, 1)
    font-size 16px
    line-height 24px
  .label
    display inline-block
    width 68px
    color rgba(166, 166, 166, 1)
    line-height 20px
  .infos
    padding 4px 0
    border-bottom 1px solid rgba(232, 232, 232, 1)
    .property
      margin 8px 0
      line-height 20px
      span
        color rgba(166, 166, 166, 1)
        line-height 20px
  .footer
    display flex
    justify-content space-between
    align-items center
    padding 12px 0
    .statistic
      flex 1
      .item
        display inline-block
        width 68px
        .count
          margin-top 4px
          color rgba(56, 56, 56, 1)
          font-weight 400
          font-size 18px
          line-height 25px
</style>
