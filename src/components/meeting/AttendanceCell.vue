<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import attendance, { IAttendance, AttendanceStates, Attendance } from '@/models/meeting/attendance';
import activityStore from '@/store/modules/meeting/activity.store';
import attendanceStore from '../../store/modules/meeting/attendance.store';

@Component({
  components: {},
})
export default class AttendanceCell extends Vue {
  loading: boolean = false;

  @Prop({ type: Object, default: () => ({ meeting_activity: {} }), required: true }) attendance!: IAttendance;

  get store() {
    return attendanceStore;
  }
  get activityStore() {
    return activityStore;
  }
  get AttendanceStates() {
    return AttendanceStates;
  }
  get activity() {
    return this.attendance.meeting_activity || {};
  }
  get balance() {
    return (this.activity.limit_count || 0) - (this.activity.supposed_count || 0);
  }
  get can() {
    return Attendance.can(this.attendance, this.attendance.meeting_activity!);
  }

  cancelAttendance() {
    this.$dialog.confirm({
      title: '取消报名',
      message: '您确定要取消此次会议活动的报名吗？',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          try {
            this.loading = true;
            await attendance.cancel(this.attendance.id!);
            this.attendance.state = AttendanceStates.canceled;
            done();
            this.loading = false;
          } catch (error) {
            done();
            this.loading = false;
          }
        } else {
          done();
        }
      },
    });
  }

  deleteAttendance() {
    this.$dialog.confirm({
      title: '删除报名',
      message: '您确定要删除此次会议活动的报名吗？',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          try {
            this.loading = true;
            await attendanceStore.delete(this.attendance.id!);
            this.$message.success('删除成功');
            done();
            this.loading = false;
          } catch (error) {
            done();
            this.$message.error('删除失败，请稍后重试');
            this.loading = false;
          }
        } else {
          done();
        }
      },
    });
  }

  get stateStyle() {
    return {
      'state-success': this.attendance.state === AttendanceStates.checked,
      'state-primary': this.attendance.state === AttendanceStates.todo,
    };
  }
}
</script>

<template lang="pug">
.activity-cell(ref="cell")
  .title
    span {{ activity.title }}
    div.state-primary(v-if="!can.delete" :class="stateStyle")
      | {{ attendance.state }}
  .infos
    .property
      .label 时间
      span {{ activity.begin_time | format('MM/DD HH:mm') }}-{{ activity.end_time | format('MM/DD HH:mm') }}
    .property
      .label 地点
      span {{ activity.location }}
    .property
      .label 类型
      span {{ (activityStore.typeMap[activity.type] || {}).text }}
  .footer(v-if="can.operate")
    .statistic
      .item
        .label 已报名
        .count {{ activity.supposed_count }}
      .item
        .label 还可报名
        .count
          span(v-if="activity.limit_count")
            | {{ balance }}
          span(v-else)
            | 不限
    .actions
      van-button(v-if="can.cancel" @click="cancelAttendance" :loading="loading")
        | 取消报名
      van-button(v-else-if="can.delete" @click="deleteAttendance" :loading="loading")
        | 删除
</template>

<style lang="stylus" scoped>
.activity-cell
  margin-bottom 12px
  padding 16px 16px 0
  border-radius 4px
  background rgba(255, 255, 255, 1)
  font-weight 500
  font-size 14px
  .title
    display flex
    justify-content space-between
    align-items center
    color rgba(56, 56, 56, 1)
    font-size 16px
    line-height 24px
  .label
    display inline-block
    width 68px
    color rgba(166, 166, 166, 1)
    line-height 20px
  .infos
    padding 4px 0
    .property
      margin 8px 0
      line-height 20px
      span
        color rgba(166, 166, 166, 1)
        line-height 20px
  .footer
    display flex
    justify-content space-between
    align-items center
    padding 12px 0
    border-top 1px solid rgba(232, 232, 232, 1)
    .statistic
      flex 1
      .item
        display inline-block
        width 68px
        .count
          margin-top 4px
          color rgba(56, 56, 56, 1)
          font-weight 400
          font-size 18px
          line-height 25px
</style>
