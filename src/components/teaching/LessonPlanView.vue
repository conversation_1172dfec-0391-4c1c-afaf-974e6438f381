<script lang="ts">
// 课程资源 lesson_plan
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ILessonPlan, LessonPlan } from '@/models/teaching/lesson_plan';
import { compact } from 'lodash';
import Attachments from '@/components/global/Attachments.vue';
import { LessonItem } from '@/models/teaching/lesson_item';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    Attachments,
  },
})
export default class LesssonPlanView extends Vue {
  @Prop({ type: Object, default: () => ({}) }) plan!: ILessonPlan;
  @Prop({ type: Boolean, default: false }) showRecorderEntry!: boolean;

  activeKeys: string[] = [];

  get itemTypeZh() {
    return LessonItem.itemTypeZh;
  }
  get isStudent() {
    return sessionStore.role === 'student';
  }

  mounted() {
    this.activeKeys = [`${this.plan.id}`];
  }

  getPlanSectionTitle() {
    return LessonPlan.getSectionTitle(this.plan);
  }
  getSafeLink(url: string) {
    if (!url) return '#';
    return url.substr(0, 4) === 'http' ? url : `http://${url}`;
  }
  onPlanItemPreview(item: object, index: number) {
    this.$emit('preview', item, index);
  }
  showItem(item: object) {
    this.$emit('showItem', item);
  }
  getDuration(seconds?: number) {
    return typeof seconds === 'number' ? this.$utils.parseSeconds(seconds).toString() : '-';
  }
}
</script>

<template lang="pug">
van-collapse(v-model="activeKeys")
  van-collapse-item.lesson-plan-panel(:name="`${plan.id}`" :title="getPlanSectionTitle()")
    .lesson-plan-content
      .title {{ plan.title }}
      .subject {{ plan.subject }}
      RichText.body(:value="plan.body")
      .lesson-item(v-for="(item, index) in plan.lesson_items" :key="item.id")
        template(v-if="item.item_type === 'link'")
          .sub-title 资源链接
          a.link(
            v-if="item.attachments.link"
            :href="getSafeLink(item.attachments.link)"
            :alt="item.title"
            target="_blank")
            | {{ item.title || '超链接' }}
          .link.disabled-link(v-else)
            | 【无效的链接】{{ item.title }}
        template(v-else)
          .lesson-item-header(v-if="isStudent")
            .record
              .name 课前
              .duration {{ getDuration(item.student_recorder.prepare_in_sec) }}
            .record
              .name 课中
              .duration {{ getDuration(item.student_recorder.study_in_sec) }}
            .record
              .name 课后
              .duration {{ getDuration(item.student_recorder.review_in_sec) }}
            .record
              .name 合计
              .duration {{ getDuration(item.student_recorder.total_in_sec) }}
          .sub-title.flex-between
            span {{ itemTypeZh[item.item_type] || '其他附件' }}
            TextButton(icon="bar-chart-o" v-if="showRecorderEntry" @click="showItem(item)")
              | 学习情况
          .attachments
            Attachments(
              :attachments="item.attachments.attachments"
              :previewable="!isStudent"
              :downloadable="!isStudent"
              :showActions="false"
              :display="false"
              @preview="onPlanItemPreview(item, index)")
          .sub-title {{ itemTypeZh[item.item_type] || '其他附件' }}描述
          .pre-wrap.desc(v-if="item.title")
            | {{ item.title }}
</template>

<style lang="stylus" scoped>
.lesson-plan-panel
  .hiden-panel
    display none
  .lesson-plan-content
    .pre-wrap
      white-space pre-wrap
    .title
      font-size 16px
      font-weight 500
      color rgba(56,56,56,1)
      line-height 22px
      margin-bottom 12px
    .subject
      font-size 14px
      color rgba(56,56,56,1)
      line-height 22px
      margin-bottom 12px
    .body
      font-size 14px
      color rgba(128,128,128,1)
      line-height 20px
      margin-bottom 16px
      margin-top 0
    .lesson-item
      background rgba(255,255,255,1)
      border-radius 4px
      border 1px solid rgba(232,232,232,1)
      padding 12px 16px
      margin-bottom 8px
      .sub-title
        font-size 14px
        color #808080
        line-height 20px
        margin-bottom 8px
      .desc
        font-size 14px
        font-weight 400
        color rgba(56,56,56,1)
        line-height 20px
        margin 0
      .attachments
        margin-bottom 16px
      .link
        color #3DA8F5
        display block
        border-radius 4px
        padding 6px 0
      .disabled-link
        color #888888
        display block
      .lesson-item-header
        display flex
        align-items center
        justify-content space-between
        margin-bottom 10px
        .record
          .name
            font-size 12px
          .duration
            font-size 16px
            font-weight 500
            font-family 'DINCond'
</style>
