<script lang="ts">
/**
 * 课程整体安排组件
 * model: course_activity
 * RowHead 只显示 周几
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import ScrollTable from '@/components/teaching/ScrollTable.vue';
import courseActivityModel, { CourseActivity, ICourseActivity } from '@/models/teaching/course_activity';
import semesterModel, { defaultColumns, IScheduleColumn } from '@/models/teaching/semester';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    ScrollTable,
  },
})
export default class ActivitySchedule extends Vue {
  @Prop({ type: Array, default: () => [] }) activities!: ICourseActivity[]; // 所有 activity
  @Prop({ type: Array, default: () => defaultColumns }) columns!: IScheduleColumn[]; // 时间列
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: false }) isEvaluation!: boolean;

  scheduleColumns: IScheduleColumn[] = [];

  get tableRecords() {
    return CourseActivity.getScheduleRecords(this.activities);
  }

  created() {
    this.fetchCurrentSemester();
  }
  async fetchCurrentSemester() {
    const { data } = await semesterModel.setRole(sessionStore.role).current();
    this.scheduleColumns = data._scheduleColumns;
  }

  getCourseActivityClass(activity: ICourseActivity) {
    const classes: IObject = {};
    if (activity.start_unit! < 5) {
      classes.am = true;
    } else if (activity.start_unit! < 9) {
      classes.pm = true;
    } else {
      classes.night = true;
    }
    return classes;
  }
  getColHeadStyle(index: number) {
    const pos = index + 1;
    const styles: IObject = {};
    if (pos % 2 === 0) {
      styles.borderRight = '1px solid rgba(232, 232, 232, 0.3)';
    }
    if (pos % 4 === 0) {
      styles.borderRight = 'none';
    }
    if (pos < 5) {
      return { ...styles, background: '#68B4EB' };
    }
    if (pos < 9) {
      return { ...styles, background: '#F9AF36' };
    }
    return { ...styles, background: '#2F4169' };
  }
  getCellStyle(index: number) {
    switch (index) {
      case 0:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
      case 3:
        return {
          borderRight: '1px dashed #68B4EB',
          borderBottom: '1px solid #e8e8e8',
        };
      case 7:
        return {
          borderRight: '1px dashed #F9AF36',
          borderBottom: '1px solid #e8e8e8',
        };
      case 11:
        return {
          borderRight: '1px dashed #2F4169',
          borderBottom: '1px solid #e8e8e8',
        };
      default:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
    }
  }
  onClick(activity: ICourseActivity) {
    this.$emit('show', activity);
  }
}
</script>

<template lang="pug">
ScrollTable.class-schedule(:records="tableRecords" :columns="scheduleColumns" :loading="loading")
  template(#colHead="{ data, colIndex }")
    .col-head(:style="getColHeadStyle(colIndex)")
      .title {{ colIndex + 1 }}
      .range(:class="{ 'range-left': colIndex % 2 === 0 }") {{ data.range }}
  template(#rowHead="{ data }")
    .week-day
      .date {{ data.week }}
  template(#grid-cell="{ colIndex }")
    .grid-cell(:style="getCellStyle(colIndex)")
  template(#default="{ data }")
    .lesson-cell(
      @click="onClick(data)"
      :class="{ 'cell-pointer': isEvaluation }")
      .lesson(:class="getCourseActivityClass(data)")
        .name {{ data.course_set_name }} [{{ data.course_set_exam_mode }}]
        .info
          span {{ data.teacher_name || '教师？' }}，
          span {{ data.week_arr_zh}}，
          span {{ data.classroom_name || '班级？' }}，
          span {{ data.course_name }}
</template>

<style lang="stylus" scoped>
.class-schedule
  .grid-cell
    width 100%
    height 100%
    border-right 1px dashed rgba(232, 232, 232, 1)
  .col-head
    padding 8px 0px 10px
    height 100%
    color #fff
    font-size 12px
    line-height 12px
    .title
      margin-bottom 6px
      text-align center
    .range
      margin-left -3px
    .range-left
      text-align right
      margin-right -3px
  .week-day
    padding 12px 0
    height 100%
    text-align center
    .date
      margin-bottom 4px
      color #808080
      font-weight 500
      font-size 14px
      line-height 20px
    .count
      margin-top 8px
      color rgba(166, 166, 166, 1)
      font-weight 500
      font-size 12px
  .lesson-cell
    padding 6px
    .cell-pointer
      cursor pointer
    .lesson
      padding 10px 4px
      border-radius 2px
      cursor pointer
      .name
        margin-bottom 8px
        color rgba(56, 56, 56, 1)
        letter-spacing 0
        font-weight 500
        font-size 12px
        line-height 12px
      .info
        color rgba(128, 128, 128, 1)
        letter-spacing 0
        font-weight 400
        font-size 12px
        line-height 18px
    .am
      background #E1F0FB
    .pm
      background #FEF3E1
    .night
      background #E0E3E9
</style>
