export interface ITimeSegment {
  index: number;
  title: string;
  range: string;
}

const segments: ITimeSegment[] = [
  {
    index: 1,
    title: '第一节',
    range: '8:30 -',
  },
  {
    index: 2,
    title: '第二节',
    range: '- 9:50',
  },
  {
    index: 3,
    title: '第三节',
    range: '10:05 -',
  },
  {
    index: 4,
    title: '第四节',
    range: '- 11:25',
  },
  {
    index: 5,
    title: '第五节',
    range: '13:00 -',
  },
  {
    index: 6,
    title: '第六节',
    range: '- 14:20',
  },
  {
    index: 7,
    title: '第七节',
    range: '14:35 -',
  },
  {
    index: 8,
    title: '第八节',
    range: '- 15:55',
  },
  {
    index: 9,
    title: '第九节',
    range: '19:00 -',
  },
  {
    index: 10,
    title: '第十节',
    range: '- 20:20',
  },
  {
    index: 11,
    title: '第十一节',
    range: '20:40 -',
  },
  {
    index: 12,
    title: '第十二节',
    range: '- 22:00',
  },
];

export default segments;
