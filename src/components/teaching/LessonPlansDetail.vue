<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ILessonPlan, LessonPlan } from '@/models/teaching/lesson_plan';
import LessonPlanView from './LessonPlanView.vue';

@Component({
  components: {
    LessonPlanView,
  },
})
export default class TeachingLessonPlansDetail extends Vue {
  @Prop({ type: Array, default: () => [] }) lessonPlans!: ILessonPlan[];
  @Prop({ type: Boolean, default: false }) showRecorderEntry!: boolean;

  selectedPlanId: number = 0;

  getPlanSectionTitle(plan: ILessonPlan) {
    return LessonPlan.getSectionTitle(plan);
  }
  // 预览 lesson plan_item 附件
  onPreviewLessonItem(lessonPlan: ILessonPlan, planIndex: number, lessonItem: object, itemIndex: number) {
    this.$emit('itemPreview', {
      lessonPlan,
      planIndex,
      lessonItem,
      itemIndex,
    });
  }
  onShowItem(lessonItem: object) {
    this.$emit('showItem', lessonItem);
  }
}
</script>

<template lang="pug">
.lesson-plans.scroll-y
  Empty(desc="暂无教案" v-if="lessonPlans.length === 0")
  LessonPlanView.plan(
    v-for="(plan, index) in lessonPlans"
    :key="plan.id"
    :plan="plan"
    :id="`plan_${plan.id}`"
    :showRecorderEntry="showRecorderEntry"
    @showItem="(lessonItem) => { onShowItem(lessonItem) }"
    @preview="(lessonItem, itemIndex) => { onPreviewLessonItem(plan, index, lessonItem, itemIndex) }")
</template>

<style lang="stylus" scoped>
.lesson-plans
  height 100%
  overflow auto
  .plan
    margin-bottom 10px
</style>
