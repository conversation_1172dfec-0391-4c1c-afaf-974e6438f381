<script lang="ts">
// 课程计划表，y 轴为日期，x 轴为每天的时间段
import { Component, Vue, Prop } from 'vue-property-decorator';
import ScrollTable from '@/components/teaching/ScrollTable.vue';
import { defaultColumns, IScheduleColumn } from '@/models/teaching/semester';
import { ILesson, Lesson } from '@/models/teaching/lesson';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    ScrollTable,
  },
})
export default class ClassSchedule extends Vue {
  @Prop({ type: Array, default: () => [] }) lessons!: ILesson[]; // 所有课程
  @Prop({ type: Array, default: () => defaultColumns }) columns!: IScheduleColumn[]; // 时间列
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: false }) isEvaluation!: boolean;

  get role() {
    return sessionStore.role;
  }

  get tableRecords() {
    return Lesson.getScheduleRecords(this.lessons);
  }

  getLessonClass(lessonData: ILesson) {
    const classes: IObject = {};
    if (lessonData.start_unit! < 5) {
      classes.am = true;
    } else if (lessonData.start_unit! < 9) {
      classes.pm = true;
    } else {
      classes.night = true;
    }
    return classes;
  }
  getColHeadStyle(index: number) {
    const pos = index + 1;
    const styles: IObject = {};
    if (pos % 2 === 0) {
      styles.borderRight = '1px solid rgba(232, 232, 232, 0.3)';
    }
    if (pos % 4 === 0) {
      styles.borderRight = 'none';
    }
    if (pos < 5) {
      return { ...styles, background: '#68B4EB' };
    }
    if (pos < 9) {
      return { ...styles, background: '#F9AF36' };
    }
    return { ...styles, background: '#2F4169' };
  }
  getCellStyle(index: number) {
    switch (index) {
      case 0:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
      case 3:
        return {
          borderRight: '1px dashed #68B4EB',
          borderBottom: '1px solid #e8e8e8',
        };
      case 7:
        return {
          borderRight: '1px dashed #F9AF36',
          borderBottom: '1px solid #e8e8e8',
        };
      case 11:
        return {
          borderRight: '1px dashed #2F4169',
          borderBottom: '1px solid #e8e8e8',
        };
      default:
        return {
          borderRight: '1px dashed #e8e8e8',
          borderBottom: '1px solid #e8e8e8',
        };
    }
  }
  onClickLesson(data: ILesson) {
    this.$emit('show', data);
  }
  getLessonProgress(less: ILesson) {
    const { register_count, course_std_count = 0 } = less;
    const { done = 0 } = register_count || {};
    return `${Math.round((done / course_std_count) * 100) / 100}%`;
  }
}
</script>

<template lang="pug">
ScrollTable.class-schedule(
  :records="tableRecords"
  :columns="columns"
  v-loading="loading")
  template(#colHead="{ data, colIndex }")
    .col-head(:style="getColHeadStyle(colIndex)")
      .title {{ colIndex + 1 }}
      .range(:class="{ 'range-left': colIndex % 2 === 0 }") {{ data.range }}
  template(#rowHead="{ data }")
    .week-day
      .week {{ data.week }}
      .date {{ data.date }}
      .count 共{{ data.lessonCount }}节
  template(#grid-cell="{ colIndex }")
    .cell(:style="getCellStyle(colIndex)")
  template(#default="{ data }")
    .lesson-cell(@click="onClickLesson(data)")
      .lesson(:class="getLessonClass(data)")
        .name {{ data.course_set_name }} [{{ data.course_set_exam_mode }}]
        .info
          span {{ data.teacher_name || '教师？' }}，
          span {{ data.classroom_name || '班级？' }}，
          span {{ data.course_name }}
        template(v-if="isEvaluation")
          .info(v-if="role === 'teacher'")
            span.text-primary(v-if="data.evaluation_stat.count")
              | 评价：{{ data.evaluation_stat.done || '-' }} / {{ data.evaluation_stat.count }}
            span.text-gray(v-else) 评价：无活动
          .progress.flex(v-if="role === 'student'")
            template
              TaTag.tag(size="mini" v-if="!data.register_state")
                | 未开始
              TaTag.tag(size="mini" type="primary" v-else-if="data.register_state === 'undo'")
                | 待签到
              TaTag.tag(size="mini" type="success" v-else-if="data.register_state === 'done'")
                | 已签到
              TaTag.tag(size="mini" type="warning" v-else-if="data.register_state === 'late'")
                | 迟到
              TaTag.tag(size="mini" type="danger" v-else-if="data.register_state === 'absent'")
                | 缺席
            template
              TaTag.tag(size="mini" type="primary" v-if="data.student_evaluation_state === 'todo'")
                | 待评价
              TaTag.tag(size="mini" type="warning" v-else-if="data.student_evaluation_state === 'doing'")
                | 待提交
              TaTag.tag(size="mini" type="success" v-else-if="data.student_evaluation_state === 'done'")
                | 已评价
            template
              TaTag.tag(type="default" size="mini" v-if="data.report_state === 'none'")
                | 无作业
              TaTag.tag(type="warning" size="mini" v-else-if="data.report_state === 'pending'")
                | 未提交
              TaTag.tag(type="primary" size="mini" v-else-if="data.report_state === 'published'")
                | 已提交
              TaTag.tag(type="success" size="mini" v-else-if="data.report_state === 'scored'")
                | 已评分
</template>

<style lang="stylus" scoped>
.class-schedule
  .cell
    width 100%
    height 100%
    border-right 1px dashed rgba(232, 232, 232, 1)
  .col-head
    padding 8px 0px 10px
    height 100%
    color #fff
    font-size 12px
    line-height 12px
    .title
      margin-bottom 6px
      text-align center
    .range
      margin-left -3px
    .range-left
      text-align right
      margin-right -3px
  .week-day
    padding 12px 0
    height 100%
    text-align center
    .week
      margin-bottom 4px
      color #808080
      font-weight 500
      font-size 14px
      line-height 20px
    .date, .count
      margin-top 8px
      color rgba(166, 166, 166, 1)
      font-weight 500
      font-size 12px
  .lesson-cell
    padding 6px
    .lesson
      padding 10px 4px
      border-radius 2px
      cursor pointer
      .name
        margin-bottom 4px
        color rgba(56, 56, 56, 1)
        letter-spacing 0
        font-weight 500
        font-size 12px
        line-height 14px
        width 100%
      .info
        color rgba(128, 128, 128, 1)
        letter-spacing 0
        font-weight 400
        font-size 12px
        line-height 18px
      .progress
        position relative
        margin 6px -3px -11px
        border-bottom-right-radius 2px
        border-bottom-left-radius 2px
        color rgba(61, 168, 245, 1)
        text-align center
        font-weight 600
        display flex
        .tag
          border-radius 0
          width 100%
          white-space nowrap
        span
          position absolute
          top 50%
          left 50%
          display inline-block
          white-space nowrap
          transform translate(-50%, -50%)
    .am
      background #E1F0FB
    .pm
      background #FEF3E1
    .night
      background #E0E3E9
</style>
