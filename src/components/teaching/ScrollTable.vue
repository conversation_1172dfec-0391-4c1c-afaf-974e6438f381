<script lang="ts">
/**
 * 固定列和表头的滚动表格
 * props:
 * records: 数据列表，暂时只兼容 lesson 数据
 * columns: 数据列
 * rowHeadWith: 行头单元格宽度
 * minColWidth: 单元格最小宽度，设置后，若容器宽度减少，可触发横向滚动
 *
 * slots:
    template(#placeholder)                                   // 左上角行列交点单元格
    template(#colHead="{ data, colIndex }")                  // 列头单元格
    template(#rowHead="{ data, rowIndex }")                  // 行头单元格
    template(#grid-cell="{ row, col, rowIndex, colIndex }")       // 数据单元格
    template(#default="{ data, row, index }")                // 定位的课程元素，一个单元格可占用多个列

   events:
    loadMore: 滚动多底部事件
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { debounce } from 'lodash';
import columns, { ITimeSegment } from './scheduleData';
import { IScrollTableRecord, IScrollTableRecordItem } from './scrollTablle.interface';

@Component({
  components: {},
})
export default class ScrollTable extends Vue {
  rowHeadsHeight: number[] = [];
  showColShadow: boolean = false;
  showRowShadow: boolean = false;
  mainTableStyle: object = {};
  debounceResize: () => void = () => {};
  debounceLoadMore: () => void = () => {};

  @Prop({ type: Array, default: () => [], required: true }) records!: IScrollTableRecord[];
  @Prop({ type: Array, default: () => [], required: true }) columns!: ITimeSegment[];
  @Prop({ type: Number, default: 48 }) rowHeadWidth!: number;
  @Prop({ type: Number, default: 64 }) minColWidth!: number;
  @Prop({ type: String, default: '暂无内容' }) placeholder!: String;
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: true }) finish!: boolean;

  get headColStyle() {
    return {
      minWidth: `${this.minColWidth}px`,
    };
  }

  created() {
    this.debounceResize = debounce(this.syncTableSize, 500);
    this.debounceLoadMore = debounce(this.onLoadMore, 1000);
  }
  mounted() {
    this.$nextTick(() => {
      this.syncTableSize();
    });
    window.addEventListener('resize', this.debounceResize);
  }
  beforeDestroy() {
    window.removeEventListener('resize', this.debounceResize);
  }
  syncTableSize() {
    (this.$refs.fixedTable as HTMLElement).style.width = `${(this.$refs.mainTable as HTMLElement).clientWidth}px`;
  }
  onFixedBodyScroll(e: any) {
    (this.$refs.body as any).scrollTop = e.target.scrollTop;
  }
  onBodyScroll(e: any) {
    (this.$refs.fixedBody as any).scrollTop = e.target.scrollTop;
    this.showRowShadow = e.target.scrollTop > 0;
    if (e.target.offsetHeight + e.target.scrollTop + 100 >= e.target.scrollHeight && !this.loading && !this.finish) {
      this.debounceLoadMore();
    }
  }
  onXScroll(e: any) {
    const { scrollLeft } = e.target;
    this.showColShadow = scrollLeft > 0;
  }
  onLoadMore() {
    this.$emit('loadMore');
  }
  getCellStyle(items: IScrollTableRecordItem[], index: number) {
    const colCount = columns.length;

    const preItem: IScrollTableRecordItem = items[index - 1];
    const nowItem: IScrollTableRecordItem = items[index];
    const nextItem: IScrollTableRecordItem = items[index + 1];

    const startIndex = (nowItem.start_unit || 1) - 1;
    const end = nowItem.end_unit || 1;
    const count = end - startIndex;

    let marginLeft = '';
    if (preItem && nowItem.start_unit! > preItem.end_unit!) {
      marginLeft = `${((nowItem.start_unit! - (preItem.end_unit! + 1)) / colCount) * 100}%`;
    } else {
      marginLeft = `${(startIndex / colCount) * 100}%`;
    }
    // 防止重叠课程不换行的问题
    let marginRight = '';
    if (nextItem && nowItem.start_unit <= nextItem.start_unit && nowItem.end_unit >= nextItem.start_unit) {
      marginRight = `${((colCount - startIndex - count) / colCount) * 100}%`;
    }
    return {
      width: `${(count / colCount) * 100}%`,
      marginLeft,
      marginRight,
    };
  }
}
</script>

<template lang="pug">
.scroll-table(ref="rootContainer" v-loading="loading")
  //- empty
  .scroll-table__placeholder(v-if="records.length === 0")
    slot(name="placeholder")
      .empty {{ placeholder }}
  //- 固定表头列
  .fixed-table(:style="{ width: `${rowHeadWidth}px` }" :class="{ 'col-shadow': showColShadow }")
    .schedule-table-wrapper
      .schedule-table(ref="fixedTable")
        .row.header(:class="{ 'row-shadow': showRowShadow }")
          .row-head(:style="{ width: `${rowHeadWidth}px` }")
            slot(name="placeholder")
          .row-col(v-for="(column, colIndex) in columns" :key="colIndex" :style="headColStyle")
            slot(name="colHead" :data="column" :colIndex="colIndex")
              span {{ colIndex }}
        .body(ref="fixedBody" @scroll="onFixedBodyScroll")
          .row(v-for="(row, rowIndex) in records" :key="row.key" ref="row")
            .cell-wrapper(:style="{ paddingLeft: `${rowHeadWidth}px` }")
              .cell-head(:style="{ width: `${rowHeadWidth}px`, marginLeft: `-${rowHeadWidth}px` }")
                slot(name="rowHead" :data="row.head" :rowIndex="rowIndex")
              .default-cell.hidden(
                v-for="(item, itemIndex) in row.items"
                :key="item.id"
                :style="getCellStyle(row.items, itemIndex)")
                slot(:data="item" :row="row" :index="itemIndex")
  //- 表格内容
  .schedule-table-wrapper(@scroll="onXScroll" ref="mainTableContainer")
    .schedule-table(ref="mainTable")
      .row.header(:class="{ 'row-shadow': showRowShadow }")
        .row-head(:style="{ width: `${rowHeadWidth}px` }")
          slot(name="placeholder")
        .row-col(v-for="(column, colIndex) in columns" :key="colIndex" :style="headColStyle")
          slot(name="colHead" :data="column" :colIndex="colIndex")
            span {{ colIndex }}
      .body(ref="body" @scroll="onBodyScroll" v-if="records.length > 0")
        .row(v-for="(row, rowIndex) in records" :key="row.key" ref="row")
          //- 网格线
          .grid-wrapper
            .row-head(:style="{ width: `${rowHeadWidth}px` }")
            .row-col(v-for="(column, colIndex) in columns" :key="colIndex")
              slot(name="grid-cell" :row="row" :col="column" :rowIndex="rowIndex" :colIndex="colIndex")
          //- 定位元素
          .cell-wrapper(:style="{ paddingLeft: `${rowHeadWidth}px` }")
            .cell-head.hidden(:style="{ width: `${rowHeadWidth}px`, marginLeft: `-${rowHeadWidth}px` }")
              slot(name="rowHead" :data="row.head" :rowIndex="rowIndex")
            .default-cell(
              v-for="(item, itemIndex) in row.items"
              :key="item.id"
              :style="getCellStyle(row.items, itemIndex)")
              slot(:data="item" :row="row" :index="itemIndex")
</template>

<style lang="stylus" scoped>
.scroll-table
  position relative
  overflow-y hidden
  height 100%
  background #fff
  .scroll-table__placeholder
    position absolute
    top 48px
    right 0
    bottom 0
    min-height 200px
    .empty
      display flex
      justify-content center
      align-items center
      min-height 200px
      width 100%
      height 100%
      color #999
  .hidden
    visibility hidden
  .row-shadow
    box-shadow 0 6px 6px -4px rgba(0, 0, 0, 0.15)
  .col-shadow
    box-shadow 6px 0 6px -4px rgba(0, 0, 0, 0.15)
  .fixed-table
    position absolute
    top 0
    bottom 0
    left 0
    z-index 10
    overflow hidden
    background #FAFAFA
    .schedule-table-wrapper
      border-right none
      overflow hidden
  .schedule-table-wrapper
    overflow hidden
    overflow-x auto
    box-sizing border-box
    height 100%
    zoom 1
    -webkit-overflow-scrolling touch
    .schedule-table
      position relative
      display flex
      flex-direction column
      min-width fit-content
      width 100%
      height 100%
      .header
        flex-shrink 0
        align-items stretch
        min-width fit-content
        width 100%
      .body
        position relative
        flex 1
        overflow-x hidden
        overflow-y auto
        height 100%
        -webkit-overflow-scrolling touch
      .row
        position relative
        display flex
        .row-col
          flex 1
        .row-head
          flex-shrink 0
          border-bottom 1px solid #e8e8e8
        .grid-wrapper
          position absolute
          top 0
          right 0
          bottom 0
          display flex
          width 100%
        .cell-wrapper
          position relative
          width 100%
          .position-table
            width 100%
            table-layout fixed
          .cell-head
            position relative
            float left
            overflow hidden
            min-height 100%
            border-bottom 1px solid #e8e8e8
          .default-cell
            position relative
            display inline-block
            float left
            vertical-align top
</style>
