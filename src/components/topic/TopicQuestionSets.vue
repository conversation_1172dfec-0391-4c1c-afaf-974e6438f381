<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { QuestionSetService, IQuestionSet } from '@/service/question_set';
import { ITopic } from '@/service/topic';
import { baseStore } from '../../store/modules/base.store';
import { AnswerSetService } from '../../service/answer_set';

@Component({
  components: {},
})
export default class TopicQuestionSets extends Vue {
  @Prop({ type: Object, default: () => ({}) }) topic!: ITopic;

  questionSets: IQuestionSet[] = [];

  get loading() {
    return baseStore.loading;
  }

  mounted() {
    this.fetchQuestionSets();
  }

  async fetchQuestionSets() {
    const { data } = await QuestionSetService.Teaching.topicQuestionSets({
      courseId: this.topic.source_id!,
      topicId: this.topic.id!,
    });
    this.questionSets = data.question_sets;
  }
  async showExam(set: IQuestionSet) {
    const { data } = await QuestionSetService.UserView.find(set.id!);
    if (data.own_answer_sets && data.own_answer_sets.id) {
      window.sessionStorage.setItem('ANSWER_SET_MODE', 'show');
      this.$router.push(`${this.topic.id}/exam/${data.own_answer_sets.id}`);
    } else {
      this.$dialog.confirm({
        title: '提示',
        message: '您确定要报名此次开始答题后，请不要刷新或退出本页面，成功提交试卷后才可算作有效答题，确定开始吗？',
        beforeClose: async (action: string, done: any) => {
          if (action === 'confirm') {
            done();
            await this.createAnswerSet(set);
          } else {
            done();
          }
        },
      });
    }
  }
  async createAnswerSet(set: IQuestionSet) {
    if (this.loading) {
      return;
    }
    window.sessionStorage.setItem('ANSWER_SET_MODE', 'edit');
    const res = await AnswerSetService.UserOwn.create({
      question_set_id: set.id,
      answerable_type: 'Topic',
      answerable_id: this.topic.id,
      state: 'todo',
    });
    this.$router.push(`${this.topic.id}/exam/${res.data.id}`);
  }
}
</script>

<template lang="pug">
.container(v-loading="loading")
  van-cell.question-set(
    v-for="set in questionSets"
    :key="set.id"
    :title="set.name"
    is-link
    @click="showExam(set)")
</template>

<style lang="stylus" scoped>
.container
  padding 12px
  min-height 100%
  background #f8f8f8
  .question-set
    padding 16px 12px
    background #fff
    margin-bottom 12px
    border-radius 4px
    font-size 14px
    color #333
</style>
