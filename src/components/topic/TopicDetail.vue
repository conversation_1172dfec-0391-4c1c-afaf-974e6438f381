<script lang="ts">
/**
 * topic
 * 论坛帖子列表 - 单项条目
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITopic } from '@/service/topic';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {},
})
export default class TopicDetail extends Vue {
  @Prop({ type: Object, default: () => ({ user: {} }) }) topic!: ITopic;

  get currentUser() {
    return sessionStore.currentUser || {};
  }
  get files() {
    return this.topic.attachments ? this.topic.attachments.files || [] : [];
  }
  get operationMap() {
    return (this.topic.operations || []).reduce(
      (power: IObject, key: string) => ({
        ...power,
        [key]: true,
      }),
      {},
    );
  }

  onEdit() {
    this.$emit('edit', this.topic);
  }
  onDelete() {
    this.$dialog
      .confirm({
        title: '提示',
        message: '确定要删除此论坛吗?',
        beforeClose: (action: string, done: any) => {
          if (action === 'confirm') {
            this.$emit('delete', this.topic);
            done();
          } else {
            done();
          }
        },
      })
      .catch(() => {});
  }
}
</script>

<template lang="pug">
.topic-detail
  .title
    .title-tags
      TaTag.title-tag(size="small" color="#3DA8F5" v-if="topic.suggest")
        | 置顶
      TaTag.title-tag(size="small" color="#FF4F3E" v-if="topic.is_new")
        | 新
    .title-text
      | {{ topic.title }}
  .extra
    .author-info
      Avatar.avatar(size="20px" :name="topic.user && topic.user.name")
      span {{ topic.user && topic.user.name }}
      span(v-if="currentUser.id === topic.user && topic.user.id") (我)
      span(v-else-if="topic.user_type === 'Teacher'") (教师)
      span.time  {{ topic.created_at | format }}
    .actions
      .action(@click="onEdit" v-if="operationMap.update") 修改
      .action(@click="onDelete" v-if="operationMap.destroy") 删除
  .files(v-if="files.length")
    Attachments(:attachments="files")
  RichText.detail(:value="topic.body")
</template>

<style lang="stylus" scoped>
.topic-detail
  padding 20px
  .title
    font-size 20px
    font-weight 500
    color #383838
    line-height 0
    margin-bottom 18px
    .title-text
      margin-right 8px
      vertical-align middle
      line-height 24px
      display inline-block
    .title-tags
      padding 1px 0
      display inline-block
      vertical-align middle
      .title-tag
        vertical-align middle
        margin-right 4px
  .extra
    font-size 16px
    line-height 20px
    padding-bottom 20px
    border-bottom 1px solid #E8E8E8
    display flex
    justify-content space-between
    .actions
      .action
        color #808080
        display inline-block
        font-size 14px
        margin-left 12px
    .author-info
      color #333333
      display flex
      align-items center
      .avatar
        border-radius 50%
        background-color #58A8EF
        margin-right 6px
      .time
        color #B2B2B2
        margin-left 6px
    .statistic
      color rgba(178,178,178,1)
      .count
        margin-left 4px
      .icon
        margin-left 20px
        font-size 16px
  .files
    padding 8px 0 16px
    border-bottom 1px solid #E8E8E8
  .detail
    padding 20px 0
</style>
