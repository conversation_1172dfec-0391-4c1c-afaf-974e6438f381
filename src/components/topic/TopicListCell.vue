<script lang="ts">
/**
 * topic
 * 论坛帖子列表 - 单项条目
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITopic, TopicType } from '@/service/topic';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {},
})
export default class TopicListCell extends Vue {
  @Prop({ type: Object, default: () => ({ user: {} }) }) topic!: ITopic;
  @Prop({ type: String }) to!: string;
  @Prop({ type: Boolean }) canEdit!: boolean;

  get TopicType() {
    return TopicType;
  }
  get currentUser() {
    return sessionStore.currentUser || {};
  }
  get operationMap() {
    return (this.topic.operations || []).reduce(
      (power: IObject, key: string) => ({
        ...power,
        [key]: true,
      }),
      {},
    );
  }

  onClick(...args: any) {
    if (this.to) {
      this.$router.push(this.to);
    } else {
      this.$emit('click', ...args);
    }
  }
}
</script>

<template lang="pug">
.topic-cell(@click="onClick")
  .title
    .type
      TaTag.tag(size="small" type="warning" v-if="topic.type === TopicType.Notice")
        van-icon(size="14" name="volume")
        |  通知
      TaTag.tag(size="small" type="success" v-else-if="topic.type === TopicType.Discuss")
        van-icon(size="14" name="chat")
        |  讨论
      TaTag.tag(size="small" type="primary" v-else-if="topic.type === TopicType.Forum")
        van-icon(size="14" name="comment")
        |  论坛
      TaTag.tag(size="small" type="danger" v-else-if="topic.type === TopicType.Question")
        van-icon(size="14" name="orders-o")
        |  试卷
    .title-text {{ topic.title }}
    .title-tags
      TaTag.title-tag(size="small" color="#3DA8F5" v-if="topic.suggest")
        | 置顶
      TaTag.title-tag(size="small" color="#FF4F3E" v-if="topic.is_new")
        | 新
      TaTag.title-tag(
        size="small"
        color="#FA8C15"
        v-if="topic.comment_expire_at")
        | 评论截止{{ topic.comment_expire_at | format('M月D日 HH:mm') }}
  .author-info
    span
      span 发布者 {{ topic.user && topic.user.name }}
      span(v-if="currentUser.id === topic.user && topic.user.id") (我)
      span(v-else-if="topic.user_type === 'Teacher'") (教师)
      span &nbsp;&nbsp;|&nbsp;&nbsp;
      span(v-if="topic.comment && topic.comment.id")
        | @{{ topic.comment.user_name }} 最后回复于{{ $dayjs(topic.comment.created_at).fromNow() }}
      span(v-else) 发布于{{ $dayjs(topic.created_at).fromNow() }}
</template>

<style lang="stylus" scoped>
.topic-cell
  padding 20px
  border-bottom 1px solid rgba(0,0,0,0.1)
  cursor pointer
  background #fff
  &:last-child
    border-bottom none
  .title
    flex-grow 1
    font-size 15px
    font-weight 500
    color #333333
    padding-right 16px
    line-height 0
    .type
      line-height 0
      display inline-block
      .tag
        margin-right 6px
    .title-text
      margin-right 8px
      vertical-align middle
      line-height 22px
      display inline-block
    .title-tags
      padding 1px 0
      display inline-block
      vertical-align middle
      .title-tag
        vertical-align middle
        margin-right 4px
  .author-info
    color #B2B2B2
    font-size 13px
    margin-top 4px
</style>
