<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import CommentSender from '@/components/comment/CommentSender.vue';
import CommentMessages from '@/components/comment/Messages.vue';
import { IComment, CommentService } from '@/service/comment';
import { TopicType, ITopic } from '@/service/topic';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    CommentSender,
    CommentMessages,
  },
})
export default class TopicDiscussions extends Vue {
  @Prop({ type: Number }) courseId!: number;
  @Prop({ type: Object }) topic!: ITopic;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  timer: any = null;
  comments: IComment[] = [];
  perPage: number = 20;
  pushLoading: boolean = false;
  // get old message
  unshiftMessageCount: number = 0;
  unshiftLoading: boolean = false;
  hasAllOldestMessages: boolean = false;
  // reply
  parentComment: IComment = {};
  // setting
  hasNewMessage: boolean = false;
  sending: boolean = false;
  // expired
  isExpired: boolean = false; // 是否过期
  expiredTimer: any = null;
  // filter
  authorType: string = '';

  get lattestMessage() {
    return this.comments[this.comments.length - 1] || {};
  }
  get oldestMessage() {
    return this.comments[0] || {};
  }
  get role() {
    return sessionStore.role;
  }
  get canEdit() {
    if (this.role === 'student') {
      return this.topic.can_reply && !this.isExpired;
    }
    // 教师端，不受 评论到期时间 影响
    return this.topic.teach_permit === 'teaching';
  }

  @Watch('topic.id', { immediate: true })
  async onTargetChange() {
    await this.fetchLastestMessages();
    setTimeout(() => {
      this.scrollToBottom();
    }, 60);
  }

  mounted() {
    this.timer = setInterval(this.fetchLastestMessages, 30000);
    this.expiredTimer = setInterval(this.onExpiredTickChange, 10000);
    this.onExpiredTickChange();
  }
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.expiredTimer);
  }
  fetchComments(query: object = {}) {
    if (!this.topic.id || !this.courseId) {
      return Promise.reject();
    }
    return CommentService.teaching.fetchTopicComments({
      courseId: this.courseId,
      topicId: this.topic.id,
      topicType: this.topic.type!,
      params: {
        page: 1,
        per_page: this.perPage,
        q: query,
      },
    });
  }
  onExpiredTickChange() {
    this.isExpired = this.topic.comment_expire_at ? new Date(this.topic.comment_expire_at) < new Date() : false;
    if (this.isExpired) {
      clearInterval(this.timer);
    }
  }
  async fetchLastestMessages() {
    try {
      this.pushLoading = true;
      const { data } = await this.fetchComments({
        id_gt: this.lattestMessage.id,
        user_type_eq: this.authorType,
      });
      if (data.comments.length) {
        this.hasNewMessage = !!this.comments.length;
        this.comments.push(...data.comments.reverse());
      }
      this.pushLoading = false;
    } catch (error) {
      this.pushLoading = false;
    }
  }
  async fetchOldestMessages() {
    try {
      this.unshiftLoading = true;
      const { data } = await this.fetchComments({
        id_lt: this.oldestMessage.id,
        user_type_eq: this.authorType,
      });
      this.comments.unshift(...data.comments.reverse());
      this.hasAllOldestMessages = data.total_pages <= 1;
      this.unshiftMessageCount = data.comments.length;
      if (this.unshiftMessageCount) {
        setTimeout(() => {
          this.unshiftMessageCount = 0;
        }, 4000);
      }
      this.unshiftLoading = false;
    } catch (error) {
      this.unshiftLoading = false;
    }
  }
  beforeSend() {
    this.sending = true;
  }
  sendMessage(comment: IComment) {
    this.comments.push(comment);
    this.scrollToBottom();
    this.sending = false;
    this.parentComment = {};
  }
  reply(comment: IComment) {
    this.parentComment = comment;
    this.senderFocus();
  }
  cancelReply() {
    this.parentComment = {};
  }
  senderFocus() {
    this.$nextTick(() => {
      if (this.$refs.sender) {
        (this.$refs.sender as any).focus();
      }
    });
  }
  onTopPosition() {
    if (this.unshiftLoading || this.hasAllOldestMessages) return;
    this.fetchOldestMessages();
  }
  onBottomPosition() {
    this.hasNewMessage = false;
  }
  scrollToBottom() {
    this.$nextTick(() => {
      this.hasNewMessage = false;
      if (this.$refs.messageList) {
        (this.$refs.messageList as any).scrollToBottom();
      }
    });
  }
}
</script>

<template lang="pug">
.messages-panel
  TaTag.expired-tips(type="warning" size="small" v-if="isExpired")
    | 讨论已截止
  .message-wrapper(id="messageWrapper")
    .header
      TaTag.old-message-tips(type="primary" v-if="unshiftMessageCount")
        | 已加载 {{ unshiftMessageCount }} 条消息
    CommentMessages(
      ref="messageList"
      :comments="comments"
      :topLoading="unshiftLoading"
      :readonly="!canEdit"
      @top="onTopPosition"
      @bottom="onBottomPosition"
      @reply="reply")
    .footer
      TaTag.new-message-tips(type="primary" @click="scrollToBottom" v-if="hasNewMessage")
        | 收到新消息
      .reply-comment(v-if="parentComment.id")
        van-icon.close(name="cross" @click="cancelReply")
        small {{ parentComment.user_name }}
        div.text-pre(v-if="parentComment.body") {{ parentComment.body }}
        div(v-if="parentComment.files && parentComment.files.length")
          | {{ parentComment.files.length }} 个附件
  .message-sender(v-if="canEdit")
    CommentSender(
      ref="sender"
      :useCdn="true"
      :parentId="parentComment.id"
      :commentableId="topic.id"
      commentableType="Topic"
      commentsContainerId="messageWrapper"
      size="large"
      @beforeSend="beforeSend"
      @add="sendMessage")
</template>

<style lang="stylus" scoped>
.messages-panel
  position relative
  display flex
  flex-direction column
  width 100%
  height 100%
  background #FAFAFA
  .expired-tips
    width 100%
  .message-wrapper
    overflow auto
    height 100%
    position relative
    .header
      position absolute
      top 0
      left 0
      width 100%
      z-index 1000
      overflow hidden
      .old-message-tips
        margin 10px auto 0
        display block
        width fit-content
    .footer
      position absolute
      bottom 0
      left 0
      width 100%
      z-index 1000
      overflow hidden
      .new-message-tips
        margin 0 auto 10px
        display block
        width fit-content
      .reply-comment
        padding 10px 12px
        color #999
        background #fff
        border-top 1px solid #eee
        position relative
        .close
          position absolute
          top 8px
          right 12px
          cursor pointer
          z-index 1000
        .text-pre
          white-space pre-wrap
  .message-sender
    flex-shrink 0
    border-top 1px solid #E8E8E8
    background #ffffff
</style>
