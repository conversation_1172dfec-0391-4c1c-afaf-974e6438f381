<script lang="ts">
/**
 * 表格记录编辑器
 */

import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { sum } from 'lodash';
import TemplateFormPopup from './TemplateFormPopup.vue';
import ScrollTable from '../teaching/ScrollTable.vue';

interface IAttr {
  key: string;
  name: string;
  value?: string;
  show?: boolean;
  layout?: {
    component: string;
    options: IObject[];
  };
}
interface IRecord {
  id?: number;
  index?: number;
  [key: string]: string | number | undefined;
}

@Component({
  components: {
    TemplateFormPopup,
    ScrollTable,
  },
})
export default class TableField extends Vue {
  @Model('change', { type: Array, default: () => [] }) value!: IRecord[];
  @Prop({ type: Array, default: () => [] }) attrs!: IAttr[]; // 列结构
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: String }) label!: string;

  // records
  current: number = 1;
  pageSize: number = 10;
  // form
  visible: boolean = false;
  formData: IRecord = { id: 0 };

  get isValidValue() {
    return this.value instanceof Array;
  }

  get records() {
    return (this.value || []).map((o, i) => ({
      ...o,
      index: i + 1,
    }));
  }

  get showAttrs() {
    return (this.attrs || []).filter(o => o.show);
  }

  get template() {
    return this.showAttrs.map(({ name, key, value, layout }: IAttr) => ({
      key,
      name,
      accessibility: value ? 'readonly' : 'read_and_write',
      layout: {
        component: (layout && layout.component) || 'input',
        placeholder: `请输入${name}`,
        span: 24,
        required: true,
        options: layout && layout.options,
      },
      model: {
        attr_type: 'string',
      },
    }));
  }

  get defaultObject() {
    return this.attrs.reduce((obj: IObject, o) => {
      if (o.value || !o.show) {
        obj[o.key] = o.value;
      }
      return obj;
    }, {});
  }

  get columns() {
    return this.showAttrs.map((attr: IAttr) => {
      const headerWidth = attr.name.trim().length * 16;
      return {
        title: attr.name,
        dataIndex: attr.key,
        key: attr.key,
        width: headerWidth > 80 ? headerWidth : 80,
      };
    });
  }

  onNew() {
    this.formData = { ...this.defaultObject };
    this.visible = true;
  }

  onEdit(record: IRecord) {
    this.formData = { ...record };
    this.visible = true;
  }

  onDelete(record: IRecord) {
    const records = this.isValidValue ? this.value.concat() : [];
    const index = records.findIndex(o => o.id === record.id);
    records.splice(index, 1);
    this.$emit('change', records);
  }

  update(record: IRecord) {
    const records = this.isValidValue ? this.value.concat() : [];
    const index = records.findIndex(o => o.id === record.id);
    records.splice(index, 1, Object.assign(records[index], { ...this.defaultObject, ...record }));
    this.$emit('change', records);
    this.visible = false;
  }

  create(record: IRecord) {
    const records = this.isValidValue ? this.value.concat() : [];
    records.push({ ...this.defaultObject, ...record, id: Date.now() });
    this.$emit('change', records);
    this.visible = false;
  }
}
</script>

<template lang="pug">
.table-filed
  .table-filed__header
    .label {{ label }}
    van-button(
      v-if="!disabled"
      type="primary"
      icon="plus"
      @click="onNew"
    )
      | 添加记录
  .content(:style='`width: ${disabled ? "90vw" : "100vw"}`')
    vxe-table(
      :data='records',
      align='center',
      :sync-resize='true'
      :scroll-x='{ enable: true }'
    )
      vxe-table-column(field='index' title='序号' fixed='left' :width='50')
      vxe-table-column(v-for='column in columns' :field='column.key' :title='column.title', :width='column.width')
      vxe-table-column(fixed='right' :width='100')
        template(#default='{ row }')
          .actions
            .action
              van-button(@click='onEdit(row)' icon="edit" size='small')
            van-button(@click='onDelete(row)' icon="delete" size='small')
  TemplateFormPopup(
    v-model="visible"
    :title="formData.id ? '编辑记录' : '新建记录'"
    :formData="formData"
    :template="template"
    :loading="false"
    @update="update"
    @create="create")
</template>

<style lang="stylus" scoped>
.table-filed
  max-width 100%
  width 100%
  .table-filed__header
    margin-bottom 8px
    display flex
    justify-content space-between
    .label
      display flex
      padding-left 10px
      align-items center
  .content
    margin 0 auto

.actions
  display flex
  .action
    margin-right 10px
</style>
