<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITeacher } from '../../models/teaching/teacher';

@Component
export default class TaStoreFieldCell extends Vue {
  @Prop({ type: Object, default: () => ({}) }) record!: IObject;
  @Prop({ type: [<PERSON>olean, String], default: 'isLink' }) isLink!: string;
  @Prop({ type: Boolean, default: false }) checked!: boolean;

  onClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<template lang="pug">
van-cell(
  :title="record.name"
  :is-link="!!this.isLink"
  @click="onClick"
)
  .record-avatar.checked-icon(v-if="checked" slot="icon")
    van-icon(name="success")
  Avatar.record-avatar(
    v-else
    :name="record.name"
    slot="icon"
    size="38px"
  )
  template(slot="label")

</template>

<style lang="stylus" scoped>
.record-avatar
  margin-right 10px
  width 38px
  height 38px
  border-radius 50%

.checked-icon
  padding 7px
  background #50AC35
  color #fff
  text-align center
  font-size 24px
</style>
