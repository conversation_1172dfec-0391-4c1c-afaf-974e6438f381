<script lang="ts">
import { IModel, IModelConfig } from '@/lib/ActiveModel';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import TaStoreField from './TaStoreField.vue';
import { hrTeacherTeacherStore } from '../../store/modules/hr/teacher/teacher.store';

@Component({
  components: {
    TaStoreField,
  },
})
export default class TeacherField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;
  @Prop(String) label!: string;
  @Prop(String) placeholder!: string;
  @Prop(String) errorMessage!: string;
  @Prop(Boolean) required!: boolean;

  get store() {
    return hrTeacherTeacherStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: {} },
      },
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.duty-field
  TaStoreField(
    v-model='localValue',
    :label='label',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
    :placeholder='placeholder'
    :errorMessage='errorMessage'
    :required='required'
  )
</template>

<style lang="stylus" scoped>
.department-field
  width 400px
</style>
