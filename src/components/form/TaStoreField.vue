<script lang="ts">
import ActiveModel, { IModel } from '@/lib/ActiveModel';
import { ActiveStore } from '@/lib/ActiveStore';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import TaStoreFieldPopup from './TaStoreFieldPopup.vue';
import TaStoreFieldCell from './TaStoreFieldCell.vue';
@Component({
  components: {
    TaStoreFieldPopup,
    TaStoreFieldCell,
  },
})
export default class StoreField<T extends IModel> extends Vue {
  @Model('change') value!: number[];

  @Prop(String) readonly label!: number;
  @Prop(String) readonly placeholder!: string;
  @Prop(String) readonly errorMessage!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(String) readonly leftIcon!: string;
  @Prop({ type: String, default: 'arrow' }) rightIcon!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;
  @Prop({ type: Object, required: true }) store!: ActiveStore<T>;
  @Prop({ type: Object, default: () => ({}) }) storeConfig!: IObject;

  selectedRecordsRaw: T[] = [];
  visible = false;
  oldValue: number[] = [];

  get selectedRecordIds() {
    return this.selectedRecords.map(i => i.id);
  }

  get selectedRecords() {
    return this.selectedRecordsRaw;
  }

  set selectedRecords(val) {
    this.selectedRecordsRaw = val;
    this.$emit('selectedRecordsChange', val);
  }

  // get config() {
  //   return {
  //     recordName: this.recordName,
  //     store: this.store,
  //     showSelectionByDefault: true,
  //     mode: 'table',
  //     searcherSimpleOptions: this.searcherOptions,
  //     searcherComplicatedOptions: this.searcherOptions,
  //     tableConfig: {
  //       scroll: { y: '60vh' },
  //     },
  //   };
  // }

  get initParams() {
    return this.storeConfig.initParams || {};
  }

  get tagKey() {
    return this.storeConfig.tagKey || 'name';
  }

  get tableColumns() {
    return this.storeConfig.tableColumns || [];
  }

  get searcherOptions() {
    return this.tableColumns
      ? this.tableColumns
          .filter((column: IObject) => column.search)
          .map(
            (column: IObject) =>
              ({
                label: column.title,
                key: column.searchKey || column.dataIndex,
                type: column.type || 'string',
              } || []),
          )
      : [];
  }

  @Watch('value', { immediate: true })
  onValueChange() {
    if (JSON.stringify(this.value) === JSON.stringify(this.oldValue)) {
      return;
    }
    if (this.value && Object.getPrototypeOf(this.store.model).constructor !== ActiveModel) {
      const ids = this.value.filter(i => i);
      this.fetchExistRecords(ids);
      this.oldValue = ids;
    }
  }

  @Watch('visible')
  handleVisibleChange() {
    if (this.visible) {
      this.store.reset();
      this.$emit('openModal');
    }
  }

  @Watch('initParams')
  handleInitParamsChange() {
    this.store.init(this.initParams);
  }

  mounted() {
    this.store.reset();
    this.store.init(this.initParams);
    this.onValueChange();
  }

  async fetchExistRecords(ids: (string | number)[]) {
    if (JSON.stringify(ids.sort()) === JSON.stringify(this.selectedRecords.map(i => i.id).sort())) return;
    if (ids.length === 0) {
      this.selectedRecords = [];
    } else {
      const { data } = await this.store.model.index({ per_page: ids.length, q: { id_in: ids } });
      this.selectedRecords = data[this.store.model.dataIndexKey];
    }
  }

  removeRecord(index: number) {
    if (this.disabled) {
      return;
    }
    this.selectedRecords.splice(index, 1);
    this.syncValue();
  }

  syncValue() {
    // 单选多选都返回数组
    this.$emit('change', this.selectedRecordIds);
  }

  openSelector() {
    if (!this.disabled) {
      this.visible = true;
      this.store.init(this.initParams);
      this.$emit('open');
    }
  }

  // handleOk() {
  //   this.visible = false;
  //   this.syncValue();
  //   this.$emit('ok', this.selectedRecords);
  // }

  handleCancel() {
    this.visible = false;
    this.onValueChange();
    this.$emit('cancel');
  }

  onSelect(newValue: T[]) {
    // 单选
    // if (!this.multiple) {
    //   this.selectedRecords = newValue.filter(item => !oldValue.includes(item));
    // }
    this.selectedRecords = newValue;
    this.syncValue();
  }

  clearUpRecord() {
    this.selectedRecords = [];
    this.syncValue();
  }
}
</script>

<template lang="pug">
.ta-store-field
  van-field.field(
    v-if='!disabled'
    :name="label"
    :label="label"
    :placeholder="placeholder || '点击设置'"
    :required="required"
    :readonly="true"
    :disabled="disabled"
    :error-message="errorMessage"
    @click="openSelector"
    @click-right-icon="openSelector"
    :is-link="!disabled"
    input-align="right"
    error-message-align="right"
  )
  TaStoreFieldCell(
    v-for="record in selectedRecords || []"
    :key="record.id"
    :record="record"
    :isLink="false"
  )

  TaStoreFieldPopup(
    v-model="visible"
    v-if='visible'
    :store='store'
    :multiple="multiple"
    :default="value"
    @change="onSelect"
  )
</template>

<style lang="stylus" scoped></style>
