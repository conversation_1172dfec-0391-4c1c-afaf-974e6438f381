<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import Schema from 'async-validator';
import { isEqual } from 'xe-utils';
import { cloneDeep } from 'lodash';
import { IFormItem, getRuleType } from '../../interfaces/IFormItem';
import WechatArticleField from './WechatArticleField.vue';
import TemplateFormatter from './TemplateFormatter.vue';
import TableField from './TableField.vue';
import LevelTwoCollegeField from './LevelTwoCollegeField.vue';
import TeacherField from './TeacherField.vue';

interface submitOptions {
  success?: (formData: IObject) => void;
  fail?: (errors: IObject, formData?: IObject) => void;
}

@Component({
  components: {
    WechatArticleField,
    TemplateFormatter,
    TableField,
    LevelTwoCollegeField,
    TeacherField,
  },
})
export default class TemplateForm extends Vue {
  @Prop({ type: Object, default: () => ({}) }) formData!: IObject;
  @Prop({ type: Array, default: () => [], required: true }) template!: IFormItem[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) loading!: boolean;
  @Prop({ type: Boolean, default: true }) showActions!: boolean;

  payload: IObject = {};
  errors: IObject = {};
  modelTypeMap: IObject = {
    string: 'string',
    number: 'number',
    array: 'array',
    json: 'object',
  };
  timer: any = null;
  fileSettledMap: IObject = {};
  alignMap: IObject = {
    left: 'flex-start',
    center: 'center',
    right: 'flex-end',
  };

  formattedTemplate: IFormItem[] = [];

  get rules() {
    return this.template.reduce((rules: IObject, item: IFormItem, index: number) => {
      const required =
        (['label', 'if_container', 'container_end'].includes(item.layout.component) ||
        this.templateFormatter.indexShouldHide.includes(index)
          ? false
          : item.layout.required) &&
        (item.transitionAccessibility === undefined ? true : item.transitionAccessibility === 'read_and_write');
      rules[item.key] = {
        required,
        message: `请设置${item.name}`,
        type: getRuleType(item),
      };
      return rules;
    }, {});
  }
  get submitDisabled() {
    return this.template.every(o => this.isDisabled(o));
  }

  get templateFormatter() {
    return this.$refs.templateFormatter as TemplateFormatter;
  }

  mounted() {
    this.initPayload();
  }

  @Watch('formData', { deep: true, immediate: true })
  onFormDataChange() {
    this.initPayload();
  }

  get clonePayload() {
    return cloneDeep(this.payload);
  }

  @Watch('clonePayload', { deep: true })
  onPayloadChange(val1: IObject, val2: IObject) {
    if (!isEqual(val1, val2)) {
      this.payload = this.templateFormatter.clearHideFormValue(this.payload);
    }
  }

  initPayload() {
    const { formData, formattedTemplate } = this;
    this.payload = formData;
    // formattedTemplate.forEach((item: IObject) => {
    //   if (!this.payload[item.key]) {
    //     this.payload[item.key] = item.layout.defaultValue;
    //   }
    // });
  }
  getFormData() {
    return this.payload;
  }
  preSubmit(e: any) {
    e.preventDefault();
    this.submit();
  }
  submit(options: submitOptions = {}) {
    if (Object.values(this.fileSettledMap).every(Boolean)) {
      const validator = new Schema(this.rules);
      validator.validate(this.payload, {}, errors => {
        if (errors) {
          this.errors = errors.reduce((obj: IObject, o: any) => {
            obj[o.field] = o.message;
            return obj;
          }, {});
          if (options.fail) {
            options.fail(this.errors, this.payload);
          }
        } else {
          this.$emit('submit', this.payload);
          if (options.success) {
            options.success(this.payload);
          }
        }
      });
    } else {
      this.$message.warning('有「正在上传」或者「上传失败」的文件，请处理完成后提交');
    }
  }
  onFileUploaderStatusChanged(item: IFormItem, isAllSuccessed: boolean) {
    this.$set(this.fileSettledMap, item.key || 'normal', isAllSuccessed);
  }
  cancel(e: any) {
    e.preventDefault();
    this.$emit('cancel');
  }
  isDisabled(item: IObject) {
    const disabled = item.transitionAccessibility
      ? item.transitionAccessibility !== 'read_and_write' || this.disabled
      : false;
    return this.disabled || item.layout.notEditable;
  }
  isVisibility(item: IObject) {
    return item.transitionAccessibility !== 'hidden';
  }
  getOptions(item: IObject) {
    return (item.layout.options || []).map((o: any) => ({
      label: o.label,
      value: o.value || o.label,
    }));
  }
  onFormInput(e: any) {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      const validator = new Schema(this.rules);
      validator.validate(this.payload, {}, errors => {
        if (errors) {
          const error = errors.find((o: any) => o.field === e.target.name);
          if (error) {
            this.$set(this, 'errors', { ...this.errors, [error.field]: error.message });
          } else {
            this.$delete(this.errors, e.target.name);
          }
        }
      });
    }, 1000);
  }

  getLabelStyle(item: IObject) {
    const span = item.layout.rowspan || 1;
    const height = `${(span - 1) * 16 + span * 70}px`;
    return {
      height,
      padding: `${10}px`,
      justifyContent: this.alignMap[item.layout.textAlign || 'left'],
    };
  }
}
</script>

<template lang="pug">
form.template-form(@submit='preSubmit', ref='form', @input.stop='onFormInput')
  TemplateFormatter(
    ref='templateFormatter',
    v-model='formattedTemplate'
    :payload='payload'
    :template='template'
  )
  .form-item(v-for='(item, index) in formattedTemplate', :key='`${item.key}_${item.layout.component}`')
    .field(v-if='item.layout.component === "label"')
      .form-widget-label(:style='getLabelStyle(item)')
        | {{ item.name }}
    van-cell-group(v-else-if='isVisibility(item)', :class='{ "form-item-disabled": isDisabled(item) }')
      van-field.field(
        v-if='item.layout.component === "input" && item.model.attr_type !== "number"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :type='item.model.attr_type',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :placeholder='item.layout.placeholder',
        :disabled='isDisabled(item)',
        input-align='right',
        error-message-align='right'
      )
      van-field.field(
        v-else-if='item.layout.component === "input" && item.model.attr_type === "number"',
        :name='item.key',
        :label='item.name',
        v-model.number='payload[item.key]',
        :type='item.model.attr_type',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :placeholder='item.layout.placeholder',
        :disabled='isDisabled(item)',
        input-align='right',
        error-message-align='right'
      )
      van-field.field(
        v-else-if='item.layout.component === "currency"',
        :name='item.key',
        :label='item.name',
        v-model.number='payload[item.key]',
        :type='item.model.attr_type',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :placeholder='item.layout.placeholder',
        :disabled='isDisabled(item)',
        input-align='right',
        error-message-align='right'
      )
      select-field.field(
        v-else-if='item.layout.component === "select"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :options='getOptions(item)',
        :required='rules[item.key].required',
        :placeholder='item.layout.placeholder',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      checkbox-field.field(
        v-else-if='item.layout.component === "checkbox"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :options='getOptions(item)',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      radio-field.field(
        v-else-if='item.layout.component === "radio"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :options='getOptions(item)',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      van-field.field(
        v-else-if='item.layout.component === "switch"',
        :label='item.name',
        :name='item.key',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)',
        center
      )
        .field-slot(slot='button')
          van-switch(size='20px', v-model='payload[item.key]')
      van-field.textarea-field(
        v-else-if='item.layout.component === "textarea"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :placeholder='item.layout.placeholder',
        :disabled='isDisabled(item)',
        type='textarea',
        rows='6',
        autosize
      )
      date-time-field.field(
        v-else-if='item.layout.component === "date"',
        type='date',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      date-time-field.field(
        v-else-if='item.layout.component === "datetime"',
        type='datetime',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      date-time-field.field(
        v-else-if='item.layout.component === "time"',
        type='time',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      FileUploader.field(
        v-else-if='item.layout.component === "file"',
        v-model='payload[item.key]',
        :name='item.key',
        :label='item.name',
        :disabled='isDisabled(item)',
        :required='rules[item.key].required',
        :multiple='item.layout.multiple',
        :error-message='errors[item.key]',
        @statusChange='onFileUploaderStatusChanged(item, $event)'
      )
      FileUploader.field(
        v-else-if='item.layout.component === "image"',
        v-model='payload[item.key]',
        :name='item.key',
        :label='item.name',
        :disabled='isDisabled(item)',
        :required='rules[item.key].required',
        :multiple='item.layout.multiple',
        :error-message='errors[item.key]',
        accept='image/*',
        @statusChange='onFileUploaderStatusChanged(item, $event)'
      )
      ContactField.field(
        v-else-if='item.layout.component === "contacts"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :multiple='item.layout.multiple',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)'
      )
      WechatArticleField.field(
        v-else-if='item.layout.component === "wechat_articles"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)',
        :wechat='item.layout.wechat || {}'
      )
      TableField.field(
        v-else-if='item.layout.component === "table"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)',
        :attrs='item.layout.attrs || []'
      )
      LevelTwoCollegeField.field(
        v-else-if='item.layout.component === "level_two_college"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)',
      )
      TeacherField.field(
        v-else-if='item.layout.component === "teacher"',
        :name='item.key',
        :label='item.name',
        v-model='payload[item.key]',
        :required='rules[item.key].required',
        :error-message='errors[item.key]',
        :disabled='isDisabled(item)',
      )
      .field.rich-text-item(
        v-else-if='item.layout.component === "rich_text"',
      )
        van-field.field(
          :name='item.key',
          :label='item.name',
          :required='rules[item.key].required',
          :error-message='errors[item.key]',
          :disabled='isDisabled(item)',
        )
        div(
          v-html='payload[item.key] || item.layout.defaultValue',
        )
  .actions(v-if='showActions')
    slot(name='actions')
      van-button(type='info', native-type='submit', :disabled='submitDisabled', :loading='loading')
        | 确定
      van-button(@click='cancel')
        | 取消
</template>
<style lang="stylus">
.van-field--disabled .van-field__label
  color black

.van-field__control:disabled
  color rgba(0, 0, 0, 0.65)

.rich-text-item
  .van-cell__title
    width 100%
</style>
<style lang="stylus" scoped>
.template-form
  font-size 16px
  padding-bottom 30px
  .field-slot
    line-height 1
  .form-item
    background #fff
    .field
      border-bottom 1px solild #ddd
      background transparent
      .form-widget-label
        display flex
        align-items center
        overflow hidden
        width 100%
        background #f4f4f4
        white-space pre-wrap
        font-weight 500
        font-size 16px
    .textarea-field
      flex-direction column
  .form-item-disabled
    background #f9f9f9
    opacity 0.6
    cursor not-allowed
    pointer-events none
  .actions
    padding 20px 15px 10px
    width 100%
    .van-button
      margin-bottom 10px
      width 100%
      border-radius 5px
      font-size 18px
      &:last-child
        margin-bottom 0px
</style>
