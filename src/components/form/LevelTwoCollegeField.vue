<script lang="ts">
import { IModel, IModelConfig } from '@/lib/ActiveModel';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import TaStoreField from './TaStoreField.vue';
import { resTeacherDepartmentStore } from '../../store/modules/res/teacher/department.store';

@Component({
  components: {
    TaStoreField,
  },
})
export default class LevelTwoCollegeField extends Vue {
  @Model('change') value!: number | number[];

  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;
  @Prop(String) label!: string;
  @Prop(String) placeholder!: string;
  @Prop(String) errorMessage!: string;
  @Prop(Boolean) required!: boolean;

  get store() {
    return resTeacherDepartmentStore;
  }

  get config() {
    return {
      initParams: {
        params: { q: { depth_eq: 1, type: 'Department::College' } },
      },
      // tableColumns: [
      //   { title: '名称', dataIndex: 'name', type: 'string', search: true },
      //   { title: '代号', dataIndex: 'code', type: 'string', search: true },
      //   { title: '缩写', dataIndex: 'short_name', type: 'string', search: true },
      // ],
    };
  }

  get localValue() {
    return this.value;
  }

  set localValue(val: number | number[]) {
    this.$emit('change', val);
  }
}
</script>

<template lang="pug">
.duty-field
  TaStoreField(
    v-model='localValue',
    :label='label',
    :store='store',
    :storeConfig='config',
    :disabled='disabled',
    :multiple='multiple',
    :placeholder='placeholder'
    :errorMessage='errorMessage'
    :required='required'
  )
</template>

<style lang="stylus" scoped>
.department-field
  width 400px
</style>
