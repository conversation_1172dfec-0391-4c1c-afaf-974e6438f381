<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { INewsItem } from '../../models/wechat/article';

@Component({
  components: {},
})
export default class WechatNewsItemEditor extends Vue {
  @Model('input', { type: Boolean }) readonly value!: boolean;
  @Prop({ type: Object, default: () => ({}) }) readonly newsItem!: INewsItem;
  @Prop({ type: Boolean, default: false }) readonly loading!: boolean;
  @Prop({ type: Boolean, default: false }) readonly disabled!: boolean;

  get visible() {
    return this.value;
  }
  set visible(v: boolean) {
    this.$emit('input', v);
  }

  updateArticle() {
    if (this.loading) return;
    const dom = document.getElementById('news') as HTMLDivElement;
    this.$emit('update', {
      ...this.newsItem,
      content: dom.innerHTML,
    });
  }
}
</script>

<template lang="pug">
van-popup(
  :value="visible"
  position="bottom")
  .article-wrapper
    .header
      van-nav-bar(
        :title="newsItem.title"
        left-text="关闭"
        right-text="更新"
        @click-left="visible = false"
        @click-right="updateArticle")
    .content(v-loading="loading")
      #news(v-html="newsItem.content" :contenteditable="true")
</template>

<style lang="stylus" scoped>
.article-wrapper
  height 100vh
  width 100vw
  padding-top 46px
  position relative
  .header
    position absolute
    top 0px
    left 0
    width 100%
  .content
    height 100%
    #news
      width 100%
      height 100%
      overflow auto
      -webkit-overflow-scrolling touch
      padding 10px 20px
      background #FFFFFF
</style>
