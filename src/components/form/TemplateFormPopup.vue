<script lang="ts">
import { IFormItem } from '@/interfaces/IFormItem';
import { IModel } from '@/lib/ActiveModel';
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import TemplateForm from './TemplateForm.vue';

@Component({
  components: {
    // 循环调用 TemplateForm
    TemplateForm: () => import('./TemplateForm.vue'),
  },
})
export default class TemplateFormPopup<T extends IModel> extends Vue {
  @Model('change', { type: Boolean }) visible!: boolean;
  @Prop({ type: Array, required: true }) template!: IFormItem[];
  @Prop({ type: Object, required: true }) formData!: Partial<T>[];

  get localVisible() {
    return this.visible;
  }

  set localVisible(val: boolean) {
    this.$emit('change', val);
  }

  onSubmit(formData: Partial<T>) {
    if (formData.id) {
      this.$emit('update', formData);
    } else {
      this.$emit('create', formData);
    }
  }

  onCancel() {
    this.localVisible = false;
  }
}
</script>

<template lang="pug">
.template-form-popup
  van-popup(
    v-model="localVisible"
    round
    position="bottom"
  )
    .form
      TemplateForm(
        ref="form"
        :formData="formData"
        :template="template"
        @submit='onSubmit'
        @cancel='onCancel'
      )

</template>

<style lang="stylus" scoped>
.form
  padding 20px 10px 0
</style>
