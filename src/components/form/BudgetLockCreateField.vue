<script lang="ts">
import { IBudget } from '@/models/finance/budget';
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { FormsResourceInfoFindByIds } from '@/models/find_by_ids.api';

@Component({
  components: {},
})
export default class BudgetLockCreateField extends Vue {
  @Model('change') value!: { id: number; lock_amount: number }[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  budgets: IBudget[] = [];
  budgetsLockAmountMapping: IObject = {};

  get jsonConfig() {
    return {
      methods: [
        'catalog_name',
        'subject_name',
        'origin_name',
        'amount',
        'locking_amount',
        'processing_payment_amount',
        'completed_payment_amount',
      ],
    };
  }

  get budgetsWithLockAmount() {
    return this.budgets.map(obj => ({
      ...obj,
      lock_amount: this.budgetsLockAmountMapping[`${obj.id}`],
    }));
  }

  get resourceModel() {
    return new FormsResourceInfoFindByIds();
  }

  async mounted() {
    const ids = this.value.map(item => item.id);
    const { data } = await this.resourceModel.create({
      path: '/finance/teacher/budgets',
      ids,
      config: this.jsonConfig,
    });
    this.budgets = data.records;
    this.budgetsLockAmountMapping = this.value.reduce((out, item) => {
      out[`${item.id}`] = Math.abs(item.lock_amount);
      return out;
    }, {} as IObject);
  }

  getBalanceAmount(record: IBudget) {
    return Number(record.amount) - Number(record.processing_payment_amount) - Number(record.completed_payment_amount);
  }

  getBalanceUnlockAmount(record: IBudget) {
    return (
      Number(record.amount) -
      Number(record.processing_payment_amount) -
      Number(record.completed_payment_amount) -
      Number(record.locking_amount)
    );
  }
}
</script>

<template lang="pug">
.budget-adjust-field
  .info(v-for="(record, index) in budgetsWithLockAmount", :key="index")
    van-cell(:border="false" title="请购金额" :value="budgetsLockAmountMapping[`${record.id}`]")
    van-cell(:border="false" title="二级内容" :value="record.catalog_name")
    van-cell(:border="false" title="三级内容" :value="record.name")
    van-cell(:border="false" title="科目" :value="record.subject_name")
    van-cell(:border="false" title="来源" :value="record.origin_name")
    van-cell(:border="false" title="金额" :value="record.amount")
    van-cell(:border="false" title="可报销" :value="getBalanceAmount(record)")
    van-cell(:border="false" title="锁定中" :value="record.locking_amount")
    van-cell(:border="false" title="报销中" :value="record.processing_payment_amount")
    van-cell(:border="false" title="已报销" :value="record.completed_payment_amount")
    van-cell(v-if='record.sub_project_amount > 0' :border="false" title="已分配" :value="record.sub_project_amount")
</template>

<style lang="stylus" scoped>
.flex
  .positive, .negative
    margin-right 10px
    color red
</style>
