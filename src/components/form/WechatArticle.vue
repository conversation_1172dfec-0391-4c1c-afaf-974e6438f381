<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { cloneDeep } from 'lodash/fp';
import { Article, IArticleContent, IArticle, INewsItem } from '@/models/wechat/article';
import { IFormItemArticle } from '@/models/bpm/instance';
import { IApp } from '../../models/wechat/app';
import WechatNewsItemEditor from './WechatNewsItemEditor.vue';

@Component({
  components: {
    WechatNewsItemEditor,
  },
})
export default class WechatArticle extends Vue {
  cacheMediaId: string = '';
  loading: boolean = false;
  detail: IArticleContent | IObject = {
    news_item: [],
  };
  // news item edit
  editorVisible: boolean = false;
  editNewsItem: INewsItem = {};
  editNewsItemIndex: number = 0;
  updateLoading: boolean = false;

  @Prop({ type: Object, default: () => ({}) }) private article!: IFormItemArticle;
  @Prop({ type: Object, default: () => [], required: true }) readonly wechat!: IApp; // appid
  @Prop({ type: Boolean, default: false }) readonly linkDisabled!: boolean;
  @Prop({ type: Boolean, default: false }) readonly editable!: boolean;

  get appId() {
    return this.article.appId || this.wechat.appid;
  }
  get linkDom() {
    return this.linkDisabled ? 'div' : 'a';
  }
  get articleModel() {
    return new Article({ appId: this.article.appId });
  }

  @Watch('article', { deep: true, immediate: true })
  onArticleChange() {
    // 直接传文章对象 IArticle
    if ((this.article as any).news_item) {
      this.detail = { ...this.article };
    } else {
      this.fetchData();
    }
  }

  async fetchData() {
    if (!(this.appId && this.article.mediaId)) {
      return;
    }
    if (this.cacheMediaId && this.cacheMediaId === this.article.mediaId) {
      return;
    }
    try {
      this.loading = true;
      this.cacheMediaId = this.article.mediaId;
      const { data } = await this.articleModel.findByApp(this.article.mediaId);
      if ((data as any).code === -1) {
        this.loading = false;
        this.$message.error(`获取公众号文章失败 ${(data as any).message}`);
      } else if ((data as any).error) {
        this.loading = false;
        this.$message.error(`获取公众号文章失败 ${(data as any).error}`);
      } else {
        this.detail = data;
        this.loading = false;
      }
    } catch (error) {
      this.loading = false;
      this.$message.error('获取公众号文章失败');
    }
  }
  getImageUrl(mediaId: string) {
    return this.articleModel.getImageUrl(mediaId);
  }
  previewItem(item: INewsItem) {
    window.open(item.url, '_black');
  }
  onClick() {
    this.$emit('click', this.article);
  }
  onEditNewsItem(item: INewsItem, index: number) {
    this.editNewsItem = cloneDeep(item);
    this.editNewsItemIndex = index;
    this.editorVisible = true;
  }
  async onUpdateNewsItem(item: INewsItem) {
    this.updateLoading = true;
    const formData = {
      index: this.editNewsItemIndex,
      media_id: this.article.mediaId,
      articles: item,
    };
    await this.articleModel.updateNewsItem(formData);
    this.updateLoading = false;
    this.editorVisible = false;
    this.cacheMediaId = '';
    this.fetchData();
  }
}
</script>

<template lang="pug">
.wechat-article(@click="onClick")
  .card(v-loading="loading")
    .empty(v-if="detail.news_item.length === 0")
      | 未获得公众号文章数据
    .news-item(
      v-for="(item, index) in detail.news_item"
      :key="item.title"
      :href="item.url"
      target="_black")
      .headline(v-if="index === 0" @click="previewItem(item)")
        .title.two-line-text {{ item.title }}
        img(:src="getImageUrl(item.thumb_media_id)" height="160")
        .actions(v-if="editable")
          van-button(icon="edit" size="small" @click.stop="onEditNewsItem(item, index)")
            | 编辑
      .news(v-else @click="previewItem(item)")
        .title.two-line-text
          span {{ item.title }}
          TextButton(@click.stop="onEditNewsItem(item, index)" icon="edit" v-if="editable")
            | 编辑
        img(:src="getImageUrl(item.thumb_media_id)" height="60" width="60")
    .date
      span(v-if="detail.update_time")
        | 更新时间：{{ (detail.update_time * 1000) | format('YYYY/MM/DD HH:mm') }}


  WechatNewsItemEditor(
    v-if="editorVisible"
    v-model="editorVisible"
    :newsItem="editNewsItem"
    :loading="updateLoading"
    @update="onUpdateNewsItem")
</template>

<style lang="stylus" scoped>
.wechat-article
  padding 20px
  width 100%
  background rgba(250, 250, 250, 1)
  .card
    margin 0 auto
    padding 20px
    min-height 160px
    width 100%
    border-radius 6px
    background rgba(255, 255, 255, 1)
    box-shadow 0px 2px 4px 0px rgba(0, 0, 0, 0.1)
    .empty
      color #888
      text-align center
      line-height 160px
    .news-item
      img
        flex-shrink 0
        object-fit cover
      .headline
        position relative
        display block
        margin-bottom 16px
        .title
          position absolute
          bottom 0
          left 0
          display flex
          align-items flex-end
          padding 14px 16px
          width 100%
          width 100%
          height 100%
          background linear-gradient(rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.3))
          color rgba(255, 255, 255, 1)
          color #fff
          font-weight 500
          font-size 16px
          line-height 22px
        img
          flex-shrink 0
          width 100%
          object-fit cover
        .actions
          position absolute
          top 10px
          right 10px
          z-index 99
      .news
        display flex
        align-items center
        padding 16px 0
        border-top 1px solid rgba(232, 232, 232, 1)
        .title
          margin-right 20px
          width 100%
          color rgba(56, 56, 56, 1)
          font-weight 500
          font-size 16px
          line-height 22px
    .date
      padding-top 20px
      border-top 1px solid rgba(232, 232, 232, 1)
      color rgba(128, 128, 128, 1)
      font-weight 400
      font-size 14px
      font-family PingFangSC-Regular, PingFang SC
      line-height 20px
      display flex
      align-items center
      justify-content space-between
      &:empty
        display none
</style>
