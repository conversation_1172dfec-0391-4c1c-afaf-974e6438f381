<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { IFormItemArticle } from '../../models/bpm/instance';
import WechatArticle from './WechatArticle.vue';
import WechatArticleSelector from './WechatArticleSelector.vue';
import { IApp } from '../../models/wechat/app';

@Component({
  components: {
    WechatArticle,
    WechatArticleSelector,
  },
})
export default class WechatArticleField extends Vue {
  selectorVisible: boolean = false;

  @Model('change', { type: Object }) readonly value!: IFormItemArticle;
  @Prop({ type: Object, default: () => [], required: true }) readonly wechat!: IApp; // appid
  @Prop(String) readonly name!: string;
  @Prop(String) readonly label!: number;
  @Prop(String) readonly placeholder!: string;
  @Prop(String) readonly errorMessage!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(Boolean) readonly disabled!: boolean;

  openSelector() {
    this.selectorVisible = true;
  }
  onSelect(article: IFormItemArticle) {
    this.$emit('input', article);
    this.$emit('change', article);
    this.selectorVisible = false;
  }
}
</script>

<template lang="pug">
.wechat-article-field
  van-field.field(
    :name="name"
    :label="label"
    :placeholder="placeholder || '选择'"
    :required="required"
    :readonly="true"
    :disabled="disabled"
    :error-message="errorMessage"
    @click="openSelector"
    @click-right-icon="openSelector"
    :is-link="!disabled"
    input-align="right"
    error-message-align="right")
  WechatArticle(
    :article="value"
    :wechat="wechat"
    :editable="true")

  WechatArticleSelector(
    v-model="selectorVisible"
    :wechat="wechat"
    @change="onSelect")
</template>

<style lang="stylus">
.wechat-article-field
  background transparent
  .field
    background-color transparent
    background transparent
    .van-field__label
      width 130px
</style>
