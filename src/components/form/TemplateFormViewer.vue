<script lang="ts">
import { IFormItem } from '@/interfaces/IFormItem';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { get } from 'xe-utils';
import { IInstance } from '@/models/bpm/instance';
import WechatArticle from './WechatArticle.vue';
import TemplateFormatter from './TemplateFormatter.vue';
import TableField from './TableField.vue';
import LevelTwoCollegeField from './LevelTwoCollegeField.vue';
import TeacherField from './TeacherField.vue';
import BudgetLockCreateField from './BudgetLockCreateField.vue';
import BudgetAdjustField from './BudgetAdjustField.vue';

@Component({
  components: {
    WechatArticle,
    TemplateFormatter,
    TableField,
    LevelTwoCollegeField,
    TeacherField,
    BudgetLockCreateField,
    BudgetAdjustField,
  },
})
export default class TemplateFormViewer extends Vue {
  @Prop({ type: String, default: '提交资料' }) title!: String;
  @Prop({ type: Object, default: () => ({}) }) formData!: IObject;
  @Prop({ type: Array, default: () => [] }) template!: IFormItem[];
  @Prop({ type: Object, default: () => ({}) }) instance!: IInstance;
  @Prop({ type: Boolean, default: false }) editable!: boolean;

  formattedTemplate: IFormItem[] = [];

  get nextLinesComps() {
    return [
      'file',
      'image',
      'checkbox',
      'textarea',
      'wechat_articles',
      'table',
      'level_two_college',
      'teacher',
      'budget_lock_create',
      'budget_adjust',
      'budget_adjust_negative',
      'finance_project_execute',
      'finance_project_own',
    ];
  }

  getContacts(value: any) {
    let members = [];
    if (value instanceof Array) {
      members = value;
    } else if (value instanceof Object) {
      members = [value];
    }
    return members.map(o => o.name).join('、');
  }

  selectValue(formData: IObject, item: IObject) {
    const value = this.loadValue(formData, item.key);
    const result = item.layout.options.find(
      (opt: IObject) => (opt.value && `${opt.value}` === `${value}`) || opt.label === value,
    );
    return result ? result.label : value;
  }

  loadValue(item: IObject, key: string) {
    return get(item, key);
  }

  getFinanceBudgetName(key: string) {
    const record = this.loadValue(this.formData, key);
    if (record instanceof Array && record.length > 0) {
      return record[0].name;
    }
    return '';
  }

  get projectId() {
    return this.instance.storage && this.instance.storage.project[0].id;
  }
}
</script>

<template lang="pug">
.template-form-viewer(v-show="formattedTemplate.length")
  TemplateFormatter(
    ref='TemplateFormatter',
    v-model='formattedTemplate'
    :payload='formData'
    :template='template'
  )
  .form-title {{ title }}
  van-cell(
    v-for="(item, index) in formattedTemplate"
    :key="`${item.key}_${item.layout.component}`"
    :title="item.name"
    v-if='item.accessibility !== "hidden"'
    border
  )
    .label(slot="label" v-if="nextLinesComps.includes(item.layout.component)")
      Attachments(v-if="item.layout.component === 'file'" :attachments="loadValue(formData, item.key)")
      Attachments(v-else-if="item.layout.component === 'image'" :attachments="loadValue(formData, item.key)")
      ul.options(v-else-if="item.layout.component === 'checkbox'")
        li.option(v-for="(val, index) in (loadValue(formData, item.key) || [])")
          | {{ val }}
      .textarea(v-else-if="item.layout.component === 'textarea'")
        | {{ loadValue(formData, item.key) }}
      WechatArticle(
        v-else-if="item.layout.component === 'wechat_articles'"
        :article="loadValue(formData, item.key)"
        :wechat="item.layout.wechat"
        :editable="editable")
      .value(v-else-if="item.layout.component === 'finance_project_execute'")
        | {{ getFinanceBudgetName(item.key) }}
      .value(v-else-if="item.layout.component === 'finance_project_own'")
        | {{ getFinanceBudgetName(item.key) }}
      BudgetLockCreateField(
        v-else-if="item.layout.component === 'budget_lock_create'"
        :value='loadValue(formData, item.key)',
        :disabled='true',
      )
      BudgetAdjustField(
        v-else-if="item.layout.component === 'budget_adjust_negative' || item.layout.component === 'budget_adjust'"
        :value='loadValue(formData, item.key)',
        :projectId="projectId"
        :disabled='true',
      )
      TableField.table-field(
        v-else-if="item.layout.component === 'table'"
        :value="loadValue(formData, item.key)"
        :attrs="item.layout.attrs"
        :disabled="true"
      )
      LevelTwoCollegeField(
        v-else-if="item.layout.component === 'level_two_college'"
        :value="loadValue(formData, item.key)"
        :disabled="true"
      )
      TeacherField(
        v-else-if="item.layout.component === 'teacher'"
        :value="loadValue(formData, item.key)"
        :disabled="true"
      )
    .value(v-else)
      span(v-if="item.layout.component === 'date'")
        | {{ loadValue(formData, item.key) | format('YYYY-MM-DD') }}
      span(v-else-if="item.layout.component === 'datetime'")
        | {{ loadValue(formData, item.key) | format('YYYY-MM-DD HH:mm:ss') }}
      span(v-else-if="item.layout.component === 'time'")
        | {{ loadValue(formData, item.key) | format('HH:mm:ss') }}
      span(v-else-if="item.layout.component === 'contacts'")
        | {{ getContacts(loadValue(formData, item.key)) }}
      span(v-else-if='item.layout.component === "select" || item.layout.component === "radio"')
        | {{ selectValue(formData, item) }}
      span(v-else)
        | {{ loadValue(formData, item.key) }}
</template>

<style lang="stylus" scoped>
.template-form-viewer
  width 100%
  .form-title
    padding 16px 16px 8px
    color #969799
    font-size 14px
    line-height 16px
  .label
    width 100%
  .options
    padding-left 18px
    color #666
    list-style square
    font-size 14px
    line-height 1.4
  .cell
    display flex
    justify-content space-between
    align-items center
    margin-top 10px
    padding 12px 0px
    background #fff
    font-szie 16px
    .key
      color #383838
    .value
      colro #888

.textarea
  margin 0
  white-space pre-wrap
</style>
