<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { ActiveStore } from '@/lib/ActiveStore';
import TaStoreFieldCell from './TaStoreFieldCell.vue';

@Component({
  components: {
    TaStoreFieldCell,
  },
})
export default class TaStoreFieldPopup extends Vue {
  currentPage: number = 1;
  totalPages: number = 1;
  records: IObject[] = [];
  keyword: string = '';
  query: IObject = {};
  loading: boolean = false;
  selectedRecords: IObject[] = [];

  @Model('input', { type: Boolean }) visible!: boolean;
  @Prop({ type: Boolean }) multiple!: boolean;
  @Prop({ type: [Array, Object], default: () => [] }) default!: object | object[];
  @Prop({ type: Object, required: true }) store!: ActiveStore<IObject>;
  @Prop({ type: Array, default: () => ['name_cont_any'] }) searchFields!: string[];

  get localVisible() {
    return this.visible;
  }

  set localVisible(val) {
    this.$emit('input', val);
  }

  // mounted() {
  //   this.fetchData(1);
  // }

  @Watch('visible', { immediate: true })
  onvisibleChange() {
    if (this.default instanceof Array) {
      this.selectedRecords = this.default.concat();
    } else if (this.default instanceof Object) {
      this.selectedRecords = [this.default];
    }
    if (this.visible) {
      this.fetchData(1);
    }
  }

  async fetchData(page = this.currentPage) {
    try {
      this.loading = true;
      const searchQuery: IObject = {};

      // 构建搜索查询，支持多字段搜索
      if (this.keyword) {
        this.searchFields.forEach(field => {
          searchQuery[field] = this.keyword;
        });
      }

      const { data } = await this.store.index({
        page,
        per_page: 10,
        q: searchQuery,
      });
      this.currentPage = data.current_page;
      this.totalPages = data.total_pages;
      const newRecords = data[this.store.model.dataIndexKey];
      if (page === 1) {
        this.records = newRecords;
      } else {
        this.records = this.records.concat(newRecords);
      }
      this.loading = false;
    } catch (error) {
      this.currentPage = this.totalPages + 1;
      this.loading = false;
    }
  }

  onSearch() {
    this.records = [];
    this.fetchData(1);
  }

  onSelect(record: IObject) {
    if (this.multiple) {
      const index = this.selectedRecords.map(o => o.id).indexOf(record.id);
      if (index > -1) {
        this.selectedRecords.splice(index, 1);
      } else {
        this.selectedRecords.push(record);
      }
    } else {
      this.$emit('change', [record]);
      this.close();
    }
  }

  // 确认多选
  confirmSelect() {
    if (this.selectedRecords.length > 0) {
      this.$emit('change', this.selectedRecords);
      this.close();
    }
  }

  close() {
    this.$emit('input', false);
  }
}
</script>

<template lang="pug">
van-popup(
  v-model="localVisible"
  round
  position="bottom"
)
  .contact-selector
    .header
      van-nav-bar(
        title="选择"
        left-text="关闭"
        :right-text="multiple ? `确认(${selectedRecords.length})` : null"
        @click-left="close"
        @click-right="confirmSelect"
      )
      SearchBar.search(
        v-model="keyword"
        placeholder="请输入搜索关键词"
        @search="onSearch"
      )
    .content
      van-list(
        v-model="loading"
        :immediate-check="false"
        :offset="1000"
        :finished="currentPage > totalPages"
        finished-text="没有更多了"
        @load="fetchData(currentPage + 1)"
      )
        TaStoreFieldCell(
          v-for="record in records"
          :key="record.id"
          :record="record"
          :isLink="!multiple"
          :checked="selectedRecords.some(o => o.id === record.id)"
          @click="onSelect(record)"
        )
</template>

<style lang="stylus" scoped>
.contact-selector
  height 90vh
  width 100vw
  padding-top 102px
  position relative
  .header
    position absolute
    top 0px
    left 0
    width 100%
  .content
    height 100%
    overflow auto
    border-top 1px solid #ebedf0
    -webkit-overflow-scrolling touch
  .avatar
    margin-right 8px
</style>
