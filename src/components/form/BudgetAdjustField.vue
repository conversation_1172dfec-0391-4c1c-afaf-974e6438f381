<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { financeTeacherBudgetsStore } from '@/store/modules/finance/teacher/budgets.store';

@Component({
  components: {},
})
export default class BudgetAdjust extends Vue {
  @Model('change') value!: { id: number; delta: number }[];
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Number }) projectId!: number;

  budgets: IObject[] = [];
  budgetsLockAmountMapping: IObject = {};
  title: string = '';

  get store() {
    return financeTeacherBudgetsStore;
  }

  async mounted() {
    const ids = this.value.map(item => item.id);
    this.title = Number(this.value[0].delta) > 0 ? '预算增加金额' : '预算减少金额';
    this.store.init({ parents: [{ type: 'projects', id: this.projectId }] });
    await this.store.index({
      q: { id_in: ids },
    });
    this.budgets = this.store.records.reduce((arr: IObject[], record) => {
      const { name, subject_name, amount } = record;
      const obj: IObject = this.value.find((e: any) => e.id === record.id) || {};
      arr.push({
        name,
        subjectName: subject_name,
        amount,
        delta: obj.delta,
      });
      return arr;
    }, []);
  }
}
</script>

<template lang="pug">
.budget-adjust-field
  .info(v-for="(record, index) in this.budgets", :key="index")
    van-cell(:border="false" :title="title" :value="record.delta")
    van-cell(:border="false" title="三级内容" :value="record.name")
    van-cell(:border="false" title="科目" :value="record.subjectName")
    //- van-cell(:border="false" title="来源" :value="record.origin_name")
    //- van-cell(:border="false" title="金额" :value="record.amount")
    //- van-cell(:border="false" title="可报销" :value="getBalanceAmount(record)")
    //- van-cell(:border="false" title="锁定中" :value="record.locking_amount")
    //- van-cell(:border="false" title="报销中" :value="record.processing_payment_amount")
    //- van-cell(:border="false" title="已报销" :value="record.completed_payment_amount")
    //- van-cell(v-if='record.sub_project_amount > 0' :border="false" title="已分配" :value="record.sub_project_amount")
</template>

<style lang="stylus" scoped>
.flex
  .positive, .negative
    margin-right 10px
    color red
</style>
