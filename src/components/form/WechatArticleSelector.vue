<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import { Article, IArticle } from '@/models/wechat/article';
import { IFormItemArticle } from '../../models/bpm/instance';
import { IApp } from '../../models/wechat/app';
import WechatArticle from './WechatArticle.vue';

@Component({
  components: {
    WechatArticle,
  },
})
export default class WechatArticleSelector extends Vue {
  currentPage: number = 1;
  totalPages: number = 1;
  articles: IArticle[] = [];
  keyword: string = '';
  query: IObject = {};
  loading: boolean = false;
  selectedArticles: IArticle[] = [];
  activeAppId: string = '';
  articleModel: Article = new Article({ appId: '' });

  @Model('input', { type: Boolean }) value!: boolean;
  @Prop({ type: Object, default: () => [], required: true }) readonly wechat!: IApp; // appid
  @Prop({ type: Boolean }) multiple!: boolean;

  @Watch('value', { immediate: true })
  onValueChange() {
    if (this.value) {
      this.fetchRecords(1);
    }
  }

  async fetchRecords(page = this.currentPage) {
    try {
      this.loading = true;
      this.activeAppId = this.wechat.appid;
      this.articleModel = new Article({ appId: this.activeAppId });
      const { data } = await this.articleModel.indexByParent(this.activeAppId, {
        page,
        per_page: 20,
      });
      this.currentPage = data.current_page;
      this.totalPages = data.total_pages;
      if (page === 1) {
        this.articles = data.item;
      } else {
        this.articles = this.articles.concat(data.item);
      }
      this.loading = false;
    } catch (error) {
      this.currentPage = this.totalPages + 1;
      this.loading = false;
    }
  }
  onSearch() {
    this.articles = [];
    this.fetchRecords(1);
  }
  onSelect(article: IArticle) {
    this.$emit('change', this.getWechatArticleValue(article));
    this.close();
  }
  getWechatArticleValue(article: IArticle): IFormItemArticle {
    return {
      appId: this.activeAppId,
      mediaId: article.media_id!,
      count: article.content.news_item.length,
      headline: (article.content.news_item[0] || {}).title,
    };
  }
  close() {
    this.$emit('input', false);
  }
}
</script>

<template lang="pug">
van-popup(
  :value="value"
  round
  position="bottom")
  .contact-selector
    .header
      van-nav-bar(
        title="选择微信文章"
        left-text="关闭"
        @click-left="close")
    .content
      van-list(
        v-model="loading"
        :immediate-check="false"
        :offset="1000"
        :finished="currentPage > totalPages"
        finished-text="没有更多了"
        @load="fetchRecords(currentPage + 1)")
        WechatArticle(
          v-for="article in articles"
          :key="article.id"
          :wechat="wechat"
          :article="article.content"
          :linkDisabled="true"
          @click="onSelect(article)")
</template>

<style lang="stylus" scoped>
.contact-selector
  height 90vh
  width 100vw
  padding-top 44px
  position relative
  .header
    position absolute
    top 0px
    left 0
    width 100%
  .content
    height 100%
    overflow auto
    border-top 1px solid #ebedf0
    -webkit-overflow-scrolling touch
</style>
