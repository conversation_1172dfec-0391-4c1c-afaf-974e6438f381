<script lang="ts">
// TODO: 单个题目
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { AnswerSetState } from '@/service/answer_set';
import { QuestionTypes } from '@/service/question';

@Component({
  components: {},
})
export default class AnswerField extends Vue {
  state: string = '';

  @Model('change', { type: Object, default: () => ({ question: { choices: { options: [] } } }) }) answer!: object;
  @Prop({ type: Number }) index!: number;
  @Prop({ type: Boolean }) disabled!: boolean;

  get QuestionTypes() {
    return QuestionTypes;
  }

  getOptionKeyTitle(title: string, index: number) {
    const key = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.charAt(index);
    return `${key}. ${title}`;
  }

  setAnswerValue(value: any) {
    this.$set(this.answer, 'value', value);
  }

  setMultiTypeAnswerValue(optionKey: string) {
    if (this.disabled) return;
    const checkboxKey = `checkbox_${optionKey}`;
    const refs = this.$refs[checkboxKey] as any;
    if (refs && refs[0]) {
      refs[0].toggle();
      this.$forceUpdate();
    }
  }
}
</script>

<template lang="pug">
.answer-field
  .question(v-if="answer.question.type === QuestionTypes.single")
    .title {{ index + 1 }}. {{ answer.question.title }}
    van-radio-group(v-model="answer.value" :disabled="disabled" @change="syncAnswer")
      van-cell-group.options(:border="false")
        van-cell(
          v-for="(option, j) in answer.question.choices.options"
          :key="option.key"
          :title="getOptionKeyTitle(option.value, j)"
          clickable
          @click="setAnswerValue(option.key)")
          van-radio(slot="right-icon" :name="option.key")
    .answer(v-if="answer.answer_meta")
      span.label.text-success 正确答案
      span {{ answer.rightValue }}
  //- 多选题
  .question(v-else-if="answer.question.type === QuestionTypes.multiple")
    .title {{ index + 1 }}. {{ answer.question.title }}（多选）
    van-checkbox-group(v-model="answer.value" :disabled="disabled" @change="syncAnswer")
      van-cell-group.options(:border="false")
        van-cell(
          v-for="(option, j) in answer.question.choices.options"
          :key="option.key"
          :title="getOptionKeyTitle(option.value, j)"
          clickable
          @click="setMultiTypeAnswerValue(option.key, j)")
          van-checkbox(slot="right-icon" :name="option.key" :ref="`checkbox_${option.key}`")
    .answer(v-if="answer.answer_meta")
      span.label.text-success 正确答案
      span {{ answer.rightValue }}
  //- 填空题
  .question(v-else-if="answer.question.type === QuestionTypes.fill")
    .title {{ index + 1 }}. {{ answer.question.title }}
    van-field.field(
      v-model.trim="answer.value"
      placeholder="请输入答案"
      autosize
      rows="2"
      type="textarea"
      :disabled="disabled"
      @change="syncAnswer")
    .answer(v-if="answer.answer_meta")
      span.label.text-success 正确答案
      span {{ answer.rightValue }}
  //- 简答题
  .question(v-else-if="answer.question.type === QuestionTypes.essay")
    .title {{ index + 1 }}. {{ answer.question.title }}
    van-field.field(
      v-model.trim="answer.value"
      placeholder="请输入答案"
      autosize
      rows="2"
      type="textarea"
      :disabled="disabled")
    .answer(v-if="answer.answer_meta")
      span.label.text-success 正确答案
      span {{ answer.rightValue }}
  Attachments.files(:attachments="answer.question.attachments ? answer.question.attachments.files : []")
</template>

<style lang="stylus" scoped></style>
