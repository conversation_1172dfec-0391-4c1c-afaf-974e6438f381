<template lang="pug">
  .cart-container
    .card
      .card-top
        .organization(:class="{'major': departmentType==='部门','department': departmentType==='科室'}")
          | {{ departmentType }}
        .organization-name {{ duty.name }}
      .card-bottom
        .organizatio-code 
          span.name 部门代码
          span.content {{ duty.code }}
        .higher-office 
          span.name 上级部门
          span.content {{ duty.path[duty.depth - 1] || duty.name }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ComOrganization extends Vue {
  @Prop({ type: Object }) duty!: {};
  @Prop({ type: String }) departmentType!: {};

  public mounted() {
    this.fetchData();
  }

  public fetchData() {}
}
</script>

<style lang="stylus" scoped>
.cart-container
  width 100%
  .card
    display flex
    flex-direction column
    margin-top 16px
    padding 0px 14px
    width 100%
    height 120px
    border-radius 3px
    background white
    box-shadow 0px 1px 2px 0px rgba(0, 0, 0, 0.1)
    .card-top
      display flex
      flex-direction row
      align-items center
      width 100%
      height 40%
      border-bottom 1px solid #E8E8E8
      .organization
        margin-right 35px
        width 49px
        height 22px
        border-radius 3px
        background #6CC47C
        color white
        text-align center
      .college
        background #6DC37D
      .major
        background #3DA8F5
      .department
        background #F88917;
      .organization-name
        color #6C6C6C
        font-weight 500
        font-size 15px
        font-family PingFangSC-Medium, PingFang SC
    .card-bottom
      display flex
      flex-direction column
      justify-content center
      height 60%
      .organizatio-code
        margin-bottom 8px
      .organizatio-code, .higher-office
        .name
          display inline-block
          margin-right 7px
          width 80px
          color #727272
          font-weight 500
          font-size 14px
          font-family PingFangSC-Medium, PingFang SC
        .content
          color #727272
          font-weight 500
          font-size 14px
          font-family PingFangSC-Medium, PingFang SC
      .organization-code
        margin-bottom 7px
</style>
