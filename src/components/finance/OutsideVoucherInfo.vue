<template lang="pug">
.outside-page
  .table-module
    .flex-bettween 行程
    table
      tr
        td 出发-到达
        td 时间
        td.text-right 票价（元）
      tr(v-for="(item, index) in data.route.value" :key="index" v-if="item.start_city || item.end_city")
        td {{ item.start_city }} - {{ item.end_city }}
        td
          div {{ item.start_at }} -
          div {{ item.end_at }}
        td.text-right ￥{{ $utils.toUsCurrency(item.amount) }}
      tr
        td 合计
        td.text-right(colspan="4") ￥{{ formatAmount(data.route.value) }}
  .table-module(v-for="(key, keyIndex) in dataKeys" :key="keyIndex" v-if="data[key]")
    .table-title.flex-between
      strong {{ data[key].label }}
      span(v-if="key === 'city_traffic'") ￥{{ $utils.toUsCurrency(data[key].value) }}
    table(v-if="key !== 'city_traffic'")
      tr
        td(
          v-for="(item, index) in data[key].attrkey"
          :key="index"
          :colspan="item.key === 'amount' ? 2 : 1"
          :class="{'text-right': item.key === 'amount'}")
          | {{ item.label }}
      template(v-if="key !== 'summary'")
        tr(v-for="(item, index) in data[key].value" :key="index" v-if="item.amount")
          td {{ item.number }}
          td {{ item.days }}
          td.text-right ￥{{ $utils.toUsCurrency(item.amount) }}
      template(v-else)
        tr(v-for="(item, index) in data[key].value" :key="index" v-if="item.amount")
          td(colspan="2")
            .black.text-ellipsis {{ item.content }}
          td.gray.text-right ￥{{ $utils.toUsCurrency(item.amount) }}
      tr
        td(colspan="2") 合计
        td.text-right ￥{{ formatAmount(data[key].value) }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class OutsideVoucherInfo extends Vue {
  @Prop({ type: Object, default: () => ({ route: {} }) }) private data?: any;

  formatAmount(val: any[]): string | number {
    if (!val || val.length === 0) return 0;
    const total = (val || []).reduce(
      (sum: number, item: any) => sum + (typeof item.amount === 'number' ? Number(item.amount) : 0),
      0,
    );
    return this.$utils.toUsCurrency(total);
  }

  get dataKeys() {
    return ['sleeper_subsidy', 'accommodation', 'service_subsidy', 'city_traffic', 'summary'];
  }
}
</script>

<style lang="stylus" scoped>
fontSize = 16px
lineHeight = 20px

.outside-page
  color #383838
  font-weight 500
  font-size fontSize
  line-height lineHeight
  .table-module
    padding 12px 0px
    width 100%
    font-weight 400
    font-size 14px
    line-height 20px
    table, td
      padding 0px
      border none
    .flex-bettween
      font-weight 500
      font-size 16px

table
  width 100%
  border 1px #808080 solid
  td
    padding 8px
    height 36px
    min-width 100px
    border 1px #808080 solid
    color #A6A6A6

.title
  width 100px
</style>
