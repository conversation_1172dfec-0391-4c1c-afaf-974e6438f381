<script lang="ts">
import { Component, Vue, Prop, Model, Emit } from 'vue-property-decorator';

export interface IStepItem {
  icon?: string;
  title: string;
  activeIcon?: string;
}

@Component
export default class Steps extends Vue {
  @Prop({ type: Array, default: () => [] }) private items!: IStepItem[];
  @Model('change', { type: Number, default: 0 }) private value!: number;

  getClass(index: number) {
    return {
      'step-active': index === this.value,
      'step-finish': index < this.value,
    };
  }

  getIconName(item: IStepItem, index: number) {
    return index < this.value ? item.activeIcon || 'checked' : item.icon;
  }

  @Emit('change')
  change(index: number) {
    return index;
  }
}
</script>

<template lang="pug">
.steps
  .step(
    v-for="(item, i) in items"
    :key="i"
    :class="getClass(i)"
  )
    .title(@click="change(i)")
      van-icon.icon(:name="getIconName(item, i)")
      span {{ item.title }}
</template>

<style lang="stylus" scoped>
.steps
  display flex
  align-items center
  margin-bottom 12px
  padding 0 22px
  font-size 14px
  .step
    position relative
    z-index 1
    flex-grow 1
    flex-shrink 0
    overflow hidden
    margin-right 8px
    padding 8px 0
    &:last-child
      flex-grow 0
      margin-right 0
      .title
        padding-right 0
    .title
      position relative
      display inline-flex
      align-items center
      padding-right 8px
      height 22px
      opacity 0.8
      &:after
        position absolute
        top 11px
        left 100%
        display block
        width 9999px
        height 1px
        background #e8e8e8
        content ''
        opacity 0.8
      .icon
        margin-right 4px
        vertical-align middle
        font-size 20px
  .step-active
    .title
      opacity 1
  .step-finish
    .title, .title:after
      opacity 1
</style>
