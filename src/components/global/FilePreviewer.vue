<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import FileSaver from 'file-saver';
import FileServer, { IFile } from '@/models/file';
import attachmentIcon from '@/assets/icons/file/attachment_32.svg';
import TaTimer from '@/components/global/TaTimer.vue';
import TaVideo from '@/components/global/TaVideo.vue';
import moment from 'dayjs';
import Pdf from 'vue-pdf';

@Component({
  components: {
    TaTimer,
    TaVideo,
    Pdf,
  },
})
export default class FilePreviewer extends Vue {
  @Model('input', { type: Boolean, default: false }) private visible!: boolean;
  @Prop({ type: String, default: '' }) private title!: string;
  @Prop({ type: Object, default: () => ({}) }) private attachment!: IFile;

  pages: number = 1;
  currentPage: number = 1;
  loading: boolean = false;

  innerVisible: boolean = false;
  fileServer: FileServer = new FileServer();
  attachmentIcon: any = attachmentIcon;

  get fileTitle() {
    return this.title || this.attachment.fileName || '文件预览';
  }
  get fileItem() {
    return {
      ...this.attachment,
      url: this.fileServer.getDownloadUrl(this.attachment),
      sizeText: FileServer.getSizeText(this.attachment.fileSize),
    };
  }
  get officeTypes() {
    // 注：Word和PowerPoint文档必须小于10M，Excel 必须小于五M
    return [
      'doc',
      'docx',
      'xlsx',
      'xls',
      'pptx',
      'ppt',
      'msword',
      'vnd.openxmlformats-officedocument.wordprocessingml.document',
      'vnd.ms-excel',
      'vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'vnd.ms-powerpoint',
      'vnd.openxmlformats-officedocument.presentationml.presentation',
    ];
  }
  get durationString() {
    if (this.fileItem.duration) {
      return this.$utils.parseSeconds(this.fileItem.duration);
    }
    return '';
  }
  get isOfficeFile() {
    return this.officeTypes.some(type => this.fileItem.fileType.includes(type));
  }

  @Watch('visible')
  onVisibleChange() {
    if (this.visible) {
      if (this.isOfficeFile) {
        window.location.href = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(
          this.fileItem.url,
        )}`;
      } else {
        this.innerVisible = true;
      }
    }
  }

  onClose() {
    this.$emit('input', false);
    this.$emit('close');
    setTimeout(() => {
      this.innerVisible = false;
    }, 1000);
  }
  download() {
    FileSaver.saveAs(this.fileItem.url, this.fileItem.fileName);
  }
  onPdfLoadPages(pages: number) {
    this.pages = pages;
  }
  onPdfProgress(v: number) {
    this.loading = v !== 1;
  }
  prePage() {
    if (this.currentPage === 1) return;
    this.currentPage -= 1;
  }
  nextPage() {
    if (this.currentPage === this.pages) return;
    this.currentPage += 1;
  }
}
</script>

<template lang="pug">
van-popup.popip-window(
  v-if="innerVisible"
  :value="visible"
  :duration="0.4"
  :safe-area-inset-bottom="true"
  get-container="body"
  position="bottom")
  .file-preview-container
    .file-previewer-header
      .title {{ fileItem.fileName }}
      .actions
        .action(@click="download") 下载
        .action(@click="onClose") 关闭
    .side-container
      .side-part(v-if="$slots.top")
        slot(name="top" :fileItem="fileItem")
    .file-previewer
      //- Pdf
      .type-file-frame(v-if="fileItem.fileType === 'pdf'")
        .move-box
          .cell-button(@click="prePage")
            van-icon(name="arrow-left")
          .cell-button(@click="currentPage = 1") {{ currentPage }}
          .cell-button(@click="nextPage")
            van-icon(name="arrow")
        Pdf.pdf(
          v-loading="loading"
          :src="fileItem.url"
          :page="currentPage"
          @num-pages="onPdfLoadPages"
          @progress="onPdfProgress")
      //- Video
      TaVideo.type-file-frame(
        v-else-if="fileItem.fileCategory === 'video'"
        :src="fileItem.url")
      //- Audio
      .type-file-frame(v-else-if="fileItem.fileCategory === 'audio'")
        audio.audio-widget(:src="fileItem.url" controls)
      //- Image
      .type-file-frame(v-else-if="fileItem.fileCategory === 'image'")
        img.image-widget(
          :src="fileItem.url"
          :alt="fileItem.fileName")
      //- Offices
      //- iframe.type-file-frame(
      //-   v-else-if="isOfficeFile"
      //-   :src='`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileItem.url)}`'
      //-   width='100%'
      //-   height='100%'
      //-   frameborder='0')
      //-   .frame-placeholder
      //-     img.file-icon(:src="attachmentIcon")
      //-     .file-tips 不支持在线预览，请下载后查看
      //-     van-button(icon="down" type="primary" @click="download")
      //-       | 点击下载
      //- Other
      .type-file-frame(v-else)
        .frame-placeholder
          img.file-icon(:src="attachmentIcon")
          .file-tips 不支持在线预览，请下载后查看
          van-button(icon="down" type="primary" @click="download")
            | 点击下载
</template>

<style lang="stylus">
.popip-window
  z-index 10000 !important
.file-preview-container
  display flex
  flex-direction column
  height 100vh
  padding-top 48px
  position relative
  .file-previewer-header
    position absolute
    top 0
    left 0
    width 100%
    height 48px
    padding 0 16px
    display flex
    align-items center
    justify-content space-between
    color #333
    border-bottom 1px solid RGBA(224, 224, 224, 1.00)
    .title
      width 100%
      overflow hidden
      white-space nowrap
      text-overflow ellipsis
    .actions
      flex-shrink 0
      display flex
      .action
        margin-left 16px
        &:active
          color #000
  .file-previewer
    width 100%
    height 100%
    background #333
    .type-file-frame
      width 100%
      height 100%
      display flex
      align-items center
      justify-content center
      position relative
      overflow auto
      -webkit-overflow-scrolling touch
      .pdf
        width 100%
      .move-box
        position absolute
        bottom 30px
        right 30px
        height 48px
        line-height 48px
        display inline-flex
        box-shadow 0 0 8px 0px rgba(0, 0, 0, 0.4)
        border-radius 4px
        .cell-button
          height 48px
          width 48px
          line-height 48px
          text-align center
          font-size 20px
          margin-left 1px
          background #f5f5f5
      .frame-placeholder
        font-size 16px
        color #fff
        text-align center
        .file-icon
          width 60px
          margin-bottom 20px
        .file-tips
          margin-bottom 30px
      .audio-widget
        width 90%
      .image-widget
        max-width 100%
        max-height 100%
  .side-container
    flex-shrink 0
    overflow auto
    background #333
</style>
