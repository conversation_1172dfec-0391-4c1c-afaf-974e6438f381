<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class AppGroupItem extends Vue {
  @Prop({ type: String }) to!: string;
  @Prop({ type: String }) icon!: string;
  @Prop({ type: String }) name!: string;
  @Prop({ type: String }) desc!: string;
  @Prop({ type: Number }) count!: number;
}
</script>

<template lang="pug">
.grid-app-container
  router-link.grid-app(:to="to || ''" v-if="!to.startsWith('http')")
    van-tag.count(type="danger" v-if="count" round)
      | {{ count }}
    img(:src="icon" height="42" width="42")
    .info(type="danger")
      .name {{ name }}
      .desc {{ desc }}
  a.grid-app(:href="to || ''" v-else="!to.startsWith('http')")
    van-tag.count(type="danger" v-if="count" round)
      | {{ count }}
    img(:src="icon" height="42" width="42")
    .info(type="danger")
      .name {{ name }}
      .desc {{ desc }}
</template>

<style lang="stylus" scoped>
.grid-app-container
  padding 4px
  width 33.33%
  position relative
  .grid-app
    display block
    padding 20px 0 20px
    border-radius 4px
    background rgba(255, 255, 255, 1)
    text-align center
    text-decoration none
    cursor pointer
    position relative
    .count
      position absolute
      top 10px
      right 10px
    .info
      display flex
      flex-direction column
      justify-content center
      margin-top 16px
      height 33px
      .name
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 13px
        line-height 18px
      .desc
        padding-top 3px
        color rgba(166, 166, 166, 1)
        font-weight 500
        font-size 11px
        line-height 12px
</style>
