<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class NavBar extends Vue {
  @Prop({ type: String, default: '' }) private title!: string;
  @Prop({ type: String, default: '返回' }) private leftText!: string;
  @Prop({ type: String, default: 'arrow-left' }) private leftIcon!: string;
  @Prop({ type: String, default: '' }) private rightText!: string;
  @Prop({ type: String, default: '' }) private rightIcon!: string;

  @Emit('right-click')
  rightClick(e: Event) {
    return e;
  }

  @Emit('left-click')
  leftClick(e: Event) {
    return e;
  }
}
</script>

<template lang="pug">
.nav-bar
  .left(@click="leftClick")
    van-icon.icon(:name="leftIcon")
    span.text {{ leftText }}
  .title
    | {{ title }}
  .right(v-if="rightText && rightIcon" @click="rightClick")
    span.text {{ rightText }}
    van-icon.icon(:name="rightIcon")
</template>

<style lang="stylus" scoped>
.nav-bar
  position relative
  overflow hidden
  height 56px
  color #fff
  font-weight 500
  font-size 15px
  line-height 56px
  .left
    position absolute
    top 0
    left 0
    padding-left 8px
    width 20%
    display flex
    align-items center
    height 100%
    &:active
      opacity 0.8
  .title
    overflow hidden
    margin 0 auto
    width 60%
    text-overflow ellipsis
    white-space nowrap
    text-align center
  .right
    position absolute
    top 0
    right 0
    padding-right 8px
    width 20%
    &:active
      opacity 0.8
  .text
    display inline-block
    margin 0 4px
    height 24px
    line-height 24px
</style>
