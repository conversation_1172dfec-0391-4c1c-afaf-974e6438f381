<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component
export default class DateTimeField extends Vue {
  @Prop({ type: String }) name!: string;
  @Prop({ type: String }) value!: string;
  @Prop({ type: String, default: 'datetime', validator: v => ['datetime', 'date', 'time', 'year-month'].includes(v) })
  type!: string;
  @Prop({ type: String }) format!: string;
  @Prop({ type: String, default: '时间' }) label!: string;
  @Prop({ type: String }) errorMessage!: string;
  @Prop({ type: String }) leftIcon!: string;
  @Prop({ type: String }) rightIcon!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) required!: boolean;
  @Prop({ type: Date, default: () => new Date(1900, 0, 1) }) minDate!: Date;
  @Prop({
    type: Date,
    default: () => {
      const nowYear = new Date().getFullYear();
      return new Date(nowYear + 50, 0, 1);
    },
  })
  maxDate!: Date;
  @Prop({ type: Number }) minHour!: number;
  @Prop({ type: Number }) maxHour!: number;
  @Prop({ type: Number }) minMinute!: number;
  @Prop({ type: Number }) maxMinute!: number;

  visible: boolean = false;
  pickerValue: Date | string = new Date();

  get dateFormat() {
    return (
      (this.format ||
        ({
          datetime: 'YYYY-MM-DD HH:mm',
          'year-month': 'YYYY-MM',
          date: 'YYYY-MM-DD',
          time: 'HH:mm',
        } as any))[this.type] || 'YYYY-MM-DD HH:mm'
    );
  }
  get placeholder() {
    return ({
      datetime: '请选择日期时间',
      'year-month': '请选择年月',
      time: '请选择时间',
      date: '请选择日期',
    } as any)[this.type];
  }
  get displayValue() {
    if (this.type === 'time') return this.value;
    return this.value && this.$dayjs(this.value).format(this.dateFormat);
  }

  openPicker() {
    if (this.disabled) return;
    if (this.value) {
      this.pickerValue = this.type === 'time' ? this.value : new Date(this.value);
    } else {
      this.pickerValue = this.type === 'time' ? '12:00' : new Date();
    }
    this.visible = true;
  }
  onPickerClose() {
    this.visible = false;
  }
  onPickerConfirm(value: any) {
    const res = this.type === 'time' ? value : this.$dayjs(value).format(this.format);
    this.$emit('input', res);
    this.$emit('change', value, res);
    this.onPickerClose();
  }
  formatter(type: string, value: any) {
    if (type === 'year') {
      return `${value}年`;
    }
    if (type === 'month') {
      return `${value}月`;
    }
    return value;
  }
}
</script>

<template lang="pug">
.date-time-field
  van-field.field(
    :name="name"
    :label="label"
    :value="displayValue"
    :placeholder="placeholder"
    :required="required"
    :readonly="true"
    :disabled="disabled"
    :left-icon="leftIcon"
    :right-icon="rightIcon"
    :error-message="errorMessage"
    @click="openPicker"
    @click-right-icon="openPicker"
    :is-link="!disabled"
    input-align="right"
    error-message-align="right")

  van-popup(
    v-model="visible"
    position="bottom"
    @close="onPickerClose"
    @click-overlay="onPickerClose")
    van-datetime-picker.picker(
      :type="type"
      :value="pickerValue"
      :show-toolbar="true"
      :title="placeholder"
      :min-date="minDate"
      :max-date="maxDate"
      :min-hour="minHour"
      :max-hour="maxHour"
      :min-minute="minMinute"
      :max-minute="maxMinute"
      :formatter="formatter"
      @cancel="onPickerClose"
      @confirm="onPickerConfirm")
</template>

<style lang="stylus" scoped>
.date-time-field
  background transparent
  .field
    background-color transparent
    background transparent
</style>
