<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class NavContainer extends Vue {
  @Prop({ type: String }) private title!: string;
  @Prop({ type: String, default: '/' }) private homePath!: string;
  @Prop({ type: Boolean, default: true }) private leftArrow!: boolean;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;
  @Prop({ type: Function }) private back!: () => void;
  // 搜索
  @Prop({ type: Boolean, default: false }) private search!: boolean;
  @Prop({ type: [Array, Object], default: () => ({}) }) private value!: any; // v-model: queryObject，ransack搜索的q对象
  @Prop({ type: Array, default: () => [] }) private variables!: any; // 搜索字段，例如：['name', 'desc']
  @Prop({ type: Array, default: () => [] }) private extraKey!: any; // 额外添加的 ransack 搜索条件，例如：['sell_time_gteq']
  @Prop({ type: String, default: '请输入搜索关键词' }) private placeholder?: string;
  @Prop({ type: String, default: 'or' }) private conjunction!: string; // ransack conjunction
  @Prop({ type: String, default: 'cont_any' }) private predicate!: string; // ransack predicate
  private visibleSearch: boolean = false;
  private keyword: any = '';
  get search_key() {
    if (this.variables.length === 0) {
      return '';
    }
    return `${this.variables.join(`_${this.conjunction}_`)}_${this.predicate}`;
  }
  get navTitle() {
    return this.title || this.$route.meta.title;
  }

  clickLeft() {
    if (this.back) {
      this.back();
      return;
    }
    const routeLength = window.history.length;
    if (routeLength > 2) {
      this.$router.back();
    } else {
      this.$router.replace(this.homePath);
    }
  }
  clickRight() {
    this.$emit('click-right');
  }
  // search
  onSearch(val: any) {
    const keywordArray = this.keyword
      ? this.keyword
          .trim()
          .toLocaleLowerCase()
          .replace(/\s{2,}/g, ' ')
          .split(' ')
      : [];
    const extra = this.extraKey.reduce(
      (obj: any, key: string) => ({
        ...obj,
        [key]: keywordArray,
      }),
      {},
    );
    const emitData = { ...this.value, ...extra };

    if (this.search_key) {
      emitData[this.search_key] = keywordArray;
    }
    this.$emit('input', emitData);
    this.$emit('change', emitData, this.keyword, keywordArray);
  }
  onCancel() {
    this.$emit('cancel');
    this.visibleSearch = false;
  }
}
</script>

<template lang="pug">
.nav-bar-container
  template(v-if="visibleSearch")
    van-search(
      v-model="keyword"
      show-action
      clearable
      :placeholder="placeholder"
      @search="onSearch"
      @input="onSearch"
      @cancel="onCancel")
  template(v-else)
    van-nav-bar.nav-bar-container__bar(
      :left-arrow="leftArrow"
      fixed
      @click-left="clickLeft"
      @click-right="clickRight")
      template(slot="title")
        slot(name="title")
          span {{ navTitle }}
      template(slot="right")
        slot(name="right")
          van-icon(name="search" @click="visibleSearch = true" v-if="search")
  .nav-bar-container__content(v-loading="loading")
    slot
</template>

<style lang="stylus" scoped>
.nav-bar-container
  position relative
  padding-top 46px
  height 100%
  .nav-bar-container__bar
    z-index 10
    box-shadow 0px 1px 2px 0px rgba(0, 0, 0, 0.08)
    width 100%
  .nav-bar-container__content
    position relative
    overflow auto
    width 100%
    height 100%
    -webkit-overflow-scrolling touch
  .van-search
    position absolute
    top 0px
    left 0px
    z-index 10
    padding 6px 12px
    width 100%
    box-shadow 0px 1px 2px 0px rgba(0, 0, 0, 0.08)
</style>
