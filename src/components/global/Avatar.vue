<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Avatar extends Vue {
  fontSize: string = '24px';

  @Prop({ type: String }) name!: string;
  @Prop({ type: String }) src!: string;
  @Prop({ type: String, default: '32px' }) size!: string;
  @Prop({ type: String, default: '' }) private bgColor?: string;

  get imageStyle() {
    return {
      'background-image': `url(${this.src})`,
    };
  }
  get firstChar() {
    return (this.name || '').charAt(0).toLocaleUpperCase();
  }
  get color() {
    return this.bgColor ? '#fff' : '';
  }

  mounted() {
    const width = (this.$refs.avatar as any).clientWidth;
    this.fontSize = `${Math.floor(width / 1.8)}px`;
  }
}
</script>

<template lang="pug">
.component-avatar(
  ref="avatar"
  :style="{ height: size, width: size }")
  .photo(v-if="src", :style="imageStyle")
  .letter-photo(:style="{background: bgColor, color: color}" v-else)
    .letter(:style="{ 'font-size': fontSize }")
      | {{ firstChar }}
</template>

<style lang="stylus" scoped>
.component-avatar
  position relative
  display inline-block
  border-radius 50%
  .photo
    width 100%
    height 100%
    border-radius inherit
    background-position center center
    background-size contain
    background-repeat no-repeat
  .letter-photo
    display flex
    justify-content center
    align-items center
    width 100%
    height 100%
    border-radius inherit
    background-color #B9DCF8
    color #3674B7
    .letter
      display inline-block
      height 24px
      font-weight bold
      font-size-adjust 0.8
      font-size 24px
      line-height 24px
</style>
