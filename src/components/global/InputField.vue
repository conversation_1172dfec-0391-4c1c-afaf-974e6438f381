<script lang="ts">
import { Component, Vue, Prop, Model, Emit } from 'vue-property-decorator';

@Component
export default class InputField extends Vue {
  @Model('change') readonly value!: any;
  @Prop(String) readonly name!: string;
  @Prop(String) readonly type!: string;
  @Prop(String) readonly placeholder!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(Boolean) readonly disabled!: boolean;
  @Prop(Number) readonly min!: number;
  @Prop(Number) readonly max!: number;

  @Prop(String) readonly label!: number;
  @Prop(String) readonly icon!: string;
  @Prop(String) readonly iconRight!: string;
  @Prop({ type: String, default: 'left' }) readonly align!: string;

  @Emit('change')
  inputChange(e: any) {
    return e.target.value;
  }

  changeBlur() {
    this.$utils.resumeDocumentHeight();
  }
}
</script>

<template lang="pug">
.field-cell
  .input-field(:class="{ 'field-required': required }")
    .prefix
      slot(name="prefix")
        van-icon.icon(v-if="icon" :name="icon")
        label(v-if="label" :for="name")
          | {{ label }}
    .field
      input(
        v-bind.prop="$props"
        @input="inputChange"
        autocomplete="auto"
        :style="{ 'text-align': align }"
        @blur.prevent="changeBlur")
    .suffix
      slot(name="suffix")
        van-icon.icon(v-if="iconRight" :name="iconRight")
</template>

<style lang="stylus" scoped>
@import '~@/assets/styles/global/colors'

$inputHeight = 24px

.field-cell
  padding 14px 0
  border-bottom 1px solid $borderColor
  .input-field
    position relative
    display flex
    align-items center
    min-height $inputHeight
    width 100%
    .icon
      display block
      color $grey
      font-size 20px
    .prefix
      display flex
      flex-shrink 0
      min-height 20px
      label
        display block
        min-width 105px
        word-wrap break-word
        word-break break-all
      .icon
        margin-right 16px
    .suffix
      .icon
        margin-left 16px
    .field
      width 100%
      input
        padding 0
        width 100%
        height $inputHeight
        outline none
        border none
        background-color transparent
        box-shadow none
        font-weight 400
        font-size 15px
        line-height 24px
        line-height $inputHeight
        caret-color $blue
        &:focus
          outline none
        &:disabled
          color #ccc
  .field-required
    .prefix
      position relative
      &:before
        position absolute
        top 0
        left -10px
        width 10px
        color red
        content '*'
        line-height 24px
</style>
