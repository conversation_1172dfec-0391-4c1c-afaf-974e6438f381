<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class AppGroup extends Vue {
  @Prop({ type: String }) title!: string;
}
</script>

<template lang="pug">
.grid-app-group
  .title(v-if="title")
    | {{ title }}
  .apps
    slot
</template>

<style lang="stylus" scoped>
.grid-app-group
  position relative
  overflow hidden
  .title
    padding 20px 16px 0px
    color rgba(56, 56, 56, 1)
    font-weight 600
    font-size 16px
    line-height 20px
  .apps
    display flex
    flex-wrap wrap
    margin-top 12px
    padding 0 12px
    width 100%
</style>
