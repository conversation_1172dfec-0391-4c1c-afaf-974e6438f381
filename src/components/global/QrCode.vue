<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import QRCode, { QRCodeErrorCorrectionLevel } from 'qrcode';

@Component
export default class QrCode extends Vue {
  @Prop({ type: String, required: true }) data!: string;
  @Prop({ type: Number, default: 4 }) scale!: number;
  @Prop({ type: String, default: 'Q', validator: l => ['L', 'Q', 'M', 'H'].indexOf(l) > -1 })
  level!: QRCodeErrorCorrectionLevel;
  @Prop({ type: String, default: '#fff' }) background!: string;
  @Prop({ type: String, default: '#000' }) foreground!: string;
  @Prop({ type: Number, default: 0 }) margin!: number;

  mounted() {
    this.draw();
    ['data', 'scale', 'level', 'background', 'foreground'].forEach(key => {
      this.$watch(key, this.draw);
    });
  }

  draw() {
    QRCode.toCanvas(
      this.$refs.qrcode,
      this.data,
      {
        errorCorrectionLevel: this.level,
        margin: this.margin,
        scale: this.scale,
        color: {
          dark: this.foreground,
          light: this.background,
        },
      },
      error => {
        if (!error) {
          const element = this.$refs.qrcode as HTMLElement;
          element.style.height = null;
          element.style.width = null;
        }
      },
    );
  }
}
</script>

<template lang="pug">
.qrcode-container
  canvas(ref="qrcode")
</template>

<style lang="stylus" scoped>
.qrcode-container
  canvas
    width 100%
    height auto
</style>
