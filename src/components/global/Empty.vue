<template lang="pug">
.empty-placeholder
  img(src="@/assets/images/meeting/icon_empty.png" width="80" height="80")
  .desc {{ desc }}
  .tips {{ tips }}
  .action
    slot
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Empty extends Vue {
  @Prop({ type: String, default: '暂无相应内容' }) private desc!: string;
  @Prop({ type: String, default: '' }) private tips!: string;
}
</script>

<style lang="stylus" scoped>
.empty-placeholder
  padding 40px 0px
  width 100%
  text-align center
  line-height 1
  img
    margin-bottom 10px
  .desc
    margin-bottom 12px
    color #808080
    font-weight 500
    font-size 16px
  .tips
    margin-bottom 22px
    color #A6A6A6
    font-size 14px
  .action
    button
      min-width 100px
      height 40px
</style>
