<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class SearchBar extends Vue {
  @Model('input', { type: String }) value!: string;
  @Prop({ type: String, default: '搜索' }) placeholder!: string;

  get keyword() {
    return this.value;
  }
  set keyword(value: string) {
    this.$emit('input', value);
  }

  onSearch(value: string) {
    this.$emit('search', value);
  }
  onCancel() {
    this.keyword = '';
    this.$emit('search', '');
    this.$emit('cancel');
  }
  onClear() {
    this.keyword = '';
    this.$emit('search', '');
    this.$emit('clear');
  }
}
</script>

<template lang="pug">
form(action="." @submit.prevent="")
  van-search.search(
    v-model="keyword"
    :placeholder="placeholder"
    type="search"
    :show-action="!!keyword"
    @search="onSearch"
    @cancel="onCancel"
    @clear="onClear")
</template>

<style lang="stylus" scoped></style>
