<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ITeacher } from '../../models/teaching/teacher';

@Component
export default class ContactCell extends Vue {
  @Prop({ type: Object, default: () => ({}) }) contact!: ITeacher;
  @Prop({ type: [<PERSON><PERSON><PERSON>, String], default: 'isLink' }) isLink!: string;
  @Prop({ type: Boolean, default: false }) checked!: boolean;

  onClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<template lang="pug">
van-cell(
  :title="contact.name"
  :is-link="!!this.isLink"
  @click="onClick")
  .contact-avatar.checked-icon(v-if="checked" slot="icon")
    van-icon(name="success")
  Avatar.contact-avatar(
    v-else
    :name="contact.name"
    :src="contact.avatar"
    slot="icon"
    size="38px")
  template(slot="label")
    div 工号：{{ contact.code }}
    div 部门：{{ (contact.department_path || []).join(' / ') }} / {{ contact.department_name }}
</template>

<style lang="stylus" scoped>
.contact-avatar
  margin-right 10px
  width 38px
  height 38px
  border-radius 50%

.checked-icon
  padding 7px
  background #50AC35
  color #fff
  text-align center
  font-size 24px
</style>
