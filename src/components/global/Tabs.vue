<script lang="ts">
import { Component, Vue, Prop, Watch, Model } from 'vue-property-decorator';

@Component
export default class Tabs extends Vue {
  @Model('change', { type: [String, Number], default: '', required: true }) value!: string;
  @Prop({ type: Array, default: () => [], required: true }) tabs!: any;

  private cursorLeft: number = 0;
  private cursorWidth: number = 0;

  get cursorStyle() {
    return {
      width: `${this.cursorWidth}px`,
      transform: `translate3d(${this.cursorLeft}px, 0px, 0px)`,
    };
  }

  @Watch('value')
  public watchChange() {
    this.initActiveCursor();
  }
  @Watch('tabs', { deep: true })
  public tabsChange() {
    this.initActiveCursor();
  }

  public mounted() {
    this.initActiveCursor();
  }
  public initActiveCursor() {
    this.$nextTick(() => {
      const stepTarget = this.$refs.activeStep && (this.$refs.activeStep as any[])[0];
      if (stepTarget) {
        this.cursorLeft = stepTarget.offsetLeft - 12;
        this.cursorWidth = stepTarget.clientWidth;
      }
    });
  }
  public clickStep(e: any, step: any) {
    if (!step.disabled) {
      this.$emit('change', step.key);
    }
  }
}
</script>

<template lang="pug">
.tabs-container
  .cursor(:style="cursorStyle")
  .tabs(ref="tabs")
    .tab(
      v-for="step in tabs"
      :key="step.key"
      :ref="step.key === value ? 'activeStep' : null"
      :class="{ active: step.key === value }"
      @click="(e) => { clickStep(e, step) }")
      span {{ step.text }}
</template>

<style lang="stylus">
$primaryColor = #3DA8F5
$lineBgColor = #3DA8F5
$textColor = #A6A6A6

.tabs-container
  position relative
  flex-shrink 0
  overflow hidden
  padding 0 12px
  width 100%
  min-width 60vw
  height 46px
  border-bottom 1px solid #E8E8E8
  background #fff
  white-space nowrap
  line-height 42px
  .cursor
    position absolute
    bottom 0
    width 100%
    height 2px
    background $primaryColor
    transition all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)
  .tabs
    display flex
    justify-content space-around
    align-items center
    height inherit
    line-height inherit
    .tab
      display inline-block
      height inherit
      color $textColor
      font-weight 500
      font-size 14px
      line-height inherit
      cursor pointer
      span
        vertical-align middle
    .active
      color #383838
</style>
