<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

@Component
export default class TextButton extends Vue {
  @Prop(String) private icon!: string;
  @Prop(String) private to!: string;
  @Prop({ type: Boolean, default: false }) private disabled!: boolean;
  @Prop({ type: Boolean, default: false }) private loading!: boolean;

  click(e: Event) {
    if (this.disabled || this.loading) {
      return;
    }
    if (this.to) {
      this.$router.push(this.to);
      return;
    }
    this.$emit('click', e);
  }
}
</script>

<template lang="pug">
button.text-button(@click="click" type="button" :disabled="disabled")
  van-icon.icon(v-if="loading" name="loading")
  van-icon.icon(
    v-if="icon"
    :name="icon")
  span.text-button__text
    slot
</template>

<style lang="stylus" scoped>
.text-button
  padding 0
  outline none
  border none
  background unset
  color #3DA8F5
  vertical-align middle
  font-weight 400
  font-size 14px
  line-height 20px
  cursor pointer
  &:last-child
    margin-left 14px
  &:hover
    color darken(#3DA8F5, 20%)
  &:disabled
    color #999999
    cursor not-allowed
  .text-button__text
    margin-left 4px
  .icon
    font-size 16px
</style>
