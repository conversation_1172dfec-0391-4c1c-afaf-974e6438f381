<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import teacherModel, { ITeacher } from '../../models/teaching/teacher';

@Component({
  components: {},
})
export default class ContactSelector extends Vue {
  currentPage: number = 1;
  totalPages: number = 1;
  teachers: ITeacher[] = [];
  keyword: string = '';
  query: IObject = {};
  loading: boolean = false;
  selectedContacts: ITeacher[] = [];

  @Model('input', { type: Boolean }) value!: boolean;
  @Prop({ type: Boolean }) multiple!: boolean;
  @Prop({ type: [Array, Object], default: () => [] }) defaultContacts!: object | object[];

  @Watch('value', { immediate: true })
  onValueChange() {
    if (this.defaultContacts instanceof Array) {
      this.selectedContacts = this.defaultContacts.concat();
    } else if (this.defaultContacts instanceof Object) {
      this.selectedContacts = [this.defaultContacts];
    }
    if (this.value) {
      this.fetchMembers(1);
    }
  }

  async fetchMembers(page = this.currentPage) {
    try {
      this.loading = true;
      teacherModel.setRole(sessionStore.role);
      const { data } = await teacherModel.index({
        page,
        per_page: 10,
        q: {
          name_or_code_cont_any: this.keyword,
        },
      });
      this.currentPage = data.current_page;
      this.totalPages = data.total_pages;
      if (page === 1) {
        this.teachers = data.teachers;
      } else {
        this.teachers = this.teachers.concat(data.teachers);
      }
      this.loading = false;
    } catch (error) {
      this.currentPage = this.totalPages + 1;
      this.loading = false;
    }
  }
  onSearch() {
    this.teachers = [];
    this.fetchMembers(1);
  }
  onSelect(teacher: ITeacher) {
    if (this.multiple) {
      const index = this.selectedContacts.map(o => o.id).indexOf(teacher.id);
      if (index > -1) {
        this.selectedContacts.splice(index, 1);
      } else {
        this.selectedContacts.push(teacher);
      }
    } else {
      this.$emit('change', [{ ...teacher, type: 'Teacher' }]);
      this.close();
    }
  }
  // 确认多选
  confirmSelect() {
    if (this.selectedContacts.length > 0) {
      this.$emit(
        'change',
        this.selectedContacts.map(o => ({ ...o, type: 'Teacher' })),
      );
      this.close();
    }
  }
  close() {
    this.$emit('input', false);
  }
}
</script>

<template lang="pug">
van-popup(
  :value="value"
  round
  position="bottom")
  .contact-selector
    .header
      van-nav-bar(
        title="选择联系人"
        left-text="关闭"
        :right-text="multiple ? `确认(${selectedContacts.length})` : null"
        @click-left="close"
        @click-right="confirmSelect")
      SearchBar.search(
        v-model="keyword"
        placeholder="请输入搜索关键词"
        @search="onSearch")
    .content
      van-list(
        v-model="loading"
        :immediate-check="false"
        :offset="1000"
        :finished="currentPage > totalPages"
        finished-text="没有更多了"
        @load="fetchMembers(currentPage + 1)")
        ContactCell(
          v-for="teacher in teachers"
          :key="teacher.id"
          :contact="teacher"
          :isLink="!multiple"
          :checked="selectedContacts.some(o => o.id === teacher.id)"
          @click="onSelect(teacher)")
</template>

<style lang="stylus" scoped>
.contact-selector
  height 90vh
  width 100vw
  padding-top 102px
  position relative
  .header
    position absolute
    top 0px
    left 0
    width 100%
  .content
    height 100%
    overflow auto
    border-top 1px solid #ebedf0
    -webkit-overflow-scrolling touch
  .avatar
    margin-right 8px
</style>
