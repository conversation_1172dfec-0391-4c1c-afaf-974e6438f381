<script lang="ts">
import { Component, Vue, Prop, Model, Emit } from 'vue-property-decorator';

@Component
export default class SelectField extends Vue {
  localValue: string = '';

  @Model('change') readonly value!: any;
  @Prop(String) readonly name!: string;
  @Prop(String) readonly placeholder!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(Boolean) readonly disabled!: boolean;
  @Prop({ type: Array, default: () => [] }) readonly options!: IObject[];

  @Prop(String) readonly label!: number;
  @Prop(String) readonly icon!: string;
  @Prop({ type: String, default: 'arrow' }) readonly iconRight!: string;
  @Prop({ type: String }) readonly errorMessage!: string;

  get activeOption() {
    const value = this.value || this.localValue;
    return this.options.find(o => String(o.value) === String(value) || o.label === value) || {};
  }

  get activeValue() {
    return this.activeOption.value || this.activeOption.label;
  }

  get displayValue() {
    return this.activeOption.label;
  }

  @Emit('change')
  change(e: any) {
    this.localValue = e.target.value;
    return e.target.value;
  }
}
</script>

<template lang="pug">
.field-cell
  .select-field(:class='{ "field-required": required }')
    .prefix
      slot(name='prefix')
        van-icon.icon(v-if='icon', :name='icon')
        label(v-if='label', :for='name')
          | {{ label }}
    .field
      span.value(v-if='displayValue', :class='{ "value-disabled": disabled }')
        | {{ displayValue }}
      span.placeholder(v-else)
        | {{ placeholder }}
      select(ref='select', v-bind.prop='$props', :value='activeValue', @change='change')
        option(value='', disabled, selected)
        option(
          v-for='(option, index) in options',
          :key='index',
          :value='option.value || option.label',
          :selected='activeValue === option.value || activeValue === option.label'
        )
          | {{ option.label }}
    .suffix(v-if='!displayValue')
      slot(name='suffix')
        van-icon.icon(v-if='iconRight', :name='iconRight')
  .error {{ errorMessage }}
</template>

<style lang="stylus" scoped>
@import '~@/assets/styles/global/colors'

$fieldHeight = 24px

.field-cell
  padding 10px 16px
  border-bottom 1px solid $borderColor
  .error
    padding-left 90px
    color #f44
    text-align right
    font-size 0.75rem
  .select-field
    position relative
    display flex
    align-items center
    min-height $fieldHeight
    width 100%
    .icon
      color $grey
      font-size 14px
    .prefix
      display flex
      flex-shrink 0
      align-items center
      label
        display block
        min-width 105px
        word-wrap break-word
        word-break break-all
      .icon
        margin-right 16px
    .suffix
      padding-left 5px
      line-height $fieldHeight
      .icon
        display inline
        font-size 12px
    .field
      position relative
      min-height $fieldHeight
      width 100%
      text-align right
      line-height $fieldHeight
      select
        position absolute
        top 0
        right 0
        bottom 0
        left 0
        z-index 10
        padding 0
        width 100%
        outline none
        border none
        background transparent
        box-shadow none
        text-align-last right
        font-weight 400
        font-size 15px
        opacity 0
        &:focus
          outline none
        &:disabled
          color #ccc
      .placeholder
        color #777
        vertical-align middle
      .value-disabled
        color #ccc
  .field-required
    .prefix
      position relative
      &:before
        position absolute
        top 0
        left -10px
        width 10px
        color red
        content '*'
        line-height $fieldHeight
</style>
