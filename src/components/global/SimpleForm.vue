<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';

@Component
export default class SimpleForm extends Vue {
  submit() {
    this.$emit('submit', this.getFormData());
  }
  formChange() {
    this.$emit('change', this.getFormData());
  }
  getFormData(): object {
    const submitData: any = {};
    const originData = new FormData(this.$refs.form as HTMLFormElement);
    Array.from(originData.keys()).forEach(key => {
      submitData[key] = originData.get(key);
    });
    return submitData;
  }
}
</script>

<template lang="pug">
form(@submit.prevent="submit" ref="form" @input.prevent="formChange")
  slot
</template>

<style lang="stylus" scoped>
form
  display block
</style>
