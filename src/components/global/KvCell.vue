<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class KvCell extends Vue {
  @Prop({ type: String }) name!: string;
  @Prop({ type: String }) value!: string;
}
</script>

<template lang="pug">
.kv-cell
  .name {{ name }}
  .value
    slot
      | {{ value }}
</template>

<style lang="stylus" scoped>
.kv-cell
  display flex
  align-items flex-start
  color #A6A6A6
  font-weight bold
  line-height 20px
  width 100%
  .name
    width 76px
    flex-shrink 0
    font-size 14px
  .value
    width calc(100% - 76px)
    font-size 14px
</style>
