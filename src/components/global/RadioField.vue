<script lang="ts">
import { Component, Vue, Prop, Model, Emit, Watch } from 'vue-property-decorator';

@Component
export default class RadioField extends Vue {
  @Model('change', { type: [Number, String] }) readonly value!: number | string;
  @Prop(String) readonly name!: string;
  @Prop(String) readonly errorMessage!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(Boolean) readonly disabled!: boolean;
  @Prop({ type: Array, default: () => [] }) readonly options!: IObject[];
  @Prop(String) readonly label!: string;

  payload: any = '';

  @Watch('value', { immediate: true })
  onValueChange() {
    this.payload = this.value;
  }

  change(value: any) {
    this.$emit('input', value);
    this.$emit('change', value);
  }
}
</script>

<template lang="pug">
.options-cell
  .title(:class="{ required: required }")
    | {{ label }}
  van-radio-group.option-group(
    v-if="options && options.length > 0"
    v-model="payload"
    :required="required"
    :disabled="disabled"
    @change="change")
    van-radio.option(
      v-for="(option, index) in options"
      :key="index"
      :name="option.value")
      | {{ option.label }}
    .error {{ errorMessage }}
  .empty-placeholder(v-else)
    | 没有可选项
</template>

<style lang="stylus" scoped>
.options-cell
  .title
    font-size 14px
    padding 10px 15px 0
  .required
    position relative
    &:before
      content '*'
      position absolute
      left 0.4375rem
      font-size 0.875rem
      color #f44
  .option-group
    padding 10px 15px
    .option
      margin 10px 0
    .error
      color #f44
      font-size 0.75rem
      text-align right
      padding-left 90px
  .empty-placeholder
    height 48px
    line-height 48px
    text-align center
    color #aaa
</style>
