<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { ImagePreview } from 'vant';

@Component({
  components: {},
})
export default class RichText extends Vue {
  @Prop({ type: String }) value!: string;

  urls: string[] = [];

  mounted() {
    this.initData();
  }
  beforeUpdate() {
    this.initData();
  }
  initData() {
    this.$nextTick(() => {
      const images: NodeList = document.querySelectorAll('#rich_text_component img');
      const urls: string[] = [];
      images.forEach((img: any) => {
        urls.push(img.src);
      });
      this.urls = urls;
    });
  }
  onClick(e: any) {
    if (e.target && e.target.tagName === 'IMG') {
      const url: string = e.toElement.src;
      const index = this.urls.findIndex(o => o === url);
      // @ts-ignore
      ImagePreview({
        images: this.urls,
        startPosition: index,
      });
    }
  }
}
</script>

<template lang="pug">
.ck-content(v-html="value" @click="onClick" id="rich_text_component")
</template>

<style lang="stylus" scoped></style>
