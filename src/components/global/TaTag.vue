<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TaTag extends Vue {
  @Prop({ type: String }) type!: string;
  @Prop({ type: String, default: 'default' }) size!: string;
  @Prop({ type: String }) color!: string;

  onClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<template lang="pug">
.ta-tag(
  :class="[`ta-tag-${type}`, `ta-tag-${size}`]"
  :style="{ color: color && '#fff', background: color }"
  @click="onClick")
  slot
</template>

<style lang="stylus" scoped>
.ta-tag
  display inline-block
  padding 6px 10px
  border-radius 4px
  background #F5F5F5
  color #A6A6A6
  height 32px
  font-size 14px
  font-weight 500
  line-height 20px
  vertical-align middle
  text-align center

.ta-tag-small
  padding 3px 6px
  line-height 16px
  font-size 12px
  height 22px
  border-radius 3px

.ta-tag-mini
  padding 2px 4px
  line-height 12px
  font-size 10px
  height 16px
  border-radius 2px

.ta-tag-primary
  @extend .ta-tag
  background rgba(237, 247, 255, 1)
  color #3DA8F5

.ta-tag-success
  @extend .ta-tag
  background #F0F9F2
  color #6DC37D

.ta-tag-warning
  @extend .ta-tag
  background #FFF7E6
  color #FA8C15

.ta-tag-danger
  @extend .ta-tag
  background rgba(255, 79, 62, 0.1)
  color #e50114
</style>
