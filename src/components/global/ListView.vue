<script lang="ts">
// Scroll 支持的事件
// [refresh,enable,disable,beforeScrollStart,scrollStart,scroll,scrollEnd,scrollCancel,touchEnd,flick,destroy,pullingUp]
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import BScroll from 'better-scroll';

interface IPos {
  x: number;
  y: number;
}

@Component
export default class ListView extends Vue {
  @Prop({ type: Array, default: () => [], required: true }) data!: IObject[];
  /**
   * 1 滚动的时候会派发scroll事件，会截流。
   * 2 滚动的时候实时派发scroll事件，不会截流。
   * 3 除了实时派发scroll事件，在swipe的情况下仍然能实时派发scroll事件
   */
  @Prop({ type: Number, default: 2, validator: val => [1, 2, 3].includes(val) }) probeType!: number;
  @Prop({ type: Boolean, default: false }) loading!: boolean; // loading  控制
  @Prop({ type: <PERSON>olean, default: true }) click!: boolean; // 点击列表是否派发 click 事件
  @Prop({ type: <PERSON>olean, default: true }) listenScroll!: boolean;
  @Prop({ type: Boolean, default: false }) pullup!: boolean;
  @Prop({ type: Boolean, default: false }) pulldown!: boolean;
  @Prop({ type: String, default: '暂无数据' }) emptyText!: string;

  scroll!: BScroll;
  position: IPos = { x: 0, y: 0 };

  @Watch('data')
  onDataChange() {
    setTimeout(() => {
      this.refresh();
    }, 20);
  }

  mounted() {
    setTimeout(() => {
      this.initListView();
    }, 20);
  }

  initListView() {
    if (!this.$refs.listView) {
      return;
    }
    // init scroll
    this.scroll = new BScroll(this.$refs.listView as HTMLElement, {
      probeType: this.probeType,
      click: this.click,
      scrollY: true,
      disableMouse: false,
      disableTouch: false,
      scrollbar: true,
      mouseWheel: {
        speed: 2,
        invert: false,
        easeTime: 300,
      },
    });
    // 滚动事件
    if (this.listenScroll) {
      this.scroll.on('scroll', (pos: IPos) => {
        this.position = pos;
        this.$emit('scroll', pos);
      });
    }
    // 上拉加载
    this.scroll.on('scrollEnd', (pos: IPos) => {
      this.position = pos;
      if (this.pullup && this.scroll.y <= this.scroll.maxScrollY + 50) {
        this.$emit('scrollToEnd');
        this.$emit('loadMore');
      }
    });
    // 下拉刷新
    this.scroll.on('touchEnd', (pos: IPos) => {
      if (this.pulldown && pos.y > 50) {
        this.$emit('pulldown');
        this.$emit('refresh');
      }
    });
  }
  disable() {
    if (this.scroll) {
      this.scroll.disable();
    }
  }
  enable() {
    if (this.scroll) {
      this.scroll.enable();
    }
  }
  refresh() {
    if (this.scroll) {
      this.scroll.refresh();
    }
  }
  scrollTo(...args: any) {
    if (this.scroll) {
      this.scroll.scrollTo(...args);
    }
  }
  scrollToElement(...args: any) {
    if (this.scroll) {
      this.scroll.scrollToElement(...args);
    }
  }
  backPosition() {
    this.$nextTick(() => {
      this.scrollTo(this.position.x, this.position.y, 0);
    });
  }
}
</script>

<template lang="pug">
.list-view(ref="listView")
  .list-view-content
    slot
    .loading-cell(v-if="loading")
      van-loading
    Empty(
      v-else-if="data.length === 0"
      :desc="emptyText")
    .finish-cell(v-else-if="!pullup")
      | 已加载全部
</template>

<style lang="stylus" scoped>
.list-view
  overflow hidden
  height 100%
  .list-view-content
    .loading-cell
      height 32px
      text-align center
      line-height 32px
    .finish-cell
      height 48px
      color #999999
      text-align center
      font-size 14px
      line-height 48px
</style>
