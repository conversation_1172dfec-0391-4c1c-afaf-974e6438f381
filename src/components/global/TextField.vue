<script lang="ts">
import { Component, Vue, Prop, Model, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TextField extends Vue {
  @Model('change') readonly value!: any;
  @Prop(String) readonly name!: string;
  @Prop(String) readonly placeholder!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(Boolean) readonly disabled!: boolean;
  @Prop({ type: Number, default: 4 }) readonly rows!: number;

  @Prop(String) readonly label!: string;

  @Emit('change')
  change(e: any) {
    return e.target.value;
  }
}
</script>

<template lang="pug">
.field-cell
  .text-field(:class="{ 'field-required': required }")
    .prefix
      slot(name="prefix")
        span {{ label }}
    .field
      textarea(v-bind.prop="$props" @input="change" :rows="rows" @blur.prevent="$utils.resumeDocumentHeight")
    .suffix
      slot(name="suffix")
</template>

<style lang="stylus" scoped>
@import '~@/assets/styles/global/colors'

$inputHeight = 24px

.field-cell
  padding 14px 0
  border-bottom 1px solid $borderColor
  .text-field
    position relative
    display flex
    flex-direction column
    width 100%
    .field
      margin-top 12px
      width 100%
      textarea
        display block
        width 100%
        border none
        font-weight 400
        font-size 14px
        line-height 20px
        &:focus
          outline none
        &:disabled
          background #fff
          color #999
  .field-required
    .prefix
      position relative
      &:before
        position absolute
        top 0
        left -10px
        width 10px
        color red
        content '*'
        line-height 24px
</style>
