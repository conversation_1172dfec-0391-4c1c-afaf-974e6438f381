<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class TaTimer extends Vue {
  @Model('change', { type: Number, default: 0 }) private seconds!: number;
  @Prop({ type: Boolean, default: true }) private auto!: boolean;

  hourString: string = '00';
  minuteString: string = '00';
  secondString: string = '00';
  timer: any = null;

  mounted() {
    document.addEventListener('visibilitychange', this.onVisibilityChange);
    if (this.auto) {
      this.start();
    }
  }

  beforeDestroy() {
    document.removeEventListener('visibilitychange', this.onVisibilityChange);
  }

  start() {
    clearInterval(this.timer);
    this.nextTick();
    this.timer = setInterval(this.nextTick, 1000);
  }

  pause() {
    clearInterval(this.timer);
  }

  onVisibilityChange() {
    if (document.hidden) {
      this.pause();
    } else {
      this.start();
    }
  }

  nextTick() {
    const secPerMinute = 60;
    const secPerHour = 60 * 60;
    const hours = Math.floor(this.seconds / secPerHour);
    this.hourString = String(hours).padStart(2, '0');
    const minutesLeft = this.seconds - hours * secPerHour;
    const minutes = Math.floor(minutesLeft / secPerMinute);
    this.minuteString = String(minutes).padStart(2, '0');
    const seconds = minutesLeft - minutes * secPerMinute;
    this.secondString = String(seconds).padStart(2, '0');
    this.$emit('change', this.seconds + 1);
  }
}
</script>

<template lang="pug">
.ta-timer
  .tick
    slot(name="hour" :hour="hourString")
      | {{ hourString }}
  slot(name="hourSymbol")
    .symbol :
  .tick
    slot(name="minute" :minute="minuteString")
      | {{ minuteString }}
  slot(name="minuteSymbol")
    .symbol :
  .tick
    slot(name="second" :second="secondString")
      | {{ secondString }}
  slot(name="secondSymbol")
</template>

<style lang="stylus" scoped>
.ta-timer
  display inline-flex
  align-items center
  font-size 24px
  letter-spacing 2px
  font-weight bold
  line-height 1
  .tick
    font-family 'DINCond'
  .symbol
    margin 0 4px
</style>
