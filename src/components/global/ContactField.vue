<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { ITeacher } from '../../models/teaching/teacher';

@Component({
  components: {},
})
export default class ContactField extends Vue {
  contactSelectorVisible: boolean = false;

  @Model('change', { type: [Array, Object] }) readonly value!: ITeacher | ITeacher[];
  @Prop(String) readonly name!: string;
  @Prop(String) readonly label!: number;
  @Prop(String) readonly placeholder!: string;
  @Prop(String) readonly errorMessage!: string;
  @Prop(Boolean) readonly required!: boolean;
  @Prop(Boolean) readonly disabled!: boolean;
  @Prop(Boolean) readonly multiple!: boolean;
  @Prop(String) readonly leftIcon!: string;
  @Prop({ type: String, default: 'arrow' }) readonly rightIcon!: string;

  openSelector() {
    this.contactSelectorVisible = true;
  }
  // contacts 为联系人对象，或者数value组
  onSelect(contacts: ITeacher) {
    this.$emit('input', contacts);
    this.$emit('change', contacts);
    this.contactSelectorVisible = false;
  }
}
</script>

<template lang="pug">
.contact-field
  van-field.field(
    :name="name"
    :label="label"
    :placeholder="placeholder || '点击设置'"
    :required="required"
    :readonly="true"
    :disabled="disabled"
    :left-icon="leftIcon"
    :error-message="errorMessage"
    @click="openSelector"
    @click-right-icon="openSelector"
    :is-link="!disabled"
    input-align="right"
    error-message-align="right")
  ContactCell(
    v-for="user in value || []"
    :key="user.id"
    :contact="user"
    :isLink="false")

  ContactSelector(
    v-model="contactSelectorVisible"
    :multiple="multiple"
    :defaultContacts="value"
    @change="onSelect")
</template>

<style lang="stylus" scoped>
.contact-field
  background transparent
  .field
    background-color transparent
    background transparent
</style>
