<script lang="ts">
import { Component, Vue, Prop, Watch, Model } from 'vue-property-decorator';
import AttachmentFile from './AttachmentFile.vue';
import { IFile } from '../../models/file';

@Component({
  components: {
    AttachmentFile,
  },
})
export default class Attachments extends Vue {
  @Model('change', { type: Array, default: () => [], required: true }) attachments!: IFile[];
  @Prop({ type: Boolean, default: false }) showActions!: boolean;
  @Prop({ type: Boolean, default: false }) display!: boolean;
  @Prop({ type: Boolean, default: false }) downloadable!: boolean;
  @Prop({ type: Boolean, default: true }) previewable!: boolean;

  remove(fileItem: IFile, index: number) {
    const items = this.attachments.concat();
    items.splice(index, 1);
    this.$emit('remove', fileItem, index);
    this.onChange(items);
    if (fileItem._cancel) {
      fileItem._cancel();
    }
  }
  restart(fileItem: IFile, index: number) {
    this.$emit('restart', fileItem, index);
  }
  preview(fileItem: IFile, index: number) {
    this.$emit('preview', fileItem, index);
  }
  // 与 fileUploader 同步
  onChange(files: IFile[]) {
    const todoFiles = files.filter((file: IFile) => file.status === 'todo');
    const doingFiles = files.filter((file: IFile) => file.status === 'doing');
    const doneFiles = files.filter((file: IFile) => file.status === 'done');
    const errorFiles = files.filter((file: IFile) => file.status === 'error');

    const allSettled = todoFiles.length === 0 && doingFiles.length === 0;
    const statusFiles = {
      todo: todoFiles,
      doing: doingFiles,
      done: doneFiles,
      error: errorFiles,
    };

    this.$emit('change', files, statusFiles, allSettled);
  }
}
</script>

<template lang="pug">
.attachments
  AttachmentFile(
    v-for="(item, index) in attachments"
    :attachment="item"
    :key="`index_${item.fileKey}`"
    :display="display"
    :showActions="showActions"
    :downloadable="downloadable"
    :previewable="previewable"
    @preview="preview(item, index)"
    @remove="remove(item, index)"
    @restart="restart(item, index)")
</template>

<style lang="stylus" scoped>
.attachments
  position relative
  overflow hidden
</style>
