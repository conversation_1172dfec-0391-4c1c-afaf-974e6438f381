<template lang="pug">
.user-cell
  Avatar(:src="user.avatar" :name="user.name" :size="size" :width="size + 'px'" :height="size + 'px'")
  label {{ user.name }}
  span(style="color: #999" v-if="userInfo.id === user.id") (我)
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import store from '@/store';

@Component({
  components: {},
})
export default class UserInfoCell extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private user?: any;
  @Prop({ type: String, default: '26px' }) private size?: string;
  get userInfo() {
    return store.state.currentUser || {};
  }
}
</script>

<style lang="stylus" scoped>
.user-cell
  display flex
  align-items center
  color #383838
  font-size 16px
  line-height 20px
  label
    margin 0px 2px 0px 6px
</style>
