<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { IFormItem } from '@/interfaces/IFormItem';
import SimpleForm from './SimpleForm.vue';

@Component({
  components: {},
})
export default class StudyingTemplateForm extends Vue {
  @Prop({ type: Object, default: () => ({}) }) readonly data!: object;
  @Prop({ type: Array, default: () => [] }) readonly template!: IFormItem[];

  get templateMap(): any {
    return this.template.reduce((obj: any, item: IFormItem) => {
      if (item.key) {
        obj[item.key] = item;
      }
      return obj;
    }, {});
  }

  onSubmit(formData: { [index: string]: any }) {
    this.$emit('submit', formData);
  }

  validate(callback: (err: any[] | null, formData: object) => void) {
    const errors: any[] = [];
    const formData: object = (this.$refs.form as SimpleForm).getFormData();
    Object.entries(formData).forEach(kvs => {
      if ((this.templateMap[kvs[0]] as IFormItem).layout.required && !kvs[1]) {
        errors.push(this.templateMap[kvs[0]]);
      }
    });
    const err = errors.length > 0 ? errors : null;
    callback(err, formData);
  }
}
</script>

<template lang="pug">
.template-form
  SimpleForm(ref="form" @submit="onSubmit")
    .form-item(v-for="(item, index) in template" :key="item.key")
      InputField(
        v-if="item.layout.component === 'input'"
        :type="item.layout.type"
        :label="item.name"
        :name="item.key"
        :value="data[item.key]"
        :placeholder="item.layout.placeholder"
        :required="item.layout.required"
        :disabled="item.layout.disabled"
        align="right")
      InputField(
        v-if="item.layout.component === 'date'"
        :label="item.name"
        :name="item.key"
        :value="data[item.key]"
        type="date"
        :placeholder="item.layout.placeholder"
        :required="item.layout.required"
        :disabled="item.layout.disabled"
        align="right")
      TextField(
        v-if="item.layout.component === 'textarea'"
        :label="item.name"
        :name="item.key"
        :value="data[item.key]"
        :placeholder="item.layout.placeholder"
        :required="item.layout.required"
        :disabled="item.layout.disabled")
      SelectField(
        v-if="item.layout.component === 'select'"
        :label="item.name"
        :name="item.key"
        :value="data[item.key]"
        :placeholder="item.layout.placeholder"
        :required="item.layout.required"
        :options="item.layout.options"
        :disabled="item.layout.disabled")
    slot
</template>

<style lang="stylus" scoped></style>
