<template lang="pug">
.loading-container
  VanLoading(color="#3DA8F5")
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { Loading as VanLoading } from 'vant';

@Component({
  components: {
    VanLoading,
  },
})
export default class componentName extends Vue {}
</script>

<style lang="stylus" scoped>
.loading-container
  text-align center
  padding 12px
</style>
