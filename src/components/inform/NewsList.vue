<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { INews } from '../../models/inform/news';
import FileServer from '../../models/file';

@Component({
  components: {},
})
export default class InformNewsList extends Vue {
  @Model('change') private value!: any;
  @Prop({ type: Array, default: () => [] }) private newsList!: INews[];
  @Prop({ type: Array, default: () => [] }) private tabs!: IObject[];

  tabKey: string = '';

  changeTab(val: string) {
    this.$emit('change', val);
    this.$emit('changeTab', val);
  }

  parseImage(inform: IObject) {
    return inform.cover_image && typeof inform.cover_image === 'object'
      ? new FileServer().getDownloadUrl(inform.cover_image[0])
      : '';
  }
}
</script>

<template lang="pug">
.inform-news-list
  .tabs
    Tabs(:tabs='tabs', v-model='tabKey', @change='changeTab')
  .news-group
    .first-news(v-for='news in [newsList[0]].filter((item) => item)')
      router-link(:to='`/portal/user/news/${news.id}`')
        .title {{ news.title }}
        img(:src='parseImage(news)')
        .footer.flex-between
          p.one-line {{ $dayjs(news.publish_time).format("YYYY-MM-DD") }}
          p.one-line {{ news.tag_list.map((tag) => `#${tag}`).join(" ") }}
    .news(v-for='news in newsList.slice(-(newsList.length - 1))', :key='news.id')
      router-link(:to='`/portal/user/news/${news.id}`')
        .shell.flex-between
          .content
            .title.two-line {{ news.title }}
            .footer.flex-between
              p.one-line {{ $dayjs(news.publish_time).format("YYYY-MM-DD") }}
              p.one-line {{ news.tag_list.map((tag) => `#${tag}`).join(" ") }}
          .image
            img(:src='parseImage(news)')
</template>

<style lang="stylus" scoped>
.inform-news-list
  .news-group
    padding 20px
    .title
      color black
      font-weight 500
      font-size 15px
    .footer
      color #A6A6A6
      font-size 13px
    .first-news
      padding-bottom 20px
      img
        padding 12px 0
        width 100%
        height 168px
        object-fit cover
    .news
      .shell
        padding-bottom 20px
        .image
          img
            width 100px
            height 60px
            object-fit cover
        .content
          position relative
          margin-right 17px
          width 100%
          height 60px
          object-fit cover
          .footer
            position absolute
            bottom 0
            left 0
            width 100%

.two-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 2

.one-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 1
</style>
