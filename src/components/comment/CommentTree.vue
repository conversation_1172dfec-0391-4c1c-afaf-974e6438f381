<template lang="pug">
van-list
  .comment-cell(v-for="(item, index) in comments" :key="index")
    .comment-top
      .flex
        Avatar(size="28px" :name="item.user_name")
        .name
          strong(v-if="item.user_name") {{ item.user_name }}
          span.name-type(v-if="item.user_id === userInfo.id") (我)
      .date
        span {{ $dayjs(item.created_at).format('MM月DD日 HH:mm') }}
    .comment-middle
      template(v-if="item.reply_user_name")
        span.reply 回复
        span.nicknane
          | {{ item.reply_user_name === userInfo.name ? item.reply_user_name + '(我)' : item.reply_user_name }}：
      .text-pre.comment-body {{ item.body }}
      template(v-if="item.files && item.files.length > 0")
        Attachments(
          style="width: 100%"
          :attachments="item.files"
          :showActions="false")
    .comment-bottom
      .operation-box(v-if="!disabled")
        span {{ $dayjs(item.created_at).fromNow() }}
        span.divider |
        van-button(@click="onReply(item.id)") 回复
        template(v-if="item.operations && item.operations.length > 0")
          van-button(@click="onEdit(item)" v-if="item.operations.includes('update')") 修改
          template(v-if="item.operations.includes('destroy')")
            van-button(@click="onDelete(item.id)") 删除
        template(v-else-if="item.user_id === userInfo.id")
          van-button(@click="onEdit(item)") 修改
          van-button(@click="onDelete(item.id)") 删除
      .reply-list(v-if="item.comments && item.comments.length")
        ReplyList(
          :comments="item.comments"
          :disabled="disabled"
          @edit="onEdit"
          @reply="reply"
          @destroy="destroy")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    ReplyList: () => import('./CommentTree.vue'),
  },
})
export default class CommentTree extends Vue {
  @Prop({ type: Array, default: () => [] }) private comments?: any;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;

  private comment: any = {
    body: '',
    attachments: {},
    files: [],
    parent_id: '',
  };
  get userInfo() {
    return sessionStore.currentUser || {};
  }

  public onEdit(val: any) {
    this.$emit('edit', { ...val });
  }

  public onDelete(id: number) {
    this.$dialog.confirm({
      title: '提示',
      message: '确定要继续执行此操作吗?',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          this.$emit('destroy', id);
          done();
        } else {
          done();
        }
      },
    });
  }

  public onReply(id: number) {
    this.comment = {
      parent_id: id,
      body: '',
      attachments: {},
      files: [],
    };
    this.$emit('reply', this.comment);
  }

  // 递归组件事件
  public reply(val: any) {
    this.$emit('reply', val);
  }

  public destroy(id: any) {
    this.$emit('destroy', id);
  }
}
</script>

<style lang="stylus" scoped>
.van-list
  width 100%
  .comment-cell
    padding 16px 0px
    width 100%
    border-top 1px #e8e8e8 solid
    &:first-child
      border none
    .comment-top
      display flex
      justify-content space-between
      align-items center
      width 100%
      .name
        margin-left 8px
        strong
          font-size 16px
          line-height 20px
        .name-type
          margin-left 4px
          color #999999
          font-size 16px
          line-height 20px
    .comment-middle
      display flex
      flex-wrap wrap
      align-items center
      margin 10px 0px
      padding-left 36px
      width 100%
      color #383838
      font-size 14px
      line-height 20px
      .comment-body
        font-size 15px
        font-weight 400
        color rgba(128,128,128,1)
        line-height 24px
      .reply
        color #b2b2b2
      .nicknane
        margin-left 4px
        color #333
        font-weight 500
    .comment-bottom
      padding-left 36px
      .operation-box
        display flex
        width 100%
        .divider
          margin 0 2px 0 10px
        .van-button
          padding 0px
          width 44px
          height 20px
          border 0px
          text-align center
          background transparent
          line-height 20px
      .reply-list
        background rgba(250,250,250,1)
        padding 0 12px
    span
      color #808080
      font-size 14px
      line-height 20px
</style>
