<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { CommentService } from '@/service/comment';
import { IFile } from '@/models/file';

@Component
export default class CommentSender extends Vue {
  private content: string = '';
  private attachments: any[] = [];
  private emojiVisible: boolean = false;
  private isFileAllSettled: boolean = true;
  private loading: boolean = false;
  private keyboardVisible: boolean = false;
  private isSendButton: boolean = false;

  @Prop({ type: Number }) parentId!: number;
  @Prop({ type: Number }) commentableId!: number;
  @Prop({ type: String }) commentableType!: string;
  @Prop({ type: String, default: '添加评论' }) placeholder!: string;
  @Prop({ type: String }) commentsContainerId!: string;
  @Prop({ type: Boolean, default: false }) useCdn!: boolean;

  isCommentable() {
    return (
      this.commentableId && this.commentableType && (this.content || (this.attachments.length && this.isFileAllSettled))
    );
  }
  handleKeypress(e: KeyboardEvent) {
    if (e.key === 'Enter') {
      e.preventDefault();
      this.insertChar('\n');
      return false;
    }
    return true;
  }
  async send() {
    // 键盘弹出情况下，防止失焦造成键盘收回
    this.isSendButton = true;
    setTimeout(() => {
      this.isSendButton = false;
    }, 200);
    if (this.keyboardVisible) {
      this.setInputFocus();
    }
    // sending
    if (!this.isFileAllSettled) {
      this.$message.warning('存在没有上传完成的附件');
      return;
    }
    if (this.isCommentable()) {
      this.$emit('beforeSend');
      this.loading = true;
      const { data } = await CommentService.teaching
        .create({
          title: '',
          body: this.content,
          subject: '',
          parent_id: this.parentId,
          commentable_id: this.commentableId,
          commentable_type: this.commentableType,
          attachments: {
            files: this.attachments.filter((file: IFile) => file.status === 'done'),
          },
        })
        .finally(() => {
          this.loading = false;
        });
      this.content = '';
      this.attachments = [];
      this.$emit('add', data);
      this.afterAddComment();
    }
  }
  restartUpload(fileItem: any) {
    (this.$refs.uploader as any).start(fileItem);
  }
  insertChar(char: string, callback?: () => any) {
    const textarea = (this.$refs.senderInput as any) as HTMLTextAreaElement;
    const start = textarea.selectionStart;
    this.content = this.content.slice(0, start) + char + this.content.slice(start);
    this.$nextTick(() => {
      textarea.selectionEnd = start + char.length;
      textarea.selectionStart = start + char.length;
    });
    if (callback) {
      callback();
    }
  }
  handlePaste(e: ClipboardEvent) {
    const { items } = e.clipboardData as DataTransfer;
    if (items.length) {
      for (let index = 0; index < items.length; index += 1) {
        const item = items[index];
        if (item.type.includes('image')) {
          const file = item.getAsFile();
          (this.$refs.uploader as any).addFile(file);
        }
      }
    }
  }
  afterAddComment() {
    if (this.commentsContainerId) {
      this.$nextTick(() => {
        const parentDom = document.getElementById(this.commentsContainerId);
        if (parentDom) {
          parentDom.scrollTop = parentDom.scrollHeight;
        }
      });
    }
  }
  onInputFocus() {
    this.keyboardVisible = true;
  }
  onInputBlur() {
    setTimeout(() => {
      if (!(this.keyboardVisible && this.isSendButton)) {
        this.resumeKeyboardHeight();
      }
    }, 20);
  }
  setInputFocus() {
    if (this.$refs.senderInput) {
      (this.$refs.senderInput as any).focus();
    }
  }
  resumeKeyboardHeight() {
    this.$nextTick(() => {
      window.scrollTo({
        left: 0,
        top: 0,
        behavior: 'smooth',
      });
    });
  }
}
</script>

<template lang="pug">
.comment-sender
  .comment__wrap
    .comment__input
      .faker {{ content }}
      textarea.textarea(
        ref="senderInput"
        v-model.trip="content"
        :placeholder="placeholder"
        @paste="handlePaste"
        @keypress="handleKeypress"
        @focus="onInputFocus"
        @blur="onInputBlur")
    Attachments(
      v-model="attachments"
      :showActions="true"
      @restart="restartUpload")
  .comment__actions
    .action
      FileUploader.uploader(
        ref="uploader"
        v-model="attachments"
        :useCdn="useCdn"
        :isAllSettled.sync="isFileAllSettled"
        :showList="false")
        van-button.file-button(size="small" icon="add-o" @click="resumeKeyboardHeight")
    .action
      van-button.send-button(
        type="info"
        size="small"
        @click="send"
        :loading="loading")
        | 发送
</template>

<style lang="stylus" scoped>
.comment-sender
  padding 8px 6px 8px 8px
  width 100%
  display flex
  background #FAFAFA
  align-items flex-start
  overflow hidden
  z-index 1000
  &:focus
    outline none
  .comment__wrap
    flex-grow 1
    width 100%
    background #fff
    padding 8px 6px 8px 8px
    .comment__input
      position relative
      height fit-content
      .faker
        visibility hidden
        margin 0
        padding 0
        min-height 20px
        line-height 20px
        width 100%
        white-space pre-wrap
        word-break break-all
        overflow hidden
      .textarea
        position absolute
        top 0
        left 0
        margin 0
        padding 0
        width 100%
        height 100%
        outline 0 none
        border none
        border-radius 0
        white-space pre-wrap
        word-break break-word
        line-height 20px
        resize none
        overflow hidden
  .comment__actions
    flex-shrink 0
    display flex
    justify-content space-between
    align-items center
    .action
      padding 3px 6px
    .uploader
      padding 0
    .file-button
      padding 0
      width 30px
      min-width 30px
      border-radius 50%
      line-height 1
      font-size 26px
      border none
</style>
