<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

import commentStore from '@/store/modules/comm/comment.store';
import CommentTree from './CommentTree.vue';

@Component({
  components: {
    CommentTree,
  },
})
export default class CommentPanel extends Vue {
  @Prop({ type: Number }) commentableId!: number;
  @Prop({ type: String }) commentableType!: string;
  @Prop({ type: String, default: 'List' }) commentType!: string; // List, Tree
  @Prop({ type: Boolean, default: true }) showHeader!: boolean;
  @Prop({ type: Boolean, default: false }) useCdn!: boolean;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  // tree
  @Prop({ type: String, default: '' }) indexPath?: string; // 如： /teaching/user/topics
  @Prop({ type: String, default: '' }) deletePath?: string; // 如： /teaching/user
  @Prop({ type: Number, default: 20 }) perPage?: number;

  uploadState: boolean = true;
  commentStore: any = {
    records: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 1,
  };
  loading: boolean = false;
  visibleForm: boolean = false;
  comment: any = {
    body: '',
    attachments: {},
    files: [],
  };

  get commentable() {
    return !this.comment.body && this.comment.files.length === 0;
  }
  // tree
  mounted() {
    if (this.commentType === 'Tree') {
      this.fetchData();
    }
  }

  async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: this.perPage,
      parentPath: `${this.indexPath}/${this.commentableId}`,
    };
    this.loading = true;
    const { data } = await commentStore.fetchByParent(params).finally(() => {
      this.loading = false;
    });
    this.commentStore = {
      ...data,
      totalCount: data.total_count,
      currentPage: data.current_page,
      totalPages: data.total_pages,
      records: data.current_page === 1 ? data.comments : this.commentStore.records.concat(data.comments),
      finish: data.current_page === data.total_pages,
    };
  }

  onCreate() {
    this.comment = {
      body: '',
      attachments: {},
      files: [],
    };
    this.visibleForm = true;
  }

  onEdit(val: any) {
    this.comment = val;
    this.visibleForm = true;
  }

  onSubmit() {
    if (!this.uploadState) {
      this.$message.warning(this.$tools.fileVerifyMessage());
      return;
    }
    if (this.comment.id) {
      this.updateComment();
    } else {
      this.createComment();
    }
  }

  async createComment() {
    try {
      const obj: any = {
        commentable_type: this.commentableType,
        commentable_id: this.commentableId,
        ...this.comment,
      };
      await commentStore.create(obj);
      this.fetchData();
      this.visibleForm = false;
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  async updateComment() {
    try {
      const obj = {
        ...this.comment,
        noCompare: true,
      };
      await commentStore.update(obj);
      this.fetchData();
      this.visibleForm = false;
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  async deleteComment(id: number) {
    try {
      const params = {
        id,
        parentPath: this.deletePath,
      };
      await commentStore.deleteByParent(params);
      this.fetchData();
      this.$emit('refresh');
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.success('操作成功！');
    }
  }

  // 回复评论
  async onReply(val: any) {
    try {
      const obj = {
        ...val,
        commentable_id: this.commentableId,
        commentable_type: this.commentableType,
      };
      await commentStore.create(obj);
      this.fetchData();
      this.visibleForm = false;
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }
  // 创建评论刷新数据
  onRefresh() {
    this.fetchData();
    this.$emit('refresh');
  }
  // 附件
  onSuccess(fileItems: any[], statusFiles: any, allSettled: boolean) {
    Object.assign(this.comment, {
      files: fileItems,
      attachments: {
        files: statusFiles.done || [],
      },
    });
    this.uploadState = allSettled;
  }
}
</script>

<template lang="pug">
.comment-page(:style="{paddingBottom: visibleForm ? '160px' : '0px'}")
  ListView(
    :data="commentStore.records"
    :loading="loading"
    :pullup="!commentStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(commentStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .comment-list
      CommentTree(
        :comments.sync="commentStore.records"
        :disabled="disabled"
        @edit="onEdit"
        @reply="onEdit"
        @destroy="deleteComment")
  template(v-if="!disabled")
    .comment(@click="onCreate")
      van-icon(name="comment-o")
    .submit-card(v-if="visibleForm")
      .card-top(v-if="comment.files && comment.files.length > 0")
        Attachments(
          :attachments="comment.files"
          :showActions="true"
          @change="onSuccess")
      .card-middle
        van-field(
          v-model="comment.body"
          type="textarea"
          rows="2"
          maxlength="1000"
          show-word-limit
          :placeholder="comment.parent_id ? '添加回复' : '添加评论'")
      .card-bottom
        .attachment
          FileUploader(
            :value="comment.files"
            :multiple="true"
            :useCdn="useCdn"
            style="padding: 0px;"
            :showList="false"
            @change="onSuccess")
            van-button(type="info" plain) 上传附件
        .flex
          van-button(
            color="#bfbfbf" plain
            @click="visibleForm = false") 取消
          van-button(
            type="info"
            :loading="loading"
            :commentable="commentable || loading"
            @click="onSubmit") 确定
</template>

<style lang="stylus" scoped>
.comment-page
  width 100%
  height 100%
  background #fff
  .comment-list
    padding 0px 16px
    width 100%
  .submit-card
    position fixed
    bottom 0px
    left 0px
    z-index 99
    display flex
    flex-direction column
    justify-content flex-end
    min-height 160px
    width 100%
    border-top 1px #e8e8e8 solid
    background #fff
    box-shadow 0px 2px 12px 0px #eee
    .card-top
      overflow auto
      padding 10px 16px
      max-height 100px
      width 100%
      border-bottom 1px #e8e8e8 solid
    .card-middle
      width 100%
    .card-bottom
      display flex
      justify-content space-between
      padding 10px
      width 100%
      border-top 1px #e8e8e8 solid
      background #fff
      .van-button
        margin 0px 6px
        padding 0px
        width 80px
        height 36px
        font-size 14px
        line-height 32px
  .comment
    position fixed
    right 20px
    bottom 20px
    display flex
    justify-content center
    align-items center
    width 44px
    height 44px
    border-radius 50%
    background #fff
    box-shadow 0px 2px 4px 0px rgba(0, 0, 0, 0.1)
</style>
