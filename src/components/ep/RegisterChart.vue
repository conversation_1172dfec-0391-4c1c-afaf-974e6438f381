<template lang="pug">
.pannel
  .pannel-top
    .ring-box(v-if="register.total > 0")
      G2Pie(
        v-if="basePie && basePie.length"
        :charData.sync="basePie"
        :unit="pageType === 'Index' ? '人' : '次'"
        :colors="['#1FA0FF', '#CDCBCE']"
        id="A1")
    .count-box
      .count-item(v-for="(item, index) in infos" :key="index")
        .key {{ item.title }}
        .value
          strong.count {{ item.count }}
          strong.unit 次
  .pannel-middle
    .card(v-for="(item ,index) in registerQuestions" :key="index")
      .card-top
        .title {{ index + 1 }}. {{ item.title }}
      .card-middle
        G2Pie(
          v-if="item.basePie && item.basePie.length"
          :charData="item.basePie"
          :id="`Q${index + 1}`"
          :padding="[0, 56, 60, 56]"
          :unit="pageType === 'Index' ? '人' : '次'")
      .card-bottom(@click="onShow(item)" v-if="pageType === 'Index'")
        span 详情数据
        van-icon(name="arrow")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2Pie from '@/components/ep/G2Pie.vue';
import { inspectActivityStore } from '@/store/modules/ep/activity.store';

@Component({
  components: {
    G2Pie,
  },
})
export default class RegisterChart extends Vue {
  @Prop({ type: Object, default: () => ({}) }) private register?: any;
  @Prop({ type: Object, default: () => ({}) }) private question?: any;
  @Prop({ type: String, default: 'Index' }) private pageType?: string; // Index, Show

  get activity() {
    return inspectActivityStore.record || {};
  }
  get infos() {
    return [
      {
        title: '应打卡',
        key: 'total',
      },
      {
        title: '已打卡',
        key: 'done',
      },
      {
        title: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      ...item,
      count: this.register[item.key] || 0,
    }));
  }
  get basePie() {
    return [
      {
        label: '已打卡',
        key: 'done',
      },
      {
        label: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      state: item.label,
      amount: this.register[item.key] || 0,
      percent: +((this.register[item.key] / this.register.total) * 100).toFixed(2),
    }));
  }
  get questions() {
    return this.activity.meta && this.activity.meta.questions ? this.activity.meta.questions : [];
  }
  get registerQuestions() {
    return (this.questions || []).map((item: any) => ({
      ...item,
      basePie: this.initChartData(item),
    }));
  }

  public initChartData(val: any) {
    return (val.options || []).map((item: any) => ({
      state: item.value,
      amount: this.question[val.key] && this.question[val.key][item.key] && +this.question[val.key][item.key].number,
      percent: this.question[val.key] && this.question[val.key][item.key] && +this.question[val.key][item.key].rate,
    }));
  }

  public mounted() {
    inspectActivityStore.find(this.$route.params.id);
  }

  public onShow(val: any) {
    this.$emit('detail', val);
  }
}
</script>

<style lang="stylus" scoped>
.pannel
  width 100%
  background #fff
  .pannel-top
    width 100%
    .ring-box
      margin-top 24px
      width 100%
    .count-box
      display flex
      justify-content space-around
      align-items center
      width 100%
      .count-item
        margin-top 36px
        .key
          margin-bottom 8px
          color #A6A6A6
          font-weight 500
          font-size 12px
          line-height 14px
        .count
          color #383838
          font-weight 500
          font-size 30px
          font-family DINCond-Medium, DINCond
          line-height 30px
        .unit
          margin-left 4px
          color #808080
          font-size 12px
          line-height 18px
  .pannel-middle
    padding 30px 0px 8px
    width 100%
    .card
      margin-bottom 12px
      width 100%
      border 1px solid rgba(232, 232, 232, 1)
      border-radius 3px
      .card-top
        padding 14px 12px
        width 100%
        border-bottom 1px solid rgba(232, 232, 232, 1)
        .title
          color #383838
          font-size 14px
          line-height 20px
      .card-middle
        padding 10px
        width 100%
      .card-bottom
        padding 12px
        width 100%
        border-top 1px solid rgba(232, 232, 232, 1)
        color #808080
        text-align center
        font-size 14px
        line-height 20px
        .van-icon
          margin-left 4px
          color #A6A6A6
</style>
