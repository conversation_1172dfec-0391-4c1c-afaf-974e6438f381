<template lang="pug">
van-steps(direction="vertical")
  van-step(
    v-for="(item, index) in menus"
    :key="index"
    active-color="#07c160"
    :active="active"
    @click.native="onMenu(item)")
    template(slot="active-icon")
      van-icon(:name="item.icon" :color="activeColor" :style="{ fontSize: iconSize }")
    template(slot="inactive-icon")
      van-icon(:name="item.icon" :color="inactiveColor" :style="{ fontSize: iconSize }")
    .module
      .module-top
        .title(:style="{ color: activeColor }") {{ item.label }}
        .right
          van-tag(
            size="medium"
            :color="tagStyle(item.tag.type).color"
            :text-color="tagStyle(item.tag.type).textColor")
            | {{ item.tag.text }}
          van-icon(name="arrow" :color="inactiveColor")
      .module-middle
        slot(name="scope" :item="item")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class MenuList extends Vue {
  @Prop({ type: Array, default: () => [] }) private menus?: any;
  @Prop({ type: String, default: '#A6A6A6' }) private activeColor?: string;
  @Prop({ type: String, default: '#A6A6A6' }) private inactiveColor?: string;
  @Prop({ type: String, default: '18px' }) private iconSize?: string;
  @Prop({ type: Number, default: 10 }) private active?: string;
  get activeIndex() {
    return this.active || this.menus.length;
  }
  public onMenu(val: any) {
    this.$emit('click', val);
  }
  public tagStyle(type: string = 'info') {
    return ({
      info: {
        color: '#f5f5f5',
        textColor: '#808080',
      },
      primary: {
        color: '#EDF7FF',
        textColor: '#3DA8F5',
      },
      success: {
        color: '#F0F9F2',
        textColor: '#6DC37D',
      },
      warning: {
        color: '#ffe1e1',
        textColor: '#f2826a',
      },
      danger: {
        color: '#ffe1e1',
        textColor: '#ad0000',
      },
    } as any)[type];
  }
}
</script>

<style lang="stylus" scoped>
.module
  padding 0px 0px 10px 4px
  width 100%
  .module-top
    display flex
    justify-content space-between
    align-items center
    width 100%
    .right
      display flex
      flex-wrap nowrap
      justify-content flex-end
      align-items center
      min-width 120px
      .van-tag
        margin 0px 8px
  .module-middle
    margin-top 12px
    padding 20px
    background #FAFAFA
    &:empty
      display none

.van-step--vertical:not(:last-child)::after
  border none
</style>
