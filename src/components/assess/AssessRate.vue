<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class AssessRate extends Vue {
  @Model('change') private value!: any;
  @Prop({ type: Boolean, default: false }) private disabled!: boolean;
  @Prop({
    type: Array,
    default: () => [
      { label: '优秀', value: '优秀' },
      { label: '良好', value: '良好' },
      { label: '一般', value: '一般' },
      { label: '较差', value: '较差' },
    ],
  })
  options!: object[];

  get score() {
    return this.value;
  }
  set score(val: any) {
    this.$emit('change', val);
  }

  setScore(val: any) {
    this.score = val;
  }
}
</script>

<template lang="pug">
van-radio-group(v-model="score" :disabled="disabled")
  .assess-rate
    .rate-radio(v-for="option in options" :key="option.value" @click="setScore(option.value)")
      van-radio.rate-radio-button(:name="option.value")
      .rate-label {{ option.label }}
</template>

<style lang="stylus" scoped>
.assess-rate
  display flex
  border 1px solid rgba(232, 232, 232, 1)
  border-radius 3px
  background rgba(255, 255, 255, 1)
  .rate-radio
    flex-grow 1
    padding 14px 0 12px
    width 25%
    border-right 1px solid rgba(232, 232, 232, 1)
    text-align center
    cursor pointer
    &:last-child
      border none
    .rate-radio-button
      display inline-block
      text-align center
    .rate-label
      margin-top 6px
      color rgba(38, 38, 38, 0.65)
      text-align center
      font-size 14px
</style>
