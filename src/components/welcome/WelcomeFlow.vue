<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { IWorkflowCorePlace, TransitionTypes } from '@/models/bpm/workflow';
import tokenModel, { IToken } from '@/models/bpm/token';
import TemplateFormViewer from '@/components/form/TemplateFormViewer.vue';

@Component({
  components: {
    TemplateFormViewer,
  },
})
export default class RegistrationFlow extends Vue {
  @Prop({ type: Array, default: () => [] }) tokens!: IToken[];
  @Prop({ type: Array, default: () => [] }) places!: IWorkflowCorePlace[];

  get TransitionTypes() {
    return TransitionTypes;
  }
  get placeTokenMap() {
    return this.$utils.objectify(this.tokens, 'place_id') as any;
  }

  isCompleted(place: IWorkflowCorePlace) {
    const token: IToken = this.placeTokenMap[place.id!];
    return token && token.state === 'completed';
  }
  isProcessing(place: IWorkflowCorePlace) {
    const token: IToken = this.placeTokenMap[place.id!];
    return token && token.state === 'processing';
  }
  placeNameWithToken(place: IWorkflowCorePlace) {
    const token: IToken = this.placeTokenMap[place.id!];
    return token && token.name ? token.name : place.name;
  }
  onClick(place: IWorkflowCorePlace) {
    const token: IToken = this.placeTokenMap[place.id!];
    // if (token && token.options && token.options.url) {
    //   const redirecturi = 'http://wchattest.stiei.edu.cn/campus-mobile/studying/welcome';
    //   window.open(`${token.options.url}&redirecturi=${encodeURIComponent(redirecturi)}`, '_blank');
    // } else if (token) {
    if (token) {
      this.$emit('tokenClick', token);
    }
  }
}
</script>

<template lang="pug">
.registration-flow
  van-steps(direction='vertical')
    van-step.place.timeline-item(v-for='(place, index) in places', :key='place.id')
      img.timeline-icon(src='@/assets/images/welcome/icon_checked.png', v-if='isCompleted(place)')
      img.timeline-icon(src='@/assets/images/welcome/icon_info.png', v-else-if='isProcessing(place)')
      img.timeline-icon.gray-icon(src='@/assets/images/welcome/icon_info.png', v-else)
      //- 新生处理
      .place-content(
        @click.stop='onClick(place)',
        v-if='place.transition_type === TransitionTypes.ApprovalSponsorSelf'
      )
        .place-header
          .place-title(:class='{ "text-primary": isProcessing(place) }')
            | {{ placeNameWithToken(place) }}
          .place-state
            template(v-if='isCompleted(place)')
              span 查看
              van-icon(name='arrow')
            template(v-else-if='isProcessing(place)')
              span 待提交
              van-icon(name='arrow')
            template(v-else)
              span 未开始
        .place-information
          .place-tips(v-if='isCompleted(place)')
            | 已提交
          .place-tips(v-else-if='isProcessing(place)')
            | 点击提交资料
      //- 教师审核
      .place-content.content-disabled(
        @click.stop='onClick(place)',
        v-else-if='place.transition_type === TransitionTypes.ApprovalUser'
      )
        .place-header
          .place-title {{ place.name }}
          .place-state
            template(v-if='isCompleted(place)')
              span 查看
            template(v-else-if='isProcessing(place)')
              span 处理中
            template(v-else)
              span 待处理
      .place-content.content-disabled(v-else)
        .place-header
          .place-title {{ place.name }}
          .place-state
            template(v-if='isCompleted(place)')
              span 已通过
            template(v-else-if='isProcessing(place)')
              span 处理中
            template(v-else)
              span 待处理
</template>

<style lang="stylus" scoped>
.registration-flow
  padding-right 6px
  padding-left 12px
  .timeline-item
    padding-top 16px
    padding-bottom 16px
    min-height 24px
  .place
    position relative
    .timeline-icon
      position absolute
      top 12px
      left -32px
      z-index 999
      width 32px
      height 32px
    .gray-icon
      filter grayscale(100%)
    .place-content
      padding-left 16px
      .place-header
        display flex
        justify-content space-between
        align-items center
        margin-bottom 12px
        line-height 24px
        .place-title
          color rgba(56, 56, 56, 1)
          font-size 16px
        .place-state
          color rgba(166, 166, 166, 1)
          font-size 14px
          line-height 22px
          span
            vertical-align baseline
      .place-information
        margin-bottom 0
        color rgba(166, 166, 166, 1)
        font-size 14px
        line-height 20px
        .place-tips
          margin-bottom 10px
          .tips
            display block
            margin-bottom 0
            white-space pre-wrap
            word-wrap break-word
    .content-disabled
      .place-header
        .place-title
          color #888888
</style>
