<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import { ITofu, TofuApp } from '@/models/tofu/app';
import { TofuStar } from '../../models/tofu/star';

@Component({
  components: {},
})
export default class Tofu extends Vue {
  @Prop({ type: Object, default: {}, required: true }) private tofu!: ITofu;
  @Prop({ type: Boolean, default: false, required: false }) private starVisible!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private subscript!: boolean;
  @Prop({ type: Boolean, default: false, required: false }) private websiteImage!: boolean;
  @Prop({ type: String, default: '12px', required: false }) private titleSIze!: string;

  clickTofu() {
    this.$emit('clickTofu', this.tofu);
  }
}
</script>

<template lang="pug">
.tofu
  .shell(@click="clickTofu")
    .right-top
      slot(name="rightTop" :tofu="tofu")
    .images
      .main-image-shell
        .website-image(v-if="websiteImage")
          img(:src="tofu.image")
        .main-image(v-else)
          img(v-if="tofu.image" :src="tofu.image")
          img(v-else src="@/assets/icons/portal/tofu_placeholder.png")
          .subscript(v-if="subscript && tofu.top_tofu_image !== null")
            img(:src="tofu.top_tofu_image || require('../../assets/icons/portal/tofu_placeholder.png')")
        .instance-count(v-if="tofu.instance_count !== null && tofu.instance_count !== 0")
          .count {{ tofu.instance_count }}
    .titles
      .one-title(v-if="tofu.top_tofu_name === undefined || tofu.top_tofu_name === null")
        .title(:style="{ fontSize: titleSIze } ") {{ tofu.name }}
      .two-title(v-else)
        .title(:style="{ fontSize: titleSIze } ") {{ tofu.name }}
        .sub-title.title {{ tofu.top_tofu_name }}
</template>

<style lang="stylus" scoped>
.tofu
  position relative
  width 100%
  height 100%
  border-radius 8px
  background-color white
  .shell
    width 100%
    height 100%
    .right-top
      position absolute
      top 5%
      right 8%
      z-index 999
    .images
      display flex
      justify-content center
      height 58%
      .main-image-shell
        position relative
        display flex
        justify-content center
        align-items center
        padding-top 10%
        width 40%
        height 100%
        .main-image
          position relative
          width 30px
          height 30px
          img
            width 100%
          .subscript
            position absolute
            right -5px
            bottom -3px
            margin 0
            width 14px
            height 14px
            border 3px solid white
            border-radius 20%
            img
              z-index 99
              width 100%
        .website-image
          display flex
          justify-content center
          align-items center
          width 30px
          height 30px
          border-radius 60%
          background red
          -moz-border-radius 60%
          -webkit-border-radius 60%
        .instance-count
          position absolute
          top 20%
          right -20%
          width 20px
          border-radius 10px
          background-color #F95051
          color white
          text-align center
          line-height 20px
          -moz-border-radius 10px
    .titles
      height 42%
      font-size 12px
      .title, .one-title
        display flex
        justify-content center
        min-height 20px
        height 100%
      .one-title
        padding 10% 0
      .two-title
        padding-top 3%
        .sub-title
          color #A6A6A6
          font-size 10px
</style>
