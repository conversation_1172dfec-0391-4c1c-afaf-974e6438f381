<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator';
import { IActivityMeeting, ActivityMeeting } from '@/models/conference/activity_meeting';

@Component({
  components: {},
})
export default class ActivityMeetingCard extends Vue {
  @Model('input', { type: Boolean, default: false }) private value!: boolean;
  @Prop({ type: Object, default: () => ({}), required: true }) private record!: IActivityMeeting;
  @Prop({ type: Boolean, default: true }) private roomVisiable!: boolean;
  @Prop({ type: Boolean, default: true }) private tagVisible!: boolean;
  @Prop({ type: Boolean, default: true }) private lockVisible!: boolean;

  greenBackground: string = '#F0F9F2';
  greenFont: string = '#6DC37D';
  blueBackground: string = '#EDF7FF';
  blueFont: string = '#3DA8F5';
  orangeBackground: string = '#FFF7E6';
  orangeFont: string = '#FA8C15';
  grayBackground: string = '#F5F5F5';
  grayFont: string = '#A6A6A6';

  get isModerator() {
    return this.record.is_moderator;
  }

  get isJoined() {
    return this.record.is_joined;
  }

  get isInvited() {
    return this.record.is_invited;
  }

  get isReplacement() {
    return this.record.is_replacement;
  }

  get isLeaveing() {
    return this.record.is_leaving;
  }

  get isInviting() {
    return this.record.is_inviting;
  }

  get leavingBackground() {
    switch (this.isLeaveing) {
      case '审核中':
        return this.orangeBackground;
      case '通过':
        return this.greenBackground;
      case '未通过':
        return this.grayBackground;
      default:
        break;
    }
    return this.grayBackground;
  }

  get leavingFont() {
    switch (this.isLeaveing) {
      case '审核中':
        return this.orangeFont;
      case '通过':
        return this.greenFont;
      case '未通过':
        return this.grayFont;
      default:
        break;
    }
    return this.grayBackground;
  }

  get invitingBackground() {
    switch (this.isInviting) {
      case '审核中':
        return this.orangeBackground;
      case '通过':
        return this.greenBackground;
      case '未通过':
        return this.grayBackground;
      default:
        break;
    }
    return this.grayBackground;
  }

  get invitingFont() {
    switch (this.isInviting) {
      case '审核中':
        return this.orangeFont;
      case '通过':
        return this.greenFont;
      case '未通过':
        return this.grayFont;
      default:
        break;
    }
    return this.grayBackground;
  }

  get meetingTime() {
    return `${this.$dayjs(this.record.begin_time).format('HH:mm')} - ${this.$dayjs(this.record.end_time).format(
      'HH:mm',
    )}`;
  }
  mounted() {
    if (this.record.is_over) {
      this.greenBackground = this.grayBackground;
      this.greenFont = this.grayFont;
      this.blueBackground = this.grayBackground;
      this.blueFont = this.grayFont;
      this.orangeBackground = this.grayBackground;
      this.orangeFont = this.grayFont;
    }
  }
}
</script>

<template lang="pug">
.activity-meeting-card(:class="record.is_over ? 'disable': 'enable'")
  span.is-over(v-if="record.is_over") 已结束
  span.lock(v-if="lockVisible && record.state == 'reviewed' && !record.is_over")
    van-icon(type="lock" theme="filled")
  .body
    .title
      | {{ record.title }}
    .content
      .time
        .label 时间
        .text
          | {{ `${this.$dayjs(record.date).format('MM/DD')} ${meetingTime}` }}
      .room
        .label 地点
        .text
          | {{ record.meeting_room_name }}
      .moderator
        .label 主持人
        .text
          | {{ (record.moderator_names || []).join('、') }}
      .participant
        .label 参加者
        .text
          | {{ record.user_desc || (record.user_names || []).join('、') }}
      .participant
        .label 组织部门
        .text
          | {{ record.department_names }}
  .footer(v-show="tagVisible")
    .tags
      slot(name="tags" :meeting="record")
        .moderator-tag(v-if="isModerator" :style="{ color: greenFont, background: greenBackground }")
          van-icon(name="bullhorn")
          span 我主持的
        .participant-tag(
          v-if="isJoined && (!isInvited || !isReplacement)"
          :style="{ color: greenFont, background: greenBackground }"
        )
          van-icon(name="friends")
          span 我参与的
        .participant-invited-tag(v-if="isInvited" :style="{ color: greenFont, background: greenBackground }")
          van-icon(name="friends")
          span 我参与的-受邀
        .participant-invited-tag(v-if="isReplacement" :style="{ color: greenFont, background: greenBackground }")
          van-icon(name="friends")
          span 我参与的-替换
        .is-leaving-tag(v-if="isLeaveing" :style="{ color: leavingFont, background: leavingBackground }")
          van-icon(name="friends")
          span {{ `我要请假-${isLeaveing}` }}
        .is-inviting-tag(v-if="isInviting" :style="{ color: invitingFont, background: invitingBackground }")
          van-icon(name="friends")
          span {{ `我要邀请-${isInviting}` }}
    //- .space
    .more
      slot(name="more" :meeting="record")

</template>

<style lang="stylus" scoped>
.activity-meeting-card
  overflow hidden
  width 100%
  border 1px solid #E8E8E8
  background-color white
  border-radius 4px
  padding 16px
  .is-over
    float right
    margin -5px -45px
    width 100px
    background #E5E5E5
    color #A6A6A6
    text-align center
    font-size 10px
    transform rotateZ(45deg)
  .lock
    float right
    margin 10px 10px
    color red
  .body
    width 100%
    .title
      // overflow hidden
      width 80%
      // text-overflow ellipsis
      // white-space nowrap
      white-space wrap
      word-wrap: break-word
      font-weight bold
      font-size 18px
    .content
      padding-top 10px
      .time, .room, .moderator, .participant
        display flex
        justify-content flex-start
        padding 3px 2px
        .label
          width 70px
        .text
          width 80%
          text-overflow ellipsis
          white-space nowrap
  .footer
    display flex
    justify-content space-between
    padding-top 8px
    .tags
      display flex
      justify-content flex-start
      .moderator-tag, .participant-tag, .participant-invited-tag, .is-leaving-tag, .is-inviting-tag
        margin 0px 4px
        padding 3px 4px
        border-radius 3px
        span
          font-size 12px
    .more
      line-height 30px
  .icon
    margin 2px
    font-size 17px
  .text
    overflow hidden
    padding 3px 2px
    text-overflow ellipsis
    white-space nowrap
    font-size 14px
    line-height 14px
  .more-icon
    display none

.activity-meeting-card.disable
  .icon
    color #CCCCCC
  .text
    color #CCCCCC
  .label
    color #CCCCCC
  .title
    color #A6A6A6

.activity-meeting-card.enable:hover
  .header
    .title
      color #3DA8F5
  .more-icon
    display block
  .more-icon:hover
    color #3DA8F5
</style>
