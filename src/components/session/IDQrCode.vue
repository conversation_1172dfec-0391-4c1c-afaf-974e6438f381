<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import session from '@/models/session';

@Component
export default class IDQrCode extends Vue {
  idCode: string = '';
  loading: boolean = false;
  timer: any = null;
  @Prop({ type: String, default: '#fff' }) background!: string;

  created() {
    this.fetchCode();
    this.timer = setInterval(this.fetchCode, 13000);
  }

  beforeDestroy() {
    clearInterval(this.timer);
  }

  async fetchCode() {
    try {
      this.loading = true;
      const { data } = await session.getIdCode();
      this.$emit('fetched');
      this.idCode = data.code;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
}
</script>

<template lang="pug">
.id-qrcode(:style="{ backgroundColor: background }")
  QrCode(
    v-loading="loading"
    :data="idCode"
    level="H"
    :background="background"
    @click.native="fetchCode")
</template>

<style lang="stylus" scoped>
.id-qrcode
  padding 2px
</style>
