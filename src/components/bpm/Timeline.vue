<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import instance from '@/models/bpm/instance';
import { TokenTypes } from '@/models/bpm/token';

@Component
export default class InstanceTimeline extends Vue {
  @Prop({ type: Array, default: () => [], required: true }) readonly tokens!: any[];

  get statusMap() {
    return instance.stateMap;
  }
  get formatedTokens() {
    return this.tokens.map(t => ({
      ...t,
      extra: this.getTokenIdentify(t),
    }));
  }

  getTokenIdentify(token: any) {
    const { type, state, previous_id: previousId } = token;
    let extra: any = { key: 'approval', title: '审批' };
    if (type === TokenTypes.Token && previousId) {
      extra = { key: 'end', title: '结束' };
    } else if (type === TokenTypes.Submit) {
      extra = { key: 'start', title: '提交' };
    } else if (type === TokenTypes.Notify) {
      extra = { key: 'notify', title: '抄送' };
    } else if (type === TokenTypes.Approval && state === 'preparing') {
      extra = { key: 'assign', title: '指派' };
    } else if (type === TokenTypes.Approval && state === 'failed') {
      extra = { key: 'approval', title: '审批' };
    } else if (type === TokenTypes.Approval && state === 'rejected') {
      extra = { key: 'approval', title: '审批' };
    } else if (type === TokenTypes.Approval && state === 'canceld') {
      extra = { key: 'approval', title: '审批' };
    }
    extra.type = this.statusMap[token.state].type;
    return extra;
  }
}
</script>

<template lang="pug">
van-steps(direction="vertical" :active="formatedTokens.length")
  van-step(
    v-if="token.type !== 'Tokens::Condition'"
    v-for="(token, index) in formatedTokens"
    :key="index")
    .timeline-content
      .timeline-title {{ token.name || token.extra.title }}
      .timeline-time
        span {{ token.updated_at | format }}
        .tag(:class="`tag-${token.extra.type}`")
          | {{ statusMap[token.state].label}}
      .timeline-action(:class="token.state")
        //- 抄送
        template(v-if="token.extra.key === 'notify'")
          van-tag(type="primary" v-for="user in (token.options.users || [])" :key="user.id")
            | {{ user.name }}
        //- 审批
        template(v-else)
          Avatar.photo.avatar(:src="token.operator_avatar" size="24px" name="token.operator_name")
            | {{ token.operator_name }}
          .action-info
            .operator {{ token.operator_name || '系统' }}
            .comment(v-if="token.comment") {{ token.comment }}
            .comment(v-if="token.options && token.options.publish_at")
              | 发布时间：{{ token.options.publish_at | format }}
</template>

<style lang="stylus" scoped>
.status-icon
  font-size 24px

.timeline-content
  padding-left 8px
  .timeline-title
    color #383838
    font-size 14px
    line-height 22px
    font-weight bold
  .timeline-time
    display flex
    justify-content space-between
    align-items center
    margin-bottom 6px
    color #000
    line-height 20px
    span
      font-size 14px
    .tag
      padding 0 6px
      height 20px
      font-size 12px
  .timeline-action
    display flex
    padding 8px
    background #F5F5F5
    .photo
      margin-right 8px
      flex-shrink 0
    .action-info
      .operator
        height 24px
        font-weight bold
        line-height 24px
      .comment
        margin-top 8px
        color #666666
        // white-space pre
        font-size 12px
        word-break break-all
  .accepted
    background rgb(228, 253, 218)
  .rejected
    background #FEF7E8
  .terminated
    background rgb(255, 236, 228)
</style>
