<script lang="ts">
import { Component, Vue, Prop, Model } from 'vue-property-decorator';
import instance, { IInstance } from '../../models/bpm/instance';

@Component({
  components: {},
})
export default class InstanceCell extends Vue {
  loading: boolean = false;

  @Model('change', {
    type: Object,
    default: () => ({
      flowable_info: {},
    }),
  })
  instance!: IInstance;

  get stateMap() {
    return instance.stateMap;
  }

  handleClick(...args: any) {
    this.$emit('click', ...args);
  }
}
</script>

<template lang="pug">
.instance-cell(@click="handleClick")
  .title.flex-between
    span {{ instance.workflow_name || '-' }}
    .tag(:class="stateMap[instance.state].class")
      | {{ stateMap[instance.state].label }}
  .infos
    .property
      .label 编号
      span {{ instance.seq }}
    .property
      .label 发起人
      span {{ instance.creator_name }}
    .property
      .label 申请时间
      strong {{ instance.created_at | format }}
</template>

<style lang="stylus" scoped>
.instance-cell
  margin-bottom 12px
  padding 16px 16px 0
  border-radius 4px
  background rgba(255, 255, 255, 1)
  font-weight 500
  font-size 14px
  .title
    color rgba(56, 56, 56, 1)
    font-size 16px
    line-height 24px
  .label
    display inline-block
    width 68px
    color rgba(166, 166, 166, 1)
    line-height 20px
  .infos
    padding 4px 0
    .property
      margin 8px 0
      line-height 20px
      span
        color rgba(166, 166, 166, 1)
        line-height 20px
</style>
