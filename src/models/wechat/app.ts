import BaseModel from '../BaseModel';

export class App extends BaseModel<IApp, IResponse> {
  constructor() {
    super({
      name: 'wechat_app',
      resource: 'apps',
      namespace: '/wechat/admin',
    });
  }
}

export default new App();

interface IResponse {
  wechat_apps: IApp[];
}

export interface IApp {
  id: string;
  appid: string;
  secret: string;
  name: string;
  desc: string;
}
