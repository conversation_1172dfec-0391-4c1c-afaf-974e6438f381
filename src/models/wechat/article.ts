import sessionStore from '@/store/modules/session.store';
import BaseModel from '../BaseModel';

export class Article extends BaseModel<IArticle, IResponse> {
  appId: string = '';

  constructor(args: { appId: string }) {
    super({
      name: 'item',
      resource: 'articles',
      namespace: '/wechat/teacher',
      parentResource: 'apps',
    });
    this.appId = args.appId;
  }

  getImageUrl(mediaId: string) {
    return `${this.request.defaults.baseURL}/${this.parentResourcePath}/${this.appId}/${this.resource}/
    ${mediaId}?image=1&school_id=${sessionStore.schoolId}`;
  }

  findByApp(mediaId: string) {
    return this.request.get<IArticleContent>(`${this.parentResourcePath}/${this.appId}/${this.resource}/${mediaId}`, {
      params: {
        school_id: sessionStore.schoolId,
      },
    });
  }

  updateNewsItem(updateFormData: IUpdateArticle) {
    return this.request.patch(`${this.parentResourcePath}/${this.appId}/${this.resource}/${updateFormData.media_id}`, {
      article: updateFormData,
    });
  }
}

interface IUpdateArticle {
  media_id: string;
  index: number;
  articles: INewsItem;
}

interface IResponse {
  id: any;
  item: IArticle[];
  total_count: number;
  item_count: number;
}

export interface INewsItem {
  title?: string;
  author?: string;
  digest?: string;
  content?: string;
  content_source_url?: string;
  thumb_media_id?: string;
  show_cover_pic?: number;
  url?: string;
  thumb_url?: string;
  need_open_comment?: number;
  only_fans_can_comment?: number;
}

export interface IArticleContent {
  news_item: INewsItem[];
  create_time?: number;
  update_time?: number;
}

export interface IArticle {
  id?: number;
  media_id?: string;
  content: IArticleContent;
  update_time?: number;
}
