import BaseModel, { IModelConfig } from '../BaseModel';

export interface IConferenceRoom {
  id?: number;
  uid?: number;
  name?: string;
  state?: string;
  building?: string;
  meta?: string;
  created_at?: string;
  updated_at?: string;
}

export interface IResponse {
  meeting_rooms: IConferenceRoom[];
}

export class ConferenceRoom extends BaseModel<IConferenceRoom, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'meeting_room',
      indexKey: 'meeting_rooms',
      resource: 'rooms',
      namespace: '/meeting/teacher',
      ...config,
    });
  }
}

export default new ConferenceRoom();
