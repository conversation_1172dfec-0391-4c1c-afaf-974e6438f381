import BaseModel from '@/models/BaseModel';

export interface IMod {
  id?: number;
  title?: string;
  type?: string;
  position?: number;
  inform_mod_id?: number;
  created_at?: string;
  updated_at?: string;
}

interface IResponse {
  mods: IMod[];
}

export class InformMod extends BaseModel<IMod, IResponse> {
  constructor() {
    super({
      name: 'mod',
      resource: 'mods',
      namespace: '/inform',
      indexKey: 'inform_mods',
      role: 'user',
    });
  }
}

export default new InformMod();
