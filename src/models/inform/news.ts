import BaseModel from '@/models/BaseModel';

export interface INews {
  id?: number;
  type?: string;
  title?: string;
  content?: string;
  cover_image?: IObject[];
  banners?: IObject[];
  position?: number;
  inform_mod_id?: number;
  created_at?: string;
  updated_at?: string;
}

interface IResponse {
  news: INews[];
}

export class InformNews extends BaseModel<INews, IResponse> {
  constructor() {
    super({
      name: 'news',
      resource: 'news',
      namespace: '/inform',
      role: 'user',
    });
  }
}

export default new InformNews();
