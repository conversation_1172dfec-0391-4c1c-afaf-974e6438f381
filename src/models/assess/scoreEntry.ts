// 被考核人
import BaseModel from '../BaseModel';
import { IQuestionSet } from './questionSet';
import { ITeacher } from '../teaching/teacher';
import { IScore } from './score';
import { IEntryMeta } from './selfEntry';

/**
 * score_config:
 *  1. leader: 小项不必填，总分必填
 *  2. manual: 所有项都必填，总分和小项分数独立填写
 *  3. normal: 所有项都必填，总分禁用，由小项目计算平均分得到
 */
export interface IEntry {
  id?: number;
  teacher_id?: number;
  access_activity_id?: number;
  readonly meta?: IObject;
  state?: string;
  entry_meta?: IEntryMeta;
  teacher: ITeacher;
  exam?: IQuestionSet[]; // 显示题目
  access_score?: IScore;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
  score_config?: 'leader' | 'manual' | 'normal';
  score_count?: number; // 已打分人数
}

interface IResponse {
  access_entries: IEntry[];
}

export class Entry extends BaseModel<IEntry, IResponse> {
  constructor() {
    super({
      name: 'access_entry',
      resource: 'score_entries',
      namespace: '/access/teacher',
      parentResource: 'score_activities',
    });
  }
}

export default new Entry();
