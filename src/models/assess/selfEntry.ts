// 自评模型
import BaseModel from '../BaseModel';
import { <PERSON>eacher } from '../teaching/teacher';
import { IQuestionSet } from './questionSet';
import { IFile } from '../file';

export interface ISelfEntry {
  id?: number;
  state?: string;
  teacher_id?: number;
  teacher?: ITeacher;
  access_activity_id?: number;
  total_score?: number;
  score?: number;
  readonly meta?: IObject;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
  exam?: IQuestionSet[]; // question-set 列表
  entry_meta?: IEntryMeta; // 自评总分，必须填写
}

interface IResponse {
  access_entries: ISelfEntry[];
}

export interface IEntryMeta {
  total: string; // 总体评价
  duty: string; // 职务
  work: string; // 从事与分管工作
  question_count?: number; // 填写工作内容数量
  attachments?: { documents: IFile[] };
}

export class Entry extends BaseModel<ISelfEntry, IResponse> {
  constructor() {
    super({
      name: 'access_entry',
      resource: 'entry',
      namespace: '/access/teacher',
      parentResource: 'entry_activities',
    });
  }

  // 获取自评项
  findByActivity(activityId: number | string) {
    return this.request.get<ISelfEntry>(`${this.namespace}/entry_activities/${activityId}/${this.resource}`);
  }
  // 更新自评总体评价
  updateSelf(activityId: number | string, formData: IEntryMeta) {
    return this.request.patch(`${this.namespace}/entry_activities/${activityId}/${this.resource}`, {
      [this.name]: {
        entry_meta: formData,
      },
    });
  }
}

export default new Entry();
