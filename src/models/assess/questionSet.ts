// 考核项题目集合
import BaseModel from '../BaseModel';
import { IQuestion } from './question';

export interface IQuestionSet {
  id?: number;
  access_activity_id?: number;
  title?: string;
  body?: string;
  state?: any;
  type?: IQuestionSetTypes;
  created_at?: string;
  updated_at?: string;
  start_at?: string;
  end_at?: string;
  deleted_at?: string;
  meta?: IObject & { max_question_count?: number; max_letter_count?: number };
  questions: IQuestion[];
}

export enum IQuestionSetTypes {
  system = 'Access::QuestionSet::System',
  normal = 'Access::QuestionSet::Normal',
}

interface IResponse {
  access_question_sets: IQuestionSet[];
}

export class QuestionSet extends BaseModel<IQuestionSet, IResponse> {
  constructor() {
    super({
      name: 'question_set',
      resource: 'question_sets',
      namespace: '/access/teacher',
      parentResource: 'activities',
    });
  }
}

export default new QuestionSet();
