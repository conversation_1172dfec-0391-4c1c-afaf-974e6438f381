// 被考核人关联的打分记录
import BaseModel from '../BaseModel';

export interface IScore {
  id?: number;
  created_at?: string;
  updated_at?: string;
  access_activity_id?: number;
  teacher_id?: number;
  user_id?: number;
  access_entry_id?: number;
  access_scope_id?: number;
  deleted_at?: string;
  readonly meta?: IObject; // 前端不要修改
  score: number; // 互评页面，总评分，一定要更新
  score_meta: { [id: number]: number }; // 用来存各项分数 { questionId: score }，领导可以不用更新这个，其他中层领导必须更新
}

interface IResponse {
  access_scores: IScore[];
}

export class Score extends BaseModel<IScore, IResponse> {
  constructor() {
    super({
      name: 'access_score',
      resource: 'scores',
      namespace: '/access/teacher',
      parentResource: 'score_entries',
    });
  }

  // 对备考人进行打分
  submitScore(scoreEntryId: number, formData: IScore) {
    const { namespace, parentResource, name } = this;
    return this.request.patch(`${namespace}/${parentResource}/${scoreEntryId}/score`, { [name]: formData });
  }
}

export default new Score();
