// 考核项
import BaseModel from '../BaseModel';

export interface IQuestion {
  id?: number;
  created_at?: string;
  updated_at?: string;
  access_question_set_id?: number;
  teacher_id?: number;
  title?: string;
  meta?: IObject;
  score?: number;
  deleted_at?: string;
  sort?: number;
}

interface IResponse {
  access_questions: IQuestion[];
}

export class Question extends BaseModel<IQuestion, IResponse> {
  constructor() {
    super({
      name: 'access_question',
      resource: 'questions',
      namespace: '/access/teacher',
      parentResource: 'question_sets',
    });
  }
}

export default new Question();
