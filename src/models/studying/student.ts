import BaseModel from '@/models/BaseModel';
import { IFile } from '../file';

export interface IResume {
  begin_date: string;
  end_date: string;
  school: string;
  duty: string;
  witness: string;
}

export interface IFamily {
  nickname: string;
  name: string;
  age: number;
  politics_status: string;
  company: string;
  company_address: string;
  tel: string;
  holiday: string;
}

export interface IStudentInfo {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  name?: string;
  code?: string;
  identity_id?: string;
  sex?: string;
  state?: string;
  nation?: any;
  birth?: any;
  native_place?: string;
  home_address?: string;
  tel?: string;
  phone?: any;
  postcode?: string;
  tel2?: any;
  high_school?: string;
  specialty?: any;
  entrance_records?: number;
  uniform_size?: any;
  avatar?: IFile;
  reward_punish?: any;
  high_school_job?: any;
  resume?: {
    index: IResume[];
  };
  family?: {
    index: IFamily[];
  };
  height?: string;
  weight?: string;
  department_ids?: any[];
}

export class Student extends BaseModel<IStudentInfo> {
  constructor() {
    super({
      name: 'student',
      resource: 'info',
      namespace: '/studying/student',
    });
  }

  info() {
    return this.request.get('/studying/student/info');
  }

  update(formData: any) {
    return this.request.patch('/studying/student/info', {
      student: formData,
    });
  }
}

export default new Student();
