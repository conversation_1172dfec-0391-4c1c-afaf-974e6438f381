import { AxiosRequestConfig, AxiosInstance } from 'axios';
import createRequestClient from '@/helpers/request';

// TODO: 第二版重构：enum 在状态，类型等的应用

type IdType = number | string;

export interface IBaseModel {
  baseUrl: string;
  rootPath: string;
  namespace: string;
  role: string;
  resource: string;
  indexKey?: string;
  name: string;
  parentResource: string | null | undefined;
  index(params: IObject, config?: AxiosRequestConfig): any;
  find(id: IdType, config?: AxiosRequestConfig): any;
  create(payload: any, config?: AxiosRequestConfig): any;
  update(instance: any, config?: AxiosRequestConfig): any;
  delete(id: number | string, config?: AxiosRequestConfig): any;
  setConfig(config: IModelConfig | IObject): void;
  setRole(role: string): void;
  indexByParent(parentId: number | string, params: IObject, config?: AxiosRequestConfig): any;
  createByParent(parentId: number | string, payload: IObject, config?: AxiosRequestConfig): any;
}

export interface IModelConfig {
  baseUrl?: string;
  rootPath?: string; // 路由的命名空间
  namespace?: string; // 路由的命名空间
  role?: string; // 路由的资源角色
  resource: string; // 资源名称，一般是模型名的复数形式
  indexKey?: string; // index 接口数组的 key
  name: string; // POST 创建实例时，提交给后台的模型 key
  parentResource?: string | null; // 父模型
}

// index 接口返回接口
interface IIndex {
  id?: number | string | null;
  current_page: number;
  total_pages: number;
  total_count: number;
}

// 模型实例基础结构
interface IModel {
  id?: number | null | string;
}

/**
 * 模型抽象基础类
 * api_path: baseUrl + rootPath + namespace + role + resource
 * examples:
 *  http://www.api.com/v2/finance/user/activities
 *  http://www.api.com + /v2 + /finance + /user + /activities
 */
export default class BaseModel<T extends IModel = IModel, IResponse = {}> {
  public request!: AxiosInstance;

  public baseUrl!: string;
  public rootPath: string = '/';
  public namespace: string = '';
  public role: string = '';
  public resource: string = '';
  public name: string = '';
  public indexKey: string = '';

  public parentResource: string | null = null;

  constructor(config: IModelConfig) {
    this.request = createRequestClient();
    this.setConfig(config);
  }

  get resourcePath() {
    return this.role ? `${this.namespace}/${this.role}/${this.resource}` : `${this.namespace}/${this.resource}`;
  }

  get parentResourcePath() {
    return this.role
      ? `${this.namespace}/${this.role}/${this.parentResource}`
      : `${this.namespace}/${this.parentResource}`;
  }

  /**
   * index
   * 模型列表接口
   */
  public index(params?: object, config?: AxiosRequestConfig) {
    return this.request.get<IResponse & IIndex>(`${this.resourcePath}`, {
      ...config,
      params,
    });
  }

  /**
   * find
   * 模型详情接口
   */
  public find(id: IdType, config?: AxiosRequestConfig) {
    return this.request.get<T>(`${this.resourcePath}/${id}`, config);
  }

  /**
   * create
   * 创建记录
   */
  public create(payload: T, config?: AxiosRequestConfig) {
    return this.request.post<T>(
      `${this.resourcePath}`,
      {
        [this.name]: payload,
      },
      config,
    );
  }

  /**
   * update
   * 更新记录
   */
  public update(instance: T, config?: AxiosRequestConfig) {
    if (instance && instance.id) {
      return this.request.patch(
        `${this.resourcePath}/${instance.id}`,
        {
          [this.name]: instance,
        },
        config,
      );
    }
    return Promise.reject(new Error('更新数据不能为空'));
  }

  /**
   * delete
   * 删除记录
   */
  public delete(id: IdType, config?: AxiosRequestConfig) {
    return this.request.delete(`${this.resourcePath}/${id}`, config);
  }

  /**
   * belongsTo
   * 查询列表
   */
  public indexByParent(parentId: IdType, params?: object, config?: AxiosRequestConfig) {
    if (!this.parentResource) {
      throw new Error('You should set parentSource when you use indexByParent.');
    }
    return this.request.get<IIndex & IResponse>(`${this.parentResourcePath}/${parentId}/${this.resource}`, {
      ...config,
      params,
    });
  }

  /**
   * belongsTo
   * 创建记录
   */
  public createByParent(parentId: IdType, payload: T, config?: AxiosRequestConfig) {
    if (!this.parentResource) {
      throw new Error('You should set parentSource when you use createByParent.');
    }
    return this.request.post<T>(
      `${this.parentResourcePath}/${parentId}/${this.resource}`,
      {
        [this.name]: payload,
      },
      config,
    );
  }

  /**
   * 设置模型参数
   * @param config 模型链接参数
   */
  setConfig(config: IModelConfig | IObject) {
    // 模型 baseURL: apiDomain/rootPath
    const defaultApiUrl = process.env.VUE_APP_API_DOMAIN || '';
    this.baseUrl = config.baseUrl || defaultApiUrl;
    // 接口根路径，通常是：'/'
    this.rootPath = config.rootPath || process.env.VUE_APP_API_ROOT_PATH;
    this.request.defaults.baseURL = this.baseUrl + this.rootPath;
    // 资源 namespace, 通常是： module/role
    this.namespace = config.namespace || this.namespace;
    // 当前角色
    this.role = config.role || this.role;
    // 当前资源
    this.resource = config.resource || this.resource;
    this.indexKey = config.indexKey || this.indexKey;
    // 资源名，发送数据的接口需要使用的 data key
    this.name = config.name || this.name;
    // 父资源
    this.parentResource = config.parentResource || this.parentResource;
  }

  setRole(role: string) {
    this.role = role;
  }
}
