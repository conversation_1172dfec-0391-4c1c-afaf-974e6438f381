import ActiveModel, { IModelConfig } from '@/lib/ActiveModel';

export class ResTeacherDepartment extends ActiveModel<IDepartment> {
  constructor(config?: IModelConfig) {
    super({
      namespace: '/res/teacher',
      name: 'department',
      actions: [{ name: 'tree', method: 'get', on: 'collection' }],
      ...config,
    });
  }
}

export interface IDepartment {
  id: number;
  name?: string;
  code?: string;
  short_name?: string;
  parent_id?: number;
  type?: string;
  meta?: IObject;
  depth?: number;
  children_count?: number;
  children?: IDepartment[];
}
