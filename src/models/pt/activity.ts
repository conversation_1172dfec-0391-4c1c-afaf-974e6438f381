/*
################# index column ########
#
#  id                     :bigint           not null, primary key
#  school_id(学校id)      :bigint
#  teacher_id(教师id)     :bigint
#  college_id(学院id)     :bigint
#  college_code(学院code) :string(255)
#  major_id(专业id)       :bigint
#  major_code(专业code)   :string(255)
#  name(名称)             :string(255)      default("")
#  start_at(开始时间)     :datetime
#  end_at(结束时间)       :datetime
#  state(状态)            :string(255)
#  attachments(附件)      :json
#  meta(扩展字段)         :json
#  deleted_at(删除标识)   :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IActivity {
  id: number;
  school_id: number;
  teacher_id: number;
  college_id: number;
  college_code: string;
  major_id: number;
  major_code: string;
  name: string;
  start_at: string;
  end_at: string;
  state: string;
  attachments: any;
  meta: any;
}

export interface IResponse {
  activities: IActivity[];
}

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor() {
    super({
      name: 'activity',
      resource: 'activities',
      namespace: '/pt/admin',
    });
  }

  statistics(params: any) {
    return this.request.get(`/pt/teacher/activities/${params.parentId}/statistics`, {
      params,
    });
  }

  registers(params: any) {
    return this.request.get(`/pt/teacher/activities/${params.parentId}/statistics/registers`, {
      params,
    });
  }

  reports(params: any) {
    return this.request.get(`/pt/teacher/activities/${params.parentId}/statistics/reports`, {
      params,
    });
  }

  companies(params: any) {
    return this.request.get(`/pt/teacher/activities/${params.parentId}/statistics/companies`, {
      params,
    });
  }

  theses(params: any) {
    return this.request.get(`/pt/teacher/activities/${params.parentId}/statistics/theses`, {
      params,
    });
  }
}

export default new Activity();
