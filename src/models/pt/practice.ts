/*
################# index column ########
#
#  id                     :bigint           not null, primary key
#  activity_id(活动id)    :bigint
#  student_id(学生id)     :bigint
#  school_id(学校id)      :bigint
#  college_id(学院id)     :bigint
#  major_id(专业id)       :bigint
#  college_code(学院代码) :string(255)
#  major_code(专业代码)   :string(255)
#  adminclass_id(班级id)  :bigint
#  title(主题)            :string(255)      default("")
#  company(公司)          :string(255)      default("")
#  city(城市)             :string(255)      default("")
#  address(地址)          :string(255)      default("")
#  job(岗位)              :string(255)      default("")
#  origin(来源)           :integer          default("normal")
#  state(状态)            :string(255)
#  remark(信息)           :text(65535)
#  attachments(附件)      :json
#  meta(扩展字段)         :json
#  start_at(开始时间)     :datetime
#  end_at(结束时间)       :datetime
#  deleted_at(删除标识)   :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IPractice {
  id: number;
  activity_id: number;
  student_id: number;
  school_id: number;
  college_id: string;
  major_id: number;
  college_code: string;
  major_code: string;
  adminclass_id: number;
  title: string;
  company: string;
  city: string;
  address: string;
  job: string;
  origin: number;
  state: string;
  remark: string;
  attachments: any;
  meta: any;
  start_at: string;
  end_at: string;
  student?: any;
  company_info?: any;
  guide_teachers?: any[];
  thesis?: any;
  template?: any[];
  statistics?: any;
}

export interface IResponse {
  practices: IPractice[];
}

export class Practice extends BaseModel<IPractice, IResponse> {
  constructor() {
    super({
      name: 'practice',
      resource: 'practices',
      namespace: '/pt/admin',
      parentResource: 'activities',
    });
  }
}

export default new Practice();
