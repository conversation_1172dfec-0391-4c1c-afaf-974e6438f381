/*
################# index column ########
#
#  id                   :bigint           not null, primary key
#  school_id(学校)      :bigint
#  user_type            :string(255)
#  user_id(用户)        :bigint
#  source_type          :string(255)
#  source_id(目标)      :bigint
#  title(主题)          :string(255)      default("")
#  body(信息)           :text(65535)
#  state(状态)          :string(255)
#  flag(标识)           :integer          default("day")
#  type(STI)            :string(255)
#  meta(扩展字段)       :json
#  attachments(附件)    :json
#  deleted_at(删除标识) :datetime
#  score(分数)          :decimal(8, 2)    default(0.0)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IReport {
  id: number;
  school_id: number;
  user_type: string;
  user_id: number;
  source_type: string;
  source_id: string;
  title: string;
  body: string;
  state: string;
  flag: number;
  type: string;
  attachments: any;
  meta: any;
  score: number;
  files?: any[];
  noCompare?: boolean;
}

export interface IResponse {
  reports: IReport[];
}

export class Report extends BaseModel<IReport, IResponse> {
  constructor() {
    super({
      name: 'report',
      resource: 'reports',
      namespace: '/pt/admin',
      parentResource: 'practices',
    });
  }
  comments(params: any) {
    return this.request.get(`/pt/${params.role}/reports/${params.parentId}/comments`, {
      params,
    });
  }
  deleteComment(params: any) {
    return this.request.delete(`/pt/${params.role}/reports/${params.parentId}/comments/${params.id}`);
  }
}

export default new Report();
