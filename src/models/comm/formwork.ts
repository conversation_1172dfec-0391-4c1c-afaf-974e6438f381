/*
################# index column ########
#  id                 :bigint           not null, primary key
#  school_id(学校id)  :bigint
#  teacher_id(教师id) :bigint
#  name(名称)         :string(255)      default("")
#  state(状态)        :integer          default("active")
#  type(类型)         :string(255)
#  position(排序)     :integer
#  meta(扩展字段)     :json
#  deleted_at         :datetime
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IFormwork {
  id: number;
  school_id: number;
  teacher_id: number;
  name: string;
  state: number;
  type: string;
  position: number;
  meta: any;
}

interface IResponse {
  formworks: IFormwork[];
}

export class Formwork extends BaseModel<IFormwork, IResponse> {
  constructor() {
    super({
      name: 'formwork',
      resource: 'formworks',
      namespace: '/comm/teacher',
    });
  }
}

export default new Formwork();
