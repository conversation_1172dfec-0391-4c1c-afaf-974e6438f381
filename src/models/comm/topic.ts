/*
################# index column ########
#  id                   :bigint           not null, primary key
#  school_id(学校id)    :bigint
#  college_id(学元id)   :bigint
#  major_id(专业id)     :bigint
#  user_type            :string(255)
#  user_id(用户)        :bigint
#  source_type          :string(255)
#  source_id(目标)      :bigint
#  type(STI)            :string(255)
#  title(主题)          :string(255)      default("")
#  body(内容)           :text(65535)
#  state(状态)          :string(255)
#  cover_image(封面)    :text(65535)
#  view_count(浏览人数) :integer          default(0)
#  like_count(点赞人数) :integer          default(0)
#  star_count(点赞人数) :integer          default(0)
#  meta(扩展字段)       :json
#  deleted_at           :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface ITopic {
  id: number;
  school_id: number;
  college_id: number;
  major_id: number;
  user_type: string;
  user_id: number;
  source_type: string;
  source_id: string;
  type: string;
  title: string;
  body: string;
  state: string;
  cover_image: string;
  view_count: number;
  like_count: number;
  star_count: number;
  meta: any;
  noCompare?: boolean;
}

export interface IResponse {
  topics: ITopic[];
}

export class Topic extends BaseModel<ITopic, IResponse> {
  constructor() {
    super({
      name: 'topic',
      resource: 'topics',
      namespace: '/teaching',
      role: 'user/courses/:courseId',
    });
  }

  // 可用路由：parentPath的格式: /teaching/user/courses/1
  indexByParent(params: any) {
    return this.request.get(`${params.parentPath}/topics`, { params });
  }
  // 可用路由：parentPath的格式: /teaching/user/courses/1
  findByParent(params: any) {
    return this.request.get(`${params.parentPath}/topics/${params.id}`, { params });
  }
  // 可用路由：parentPath的格式: /teaching/user
  deleteByParent(params: any) {
    return this.request.delete(`${params.parentPath}/topics/${params.id}`);
  }
}

export default new Topic();
