/*
################# index column ########
#
#  id                  :bigint           not null, primary key
#  user_type           :string(255)
#  user_id(签到用户)   :bigint
#  source_type         :string(255)
#  source_id(签到目标) :bigint
#  state(签到状态)     :string(255)
#  type(STI)           :string(255)
#  lon(经度)           :float(24)
#  lat(纬度)           :float(24)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
*/

import wechatSdk from '@/utils/wechatSdk';
import BaseModel from '../BaseModel';

export interface IRegister {
  id: number;
  user_type: string;
  user_id: number;
  source_type: string;
  source_id: string;
  state: string;
  type: string;
  lon: string;
  lat: string;
}

export interface IResponse {
  registers: IRegister[];
}

export class Register extends BaseModel<IRegister, IResponse> {
  constructor() {
    super({
      name: 'register',
      resource: 'registers',
      namespace: '/comm/user',
    });
  }

  getAddress(location: { lon?: number; lat?: number; ip?: string }) {
    return this.request.post<{ province: string; city: string; district: string; address: string }>(
      '/comm/user/register/address',
      {
        ...location,
      },
    );
  }
}

export default new Register();
