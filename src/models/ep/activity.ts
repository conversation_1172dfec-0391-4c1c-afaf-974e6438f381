/*
################# index column ########
#
#  id                   :bigint           not null, primary key
#  school_id(学校id)    :bigint
#  teacher_id(教师id)   :bigint
#  name(名称)           :string(255)      default("")
#  state(状态)          :string(255)
#  start_at(开始时间)   :datetime
#  end_at(结束时间)     :datetime
#  meta(扩展字段)       :json
#  deleted_at(软删标识) :datetime
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IActivity {
  id: number;
  school_id: number;
  teacher_id: number;
  name: string;
  start_at: string;
  end_at: string;
  state: 'pending' | 'starting' | 'completed';
  meta: any;
  noCompare: boolean;
  teacher_count?: number;
  student_count?: number;
  infos?: any;
}

export interface IResponse {
  activities: IActivity[];
}

export const stateMap = {
  pending: { text: '未开始', value: 'pending', type: 'info' },
  starting: { text: '进行中', value: 'staring', type: 'primary' },
  completed: { text: '已完成', value: 'completed', type: 'success' },
};

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor(role: 'admin' | 'inspect') {
    super({
      name: 'activity',
      resource: 'activities',
      namespace: '/ep',
      role,
    });
  }

  registers(params: any) {
    return this.request.get(`/ep/${this.role}/activities/${params.parentId}/statistics/registers`, {
      params,
    });
  }

  questions(params: any) {
    return this.request.get(`/ep/${this.role}/activities/${params.parentId}/questions`, {
      params,
    });
  }

  programs(params: any) {
    return this.request.get(`/ep/${this.role}/activities/${params.parentId}/programs`, {
      params,
    });
  }
}
