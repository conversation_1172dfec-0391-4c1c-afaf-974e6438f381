/*
################# index column ########
#
#  id                  :bigint           not null, primary key
#  user_type           :string(255)
#  user_id(签到用户)   :bigint
#  source_type         :string(255)
#  source_id(签到目标) :bigint
#  state(签到状态)     :string(255)
#  type(STI)           :string(255)
#  lon(经度)           :float(24)
#  lat(纬度)           :float(24)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
# Indexes
#
#  index_registers_on_source_type_and_source_id  (source_type,source_id)
#  index_registers_on_state                      (state)
#  index_registers_on_user_type_and_user_id      (user_type,user_id)
#
*/

import BaseModel from '../BaseModel';

export interface IRegister {
  id: number;
  user_type: string;
  user_id: number;
  source_type: string;
  source_id: string;
  state: string;
  type: string;
  lon: string;
  lat: string;
  meta?: any;
  user?: any;
  questions?: any[];
  created_at?: string;
}

export interface IResponse {
  registers: IRegister[];
}

export class Register extends BaseModel<IRegister, IResponse> {
  constructor(role: 'admin' | 'user' | 'inspect') {
    super({
      name: 'register',
      resource: 'registers',
      namespace: '/ep',
      parentResource: 'attendances',
      role,
    });
  }

  getAddress(params: any) {
    return this.request.post(`/comm/user/register/address`, {
      ...params,
    });
  }
}
