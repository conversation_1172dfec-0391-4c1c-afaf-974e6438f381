/**
 * 我的资金卡 - 可执行的预算
 * belongs_to project
 * */
import BaseModel from '@/models/BaseModel';

export interface IExecuteBudget {
  id?: number;
  amount?: number;
  effective_amount?: number;
  used_amount?: number;
  created_at?: string;
  updated_at?: string;
  project_id?: number;
  teacher_id?: number;
  budget_id?: number;
}

interface IResponse {
  execute_budgets: IExecuteBudget[];
}

export class ExecuteBudget extends BaseModel<IExecuteBudget, IResponse> {
  constructor() {
    super({
      namespace: '/finance',
      parentResource: 'projects',
      resource: 'execute_budgets',
      role: 'teacher/own',
      name: 'execute_budget',
    });
  }

  batchUpdate(projectId: number, teacherIds: number[], budgetIds: number[]) {
    return this.request.post(`${this.parentResourcePath}/${projectId}/execute_budgets/batch_update`, {
      execute_budgets: {
        budget_ids: budgetIds,
        teacher_ids: teacherIds,
      },
    });
  }

  // {"{:teacher_id=>6545, :code=>'20070142', :name=>'MyString'}":1}
  executors(projectId: number) {
    return this.request.get(`${this.parentResourcePath}/${projectId}/execute_budgets/executors`);
  }
}

export default new ExecuteBudget();
