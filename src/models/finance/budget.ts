import { IExecuteBudget } from '@/models/finance/execute_budget';
/**
 * 资金卡 - 预算
 * has_many vouchers (报销)
 */
import BaseModel from '@/models/BaseModel';
import { ITeacher } from '../hr/teacher';

export interface IBudget {
  id?: number;
  name?: string;
  project_uid?: string;
  project_id?: number;
  project_name?: string;
  catalog_id?: number;
  catalog_name?: string;
  subject_id?: number;
  subject_name?: string;
  origin_id?: number;
  origin_name?: string;
  amount?: string;
  locking_amount?: string;
  processing_payment_amount?: string;
  completed_payment_amount?: string;
  checked_payment_amount?: string;
  processing_payment_count?: number;
  completed_payment_count?: number;
  number?: number;
  unit_price?: string;
  purchase?: string; // 采购方式
  remark?: string;
  executor_ids?: number[];
  executors?: ITeacher[];
  execute_budgets?: IExecuteBudget[];
  created_at?: string;
  updated_at?: string;
  unit?: string;
  payment_way?: string;
  delta?: number;
  parent_budget_id?: number;
}

interface IResponse {
  budgets: IBudget[];
}

export class Budget extends BaseModel<IBudget, IResponse> {
  constructor() {
    super({
      name: 'budget',
      resource: 'budgets',
      namespace: '/finance',
      parentResource: 'projects',
      role: 'admin', // teacher/own, teacher/execute
    });
  }
}

export default new Budget();
