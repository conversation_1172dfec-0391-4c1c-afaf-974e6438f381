import BaseModel from '../BaseModel';
import { IWorkflowCorePlace, TransitionTypes, PlaceActionType, PlaceActionConfig } from './workflow';
import { IFile } from '../file';

export interface IToken {
  id?: number | string | null;
  name?: string | null;
  comment?: string;
  state?: string;
  type?: TokenTypes;
  instance_id?: number | string;
  operator_id?: number | string;
  operator_name?: string;
  operator_type?: 'Teacher' | 'Student';
  place_id?: number | string;
  place_name?: string;
  place?: IWorkflowCorePlace;
  place_form?: { fields: IFile[] }; // 节点自身表单
  token_payload?: IObject; // 节点自身表单数据
  transition?: IWorkflowCorePlace;
  transition_type?: TransitionTypes;
  previous_id?: number;
  options?: any; // 存储节点配置数据
  created_at?: string;
  updated_at?: string;
  action_alias?: PlaceActionConfig; // 操作别名
}

export interface IResponse {
  tokens: IToken[];
}

export enum TokenTypes {
  Token = 'Token', // 开始 | 结束
  Submit = 'Tokens::Submit', // 发起申请
  Notify = 'Tokens::Notify', // 抄送
  Approval = 'Tokens::Approval', // 审批
  ApprovalSelect = 'Tokens::ApprovalSelect', // 审批
  WechatDirectPublish = 'Wechat::Tokens::Publish', // 文章直接发送
  WechatTimedPublish = 'Wechat::Tokens::TimedPublish', // 文章定时发送
}

export class Token extends BaseModel<IToken, IResponse> {
  constructor() {
    super({
      name: 'token',
      resource: 'tokens',
      namespace: '/bpm',
      parentResource: 'instances',
      role: 'user',
    });
  }

  // 审批通过
  async accept(id: any, comment: string) {
    return this.request.post(`${this.resourcePath}/${id}/fire`, {
      [this.name]: {
        action: 'accept',
        comment,
      },
    });
  }
  // 审批撤回
  async recall(id: any, comment: string) {
    return this.request.post(`${this.resourcePath}/${id}/fire`, {
      [this.name]: {
        action: 'recall',
        comment,
      },
    });
  }
  // 审批打回，可以指定打回过去的具体节点
  async reject(id: any, comment: string, nextPlaceId?: number) {
    return this.request.post(`${this.resourcePath}/${id}/fire`, {
      [this.name]: {
        action: 'reject',
        comment,
        next_place_id: nextPlaceId,
      },
    });
  }
  // 审批退回，可以指定打回过去的具体节点
  async fail(id: any, comment: string, nextPlaceId?: number) {
    return this.request.post(`${this.resourcePath}/${id}/fire`, {
      [this.name]: {
        action: 'fail',
        comment,
        next_place_id: nextPlaceId,
      },
    });
  }
  // 审批流程终止
  async terminate(id: any, comment: string) {
    return this.request.post(`${this.resourcePath}/${id}/fire`, {
      [this.name]: {
        action: 'terminate',
        comment,
      },
    });
  }
  // 指定审批者
  async assign(id: any, token: IToken) {
    return this.request.post(`${this.resourcePath}/${id}/assign`, {
      [this.name]: {
        ...token,
      },
    });
  }

  // 转办
  async forward(id: any, token: IToken) {
    return this.request.post(`${this.resourcePath}/${id}/forward`, {
      [this.name]: {
        ...token,
      },
    });
  }
  // 通用方法
  async fire(action: PlaceActionType, id: any, comment: string, nextPlaceId?: number | string | null) {
    const payload: any = {
      action,
      comment,
    };
    if (['reject', 'fail'].includes(action)) {
      payload.next_place_id = nextPlaceId;
    }
    return this.request.post(`${this.resourcePath}/${id}/fire`, {
      [this.name]: payload,
    });
  }
}

export default new Token();
