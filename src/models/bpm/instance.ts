import { sum } from 'lodash';
import utils from '@/utils';
import BaseModel from '../BaseModel';
import { IToken, TokenTypes } from './token';
import { PlaceActionType } from './workflow';

interface IResponse {
  instances: IInstance[];
}

export class Instance extends BaseModel<IInstance, IResponse> {
  constructor() {
    super({
      resource: 'instances',
      name: 'instance',
      namespace: '/bpm',
      parentResource: 'workflows',
      role: 'user',
    });
  }

  async find(id: number | string) {
    const res = await this.request.get<IInstance>(`${this.resourcePath}/${id}`);
    const { data } = res;
    // 可执行的操作
    const enableActionsMap = (data.enable_actions || []).reduce(
      (obj: any, key: any) => ({
        ...obj,
        [key]: true,
      }),
      {},
    );
    // 可退回打回的节点
    const tokensMap = utils.objectify(data.tokens || [], 'place_id');
    const historyPlaceOptions = (Object.values(tokensMap) as IToken[])
      .filter(
        (o: IToken) =>
          o.place_id && [TokenTypes.Submit, TokenTypes.Approval].includes(o.type!) && o.id !== data.current_token!.id,
      )
      .map((o: any) => ({ value: o.place_id, label: `【${o.operator_name}】${o.name}` }));
    // 默认退回或打回的节点
    const defaultNextPlaceId = historyPlaceOptions.length ? historyPlaceOptions.concat().reverse()[0].value : 0;
    res.data = {
      ...res.data,
      enableActionsMap,
      historyPlaceOptions,
      defaultNextPlaceId,
    };
    return res;
  }

  /**
   * 审批记录统计
   * @param type  统计的具体 instance 类型，空代表获取所有类型的数据
   * @param workflowId 统计某个工作流，不传，统计所有工作流
   * @param filterTypes 返回的所有类型统计，需要过滤的类型，只影响 statistic 的统计结果
   */
  async statistic(type?: InstanceType | string, workflowId?: number | string, filterTypes?: InstanceType[]) {
    const { data } = await this.request.post<IObject>(
      `/bpm/user/instances/statistic?workflow_id=${workflowId || ''}&type=${type || ''}`,
    );
    const statistic = Object.values(data || {})
      .filter((o: any) => (filterTypes ? filterTypes.includes(o.type) : true))
      .reduce((res, o) => {
        Object.keys(o).forEach((k: string) => {
          if (Number.isInteger(o[k])) {
            const stateKey = k.replace(/_count/, '');
            res[stateKey] = res[stateKey] || 0;
            res[stateKey] += o[k];
          }
        });
        return res;
      }, {});
    return {
      data: {
        ...data,
        total: sum(Object.values(statistic)) || 0,
        statistic,
      } as IObject & { statistic: IObject },
    };
  }

  get stateMap() {
    return {
      all: { value: '', label: '全部' },
      created: { value: 'created', label: '待提交', type: 'default', color: '#CCCCCC', class: 'tag' },
      preparing: { value: 'preparing', label: '待处理', type: 'default', color: '#CCCCCC', class: 'tag' },
      processing: { value: 'processing', label: '进行中', type: 'primary', color: '#1890ff', class: 'tag-primary' },
      completed: { value: 'completed', label: '已完成', type: 'success', color: '#15bc83', class: 'tag-success' },
      checked: { value: 'checked', label: '审核通过', type: 'success', color: '#15bc83', class: 'tag-success' },
      rejected: { value: 'rejected', label: '已打回', type: 'danger', color: '#f29851', class: 'tag-danger' },
      failed: { value: 'failed', label: '已退回', type: 'danger', color: '#f29851', class: 'tag-danger' },
      canceled: { value: 'canceled', label: '已取消', type: 'warning', color: '#f29851', class: 'tag-warning' },
      terminated: { value: 'terminated', label: '已终止', type: 'danger', color: '#f5222d', class: 'tag-danger' },
    } as IObject;
  }
}

export default new Instance();

export interface IInstance {
  id?: number | string;
  created_at?: string;
  updated_at?: string;
  workflow_id?: number;
  creator_name?: string;
  creator_type?: 'Teacher' | 'Student';
  creator_id?: number;
  creator_department_path?: string[];
  creator_department_name?: string;
  state?:
    | 'created'
    | 'processing'
    | 'preparing'
    | 'completed'
    | 'checked'
    | 'rejected'
    | 'failed'
    | 'canceled'
    | 'terminated';
  seq?: string;
  type?: InstanceType;
  tokens?: IToken[];
  current_token?: IToken;
  last_token?: IToken;
  payload?: IObject;
  enable_actions?: PlaceActionType[];
  meta?: IObject;
  flowable_id?: number;
  flowable_type?: IFlowableType;
  flowable_info?: IObject;
  enableActionsMap?: IObject; // 业务数据
  historyPlaceOptions?: object[]; // 业务数据
  defaultNextPlaceId?: number; // 业务数据
  storage?: IObject;
}

export enum InstanceType {
  Bpm = 'Bpm::Instance',
  Wechat = 'Wechat::Instance',
  Voucher = 'Finance::VoucherInstance',
  FinanceVoucher = 'Finance::VoucherInstance',
  FinanceLoan = 'Finance::LoanVoucherInstance',
  FinanceProject = 'Finance::ProjectInstance',
  ExamActivity = 'Exam::ActivityInstance',
  Meeting = 'Meeting::ApplicationFormInstance',
  Welcome = 'Studying::Welcome::Instance',
  Hr = 'Hr::ModificationInstance',
  EmsCourseSet = 'Ems::CourseSetInstance',
  Inform = 'Inform::Instance',
}

// 微信文章表单项结构
export interface IFormItemArticle {
  appId: string;
  mediaId: string;
  count?: number;
  headline?: string;
}

export enum IFlowableType {
  Voucher = 'Finance::Voucher',
}
