import utils from '@/utils';
import { IFormItem, getRuleType } from '@/interfaces/IFormItem';
import Schema, { <PERSON><PERSON><PERSON><PERSON><PERSON>, FieldErrorList } from 'async-validator';
import BaseModel, { IModelConfig } from '../BaseModel';
import tokenModel, { IToken } from './token';
import { IFile } from '../file';

type ValidateCallbacks = {
  success?: (formData: IObject) => void;
  fail?: (errors: ErrorList, errorFields: FieldErrorList) => void;
};

export interface ITokenFormResponse {
  workflow: IWorkflow;
  token: IToken;
  formTemplate: IFormItem[];
  formEditable: boolean;
}

export interface IWorkflow {
  id: number | string | null;
  name?: string;
  state?: WorkflowState;
  desc?: string;
  type?: WorkflowTypes;
  core?: IWorkflowCore;
  form?: { fields: IFormItem[] };
  created_at?: string;
  updated_at?: string;
  category_id?: number;
  category_name?: string;
  position?: number;
  meta?: {
    workflow_roles: string[];
    workflow_callbacks: Array<{ callback_method: string; name: string }>;
    workflow_attributes: Array<{ attr: string; name: string; attr_type: string }>;
  };
}

export enum WorkflowState {
  Todo = 'todo',
  Done = 'done',
}

export enum WorkflowTypes {
  Bpm = 'Bpm::Workflow',
  Wechat = 'Wechat::Workflow',
  Finance = 'Finance::VoucherWorkflow',
  FinanceLoan = 'Finance::LoanVoucherWorkflow',
  FinanceProject = 'Finance::ProjectWorkflow',
  ExamActivity = 'Exam::ActivityWorkflow',
  Meeting = 'Meeting::ApplicationFormWorkflow',
  Welcome = 'Studying::Welcome::Workflow',
  Hr = 'Hr::ModificationWorkflow',
}

export interface IWorkflowCore {
  places: IWorkflowCorePlace[];
  tree: IObject[];
}

export interface IWorkflowCorePlace {
  id?: number | null;
  seq?: string;
  name?: string;
  type?: PlaceTypes;
  transition_type?: TransitionTypes;
  workflow_id?: number;
  options?: IObject;
  token?: IToken;
  fields?: {
    fields: IFile[];
  };
  position?: number;
  callback_options?: {
    callback_method?: string;
    callback_type?: 'token_callback';
    action_permits?: { [key: string]: boolean }; // 节点操作的显示权限
    action_alias?: PlaceActionConfig; // 节点操作的配置
  };
  place_form?: {
    fields: IFile[];
  }; // 节点自身表单
  kind?: 'condition'; // 业务属性
  place_meta?: {
    major: string[];
  };
}

// 流程的 place 类型
export enum PlaceTypes {
  Start = 'Places::StartPlace', // 开始节点
  End = 'Places::EndPlace', // 结束节点
  Route = 'Places::Route', // 条件节点
  Place = 'Place', // 普通节点
}

export enum TransitionTypes {
  Transition = 'Transition', // 结束节点
  Start = 'Transitions::Submit', // 开始节点
  End = 'Transitions::Finish',
  Condition = 'Transitions::Condition', // 条件节点
  ApprovalSpecifyManager = 'Transitions::Approval::SpecifyManager', // 部门主管
  ApprovalDirectManager = 'Transitions::Approval::DirectManager', // 直接主管
  ApprovalLevelManager = 'Transitions::Approval::LevelManager', // 学院主管
  ApprovalTeacher = 'Transitions::Approval::Teacher', // 指定成员
  ApprovalSponsorSelect = 'Transitions::Approval::SponsorSelect', // 发起人自选
  ApprovalSponsorSelf = 'Transitions::Approval::SponsorSelf', // 发起人自己
  ApprovalUser = 'Transitions::Approval::User',
  ApprovalFlowableRole = 'Transitions::Approval::FlowableRole', // 角色分配
  ApprovalSelect = 'Transitions::Approval::ApprovalSelect', // 审批人自选
  NotifySpecifyManager = 'Transitions::Notify::SpecifyManager', // 部门主管
  NotifyDirectManager = 'Transitions::Notify::DirectManager', // 直接主管
  NotifyLevelManager = 'Transitions::Notify::LevelManager', // 学院主管
  NotifyTeacher = 'Transitions::Notify::Teacher', // 指定成员
  NotifySponsorSelect = 'Transitions::Notify::SponsorSelect', // 发起人自选
  WechatDirectPublish = 'Wechat::Transitions::DirectPublish', // 公众号文章直接发送
  WechatTimedPublish = 'Wechat::Transitions::TimedPublish', // 公众号文章定时发送
  FlowableCallback = 'Transitions::FlowableCallback', // 方法回调
  ApiCallback = 'Transitions::ApiCallback', // api 回调
  FunctionCallback = 'Transitions::FunctionCallback', // 方法回调
  Formula = 'Transitions::Formula', // 公式
}

interface IResponse {
  workflows: IWorkflow[];
}

// place enable_actions 节点自定义操作
export type PlaceActionType =
  | 'print'
  | 'submit'
  | 'edit' // 可以编辑
  | 'assign' // 指派人员
  | 'forward' // 转办
  | 'accept' // 通过
  | 'recall' // 撤回
  | 'reject' // 打回，指定 next_place_id，退回后，提交还是到当前节点
  | 'fail' // 退回，指定 next_place_id，退回从新走流程
  | 'terminate'; // 终止

export interface PlaceActionConfig {
  [key: string]: {
    name: string;
    show: boolean;
    desc: string;
  };
}

// 节点操作配置
export const getDefaultPlaceActionConfig = (): PlaceActionConfig => ({
  print: { name: '打印', show: true, desc: '打印关联的数据' },
  submit: { name: '提交', show: true, desc: '当前流程需要您提交后，流程才可开始运行' },
  edit: { name: '编辑', show: true, desc: '编辑关联的数据' },
  assign: { name: '指派', show: true, desc: '选择节点处理人员' },
  accept: { name: '通过', show: true, desc: '接受当前节点，使流程可以转移到下一节点' },
  forward: { name: '转办', show: true, desc: '选择其他转办人员' },
  recall: { name: '撤回', show: true, desc: '撤回已执行的审批操作，并重新处理' },
  reject: { name: '驳回', show: true, desc: '驳回节点，需要重新走审批流程' },
  fail: { name: '退回', show: true, desc: '退回节点，不需要重新走流程' },
  terminate: { name: '终止', show: true, desc: '直接结束本次流程' },
});

export class Workflow extends BaseModel<IWorkflow, IResponse> {
  constructor(config?: IModelConfig | IObject) {
    super({
      resource: 'workflows',
      name: 'workflow',
      namespace: '/bpm',
      role: 'user',
      ...config,
    });
  }

  /**
   * 获取指定 token 赋权的工作流表单，无 token 取第一个节点
   * @param workflowId 工作流实例
   * @param tokenId 阶段 token
   */
  async findTokenForm(workflowId: any, tokenId?: number | string | null): Promise<ITokenFormResponse> {
    const { data } = await this.find(workflowId);
    const { fields } = data.form || { fields: [] };
    let token: IToken = {
      transition: (data.core!.places || []).find(o => o.type === PlaceTypes.Start),
    };
    if (tokenId) {
      const res = await tokenModel.find(tokenId);
      token = res.data;
    }
    const newFormFields = this.getAccessibilityFields(fields, token.transition!);
    return Promise.resolve<ITokenFormResponse>({
      workflow: data,
      token,
      formTemplate: newFormFields,
      formEditable: newFormFields.some(
        o => o.transitionAccessibility === 'read_and_write' || !o.transitionAccessibility,
      ),
    });
  }

  /**
   * template: workflow 的 form.fields.fields 定义
   * transition: 挡圈操作的 place 或 transition
   * return: 返回新的模板，增加了 具体节点的 transitionAccessibility 属性
   */
  getAccessibilityFields(template: any[], transition: IWorkflowCorePlace) {
    if (!transition) {
      return template;
    }

    const power = utils.objectify((transition.fields && transition.fields.fields) || [], 'key', 'accessibility');
    const defaultAccessibility = [TransitionTypes.Start, PlaceTypes.Start].includes(transition.type!)
      ? 'read_and_write'
      : 'readonly';
    return template.map(item => ({
      ...item,
      transitionAccessibility: power[item.key] || defaultAccessibility,
    }));
  }

  /**
   * 获取 template 对于表单的验证规则
   */
  getFormRules(template: IFormItem[]) {
    return template.reduce((rules: IObject, item: IFormItem) => {
      rules[item.key!] = {
        required: item.layout.required && item.transitionAccessibility === 'read_and_write',
        message: `请设置${item.name}`,
        type: getRuleType(item),
      };
      return rules;
    }, {});
  }

  /**
   * 根据模板表单，验证 formData 合法性
   */
  validateForm(formData: IObject, template: IFormItem[], callbacks: ValidateCallbacks = {}) {
    const rules = this.getFormRules(template);
    const validator = new Schema(rules);
    validator.validate(formData, {}, (errors, errorFields) => {
      if (errors) {
        if (callbacks.fail) {
          callbacks.fail(errors, errorFields);
        }
      } else if (callbacks.success) {
        callbacks.success(formData);
      }
    });
  }
}

export default new Workflow();
