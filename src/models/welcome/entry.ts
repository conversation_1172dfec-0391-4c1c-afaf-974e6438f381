import BaseModel from '../BaseModel';
import { IInstance } from '../bpm/instance';

export class WelcomeEntry extends BaseModel<IWelcomeEntry, IResponse> {
  constructor() {
    super({
      name: 'entry',
      resource: 'entries',
      namespace: '/studying/student/welcome',
    });
  }

  studentEntry() {
    return this.request.get<IWelcomeEntry>('/studying/student/welcome/entry');
  }
}

interface IResponse {
  entries: IWelcomeEntry[];
}

export interface IWelcomeEntry {
  id?: number;
  created_at?: string;
  updated_at?: string;
  activity_id?: number;
  student_id?: number;
  flowable_info: IWelcomeEntryFlowableInfo;
  instance: IInstance;
  student: any;
}

export interface IWelcomeEntryFlowableInfo {
  seq?: string;
  activity_name?: string;
  activity_start_at?: string;
  activity_end_at?: string;
  activity_state?: string;
  student_id?: number;
  instance_state?: string;
  student_code?: string;
  student_name?: string;
}
