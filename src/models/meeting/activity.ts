import BaseModel, { IModelConfig } from '../BaseModel';
import { IAttendance } from './attendance';

export interface IActivity {
  id?: number;
  date?: string;
  title?: string;
  desc?: string;
  meeting_room_id?: number;
  begin_time?: string;
  end_time?: string;
  reserver_type?: 'Teacher' | 'Student';
  reserver_id?: number;
  reserver_name?: string;
  reserver_code?: string;
  location?: string;
  school_id?: number;
  supposed_count?: number; // 已报名
  present_count?: number; // 已签到
  limit_count?: number | null; // 会议设定人数, null: 不限人数
  type?: ActivityTypes;
  meta?: IObject;
  created_at?: string;
  updated_at?: string;
  published?: boolean;
  meeting_attendance?: IAttendance | null; // 当前用户的报名情况
  typeText?: string; // 业务数据：类型文本
  balance?: number; // 业务数据：还可报名人数
}

export enum ActivityTypes {
  Assign = 'Meeting::ActivityAssign', // 管理员指定，指定哪些人需要参加活动
  Invite = 'Meeting::ActivityInvite', // 管理员审核
  InviteAuto = 'Meeting::ActivityInviteAuto', // 自动通过
}

interface IResponse {
  meeting_activities: IActivity[];
}

export class Activity extends BaseModel<IActivity, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'meeting_activity',
      resource: 'activities',
      namespace: '/meeting/user',
      ...config,
    });
  }
}

export default new Activity();
