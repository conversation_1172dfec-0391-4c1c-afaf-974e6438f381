import BaseModel, { IModelConfig } from '../BaseModel';
import { IActivity } from './activity';

export interface IRoom {
  id?: number;
  uid?: number;
  name?: string;
  state?: string;
  building?: string;
  meeting_activities?: IActivity[];
  meta?: string;
  created_at?: string;
  updated_at?: string;
}

export interface IResponse {
  meeting_rooms: IRoom[];
}

export class Room extends BaseModel<IRoom, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'room',
      resource: 'rooms',
      namespace: '/meeting/api',
      ...config,
    });
  }
}

export default new Room();
