import BaseModel, { IModelConfig } from '../BaseModel';
import { IActivity, ActivityTypes } from './activity';

export interface IAttendance {
  id?: number;
  meeting_activity?: IActivity;
  user_id?: number;
  user_type?: 'Teacher' | 'Student';
  user_name?: string;
  user_code?: string;
  state?: AttendanceStates;
  meeting_activity_id?: number;
  meta?: IObject;
  signup_at?: any;
  created_at?: string;
  updated_at?: string;
  user_department_path?: string[];
  user_department_name?: string;
}

export enum AttendanceStates {
  signed = '已报名',
  checked = '已签到',
  canceled = '已取消',
  todo = '待审批',
}

interface IResponse {
  meeting_attendances: IAttendance[];
}

export class Attendance extends BaseModel<IAttendance, IResponse> {
  constructor(config?: IModelConfig) {
    super({
      name: 'meeting_attendance',
      resource: 'attendances',
      namespace: '/meeting/user',
      parentResource: 'activities',
      ...config,
    });
  }

  // User cancel
  cancel(id: number) {
    return this.request.post(`${this.namespace}/${this.resource}/${id}/cancel`);
  }

  // 报名
  apply(activityId: number) {
    return this.request.post(`${this.namespace}/${this.parentResource}/${activityId}/attendance`);
  }

  static can(attendance: IAttendance, activity: IActivity) {
    return {
      delete: attendance.state === AttendanceStates.canceled, // 删除
      apply: !(
        attendance.id ||
        (activity.limit_count && activity.supposed_count! >= activity.limit_count) ||
        activity.type === ActivityTypes.Assign
      ), // 报名
      cancel:
        attendance.state === AttendanceStates.signed &&
        activity.type !== ActivityTypes.Assign &&
        new Date(activity.end_time!) > new Date(),
      operate: new Date(activity.end_time!) > new Date(), // 非历史活动，还未结束，可以操作
    };
  }
}

export default new Attendance();
