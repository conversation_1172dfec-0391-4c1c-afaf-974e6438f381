import BaseModel from '../BaseModel';

export interface IDepartment {
  id: number;
  name?: string;
  code?: string;
  short_name?: string;
  parent_id?: number;
  type?: string;
  meta?: IObject;
  depth?: number;
  children_count?: number;
  children?: IDepartment[];
  path?: [];
}

interface IResponse {
  modifications: IDepartment[];
}

export class Department extends BaseModel<IDepartment, IResponse> {
  constructor() {
    super({
      name: 'department',
      resource: 'departments',
      namespace: '/res/teacher',
    });
  }

  tree(params: any) {
    return this.request.get<IResponse>(`/res/teacher/departments/tree`, { params });
  }

  static convertTreeData(departments: IDepartment[]): IDepartment[] {
    return departments.map(d => ({
      ...d,
      key: d.id,
      value: d.id,
      title: d.short_name,
      children: this.convertTreeData(d.children || []),
    }));
  }

  getDepartmentKeys(departments: any[] = [], keyword: string) {
    let parentKeys: any = [];
    (departments || []).forEach((node: any) => {
      if (node.name.indexOf(keyword) > -1) {
        parentKeys.push(node.key);
        if (node.children_count) {
          parentKeys = parentKeys.concat(this.getDepartmentKeys(node.children, keyword));
        }
      } else if (node.children_count) {
        parentKeys = parentKeys.concat(this.getDepartmentKeys(node.children, keyword));
      }
    });
    return parentKeys;
  }
}

export default new Department();
