/*
################# index column ########
#
#  id                      :bigint           not null, primary key
#  school_id(学校id)       :bigint
#  audit_user_type         :string(255)
#  audit_user_id(审核用户) :bigint
#  apply_user_type         :string(255)
#  apply_user_id(提交用户) :bigint
#  source_type             :string(255)
#  source_id(修改对象)     :bigint
#  info(修改对象字段信息)  :json
#  state(状态)             :string(255)
#  meta(扩展字段)          :json
#  deleted_at(软删除标识)  :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
*/

import BaseModel from '../BaseModel';

export interface IModification {
  id: number;
  school_id: number;
  audit_user_type: string;
  audit_user_id: number;
  apply_user_type: string;
  apply_user_id: number;
  source_type: string;
  source_id: number;
  info: any;
  state?: string;
  meta: any;
  user?: any;
}

interface IResponse {
  modifications: IModification[];
}

export class Modification extends BaseModel<IModification, IResponse> {
  constructor(role: 'teacher' | 'admin') {
    super({
      name: 'modification',
      resource: 'modifications',
      namespace: '/hr',
      role,
    });
  }

  apply_modifications(params: any) {
    return this.request.get(`/hr/admin/teachers/${params.parentId}/apply_modifications`, { params });
  }
}
