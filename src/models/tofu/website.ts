import BaseModel from '@/models/BaseModel';

export interface ITofuWebsite {
  id?: number;
  name?: string;
  url?: string;
  type?: string;
  icon?: string;
  image?: string;
  position?: number;
}

interface IResponse {
  websites: ITofuWebsite[];
}

export class TofuWebsite extends BaseModel<ITofuWebsite, IResponse> {
  constructor() {
    super({
      name: 'website',
      resource: 'websites',
      namespace: '/tofu',
      role: 'user',
    });
  }
}

export default new TofuWebsite();
