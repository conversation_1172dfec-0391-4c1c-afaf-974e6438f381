import BaseModel from '@/models/BaseModel';
import { ITofu } from './app';

export interface ITofuStar extends ITofu {
  id?: number;
  name?: string;
  url?: string;
  type?: string;
  icon?: string;
  image?: string;
  position?: number;
}

interface IResponse {
  stars: ITofuStar[];
}

export class TofuStar extends BaseModel<ITofu, IResponse> {
  constructor() {
    super({
      name: 'star',
      resource: 'stars',
      namespace: '/tofu',
      role: 'user',
    });
  }

  batchUpdate(ids: number[]) {
    return this.request.patch(`/tofu/user/stars/batch_update`, { tofu_ids: ids });
  }
}

export default new TofuStar();
