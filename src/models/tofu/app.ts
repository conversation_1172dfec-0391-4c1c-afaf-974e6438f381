import BaseModel from '@/models/BaseModel';

export interface ITofu {
  id?: number;
  name?: string;
  url?: string;
  mobile_url?: string;
  type?: string;
  icon?: string;
  image?: string;
  position?: number;
  top_tofu_name?: string;
  instance_count?: number;
  isEdit?: boolean;
  subs?: IObject[];
  stared?: boolean;
}

interface IResponse {
  apps: ITofu[];
}

export class TofuApp extends BaseModel<ITofu, IResponse> {
  constructor() {
    super({
      name: 'app',
      resource: 'apps',
      namespace: '/tofu',
      role: 'user',
    });
  }
}

export default new TofuApp();
