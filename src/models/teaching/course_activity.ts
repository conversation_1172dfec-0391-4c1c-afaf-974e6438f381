import utils from '@/utils';
import BaseModel from '../BaseModel';

export interface ICourseActivity {
  id?: number;
  created_at?: string;
  updated_at?: string;
  start_time?: number;
  end_time?: number;
  start_unit?: number;
  end_unit?: number;
  week_state?: string;
  week_state_num?: number;
  weekday?: number;
  course_id?: number;
  teacher_id?: number;
  classroom_id?: any;
}

interface IResponse {
  course_activities: ICourseActivity[];
}

export class CourseActivity extends BaseModel<ICourseActivity, IResponse> {
  constructor() {
    super({
      name: 'course_activity',
      resource: 'course_activities',
      namespace: '/teaching/teacher',
    });
  }

  // 生成课程总表数据
  // [{ key: '', head: {}, items: [] }, ...]
  static getScheduleRecords(courseActivities: ICourseActivity[], columnCount: number = 12) {
    type EntryItem = [number, ICourseActivity];
    // 获取 records
    return (Object.entries(utils.groupBy(courseActivities, 'weekday') as any) as any)
      .sort((a: EntryItem, b: EntryItem) => a[0] - b[0])
      .map((record: any) => {
        const [weekday, activities] = record;
        const sortedActivities = activities.sort(
          (a: ICourseActivity, b: ICourseActivity) => a.start_unit! - b.start_unit!,
        );
        return {
          key: weekday,
          head: {
            week: utils.weekDay(weekday),
          },
          items: sortedActivities,
        };
      });
  }

  setRole(role: string = 'teacher') {
    this.setConfig({ namespace: `/teaching/${role}` });
    return this;
  }
}

export default new CourseActivity();
