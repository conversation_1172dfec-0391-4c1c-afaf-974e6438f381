import BaseModel from '../BaseModel';
import { IFile } from '../file';

export interface IRegister {
  id?: number;
  created_at?: string;
  updated_at?: string;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: number;
  state?: 'done' | 'undo' | 'late';
  type?: string;
  lon?: any;
  lat?: any;
  student: {
    name: string;
    avatar: IFile;
  };
}

interface IResponse {
  registers: IRegister[];
}

export class TeacherRegister extends BaseModel<IRegister, IResponse> {
  constructor() {
    super({
      name: 'lesson_register',
      resource: 'registers',
      namespace: '/teaching/teacher/inspect',
      parentResource: 'lessons',
    });
  }

  init(lessonId: number) {
    return this.request.post<void>(`/teaching/teacher/lessons/${lessonId}/registers/init`);
  }

  findByLesson(lessonId: number, id: number) {
    return this.request.get<IRegister>(`/teaching/teacher/lessons/${lessonId}/registers/${id}`);
  }

  updateByLesson(lessonId: number, register: IRegister) {
    return this.request.patch(`/teaching/teacher/lessons/${lessonId}/registers/${register.id}`, {
      [this.name]: register,
    });
  }

  qrcode(lessonId: number) {
    return this.request.get<{ nonce: string }>(`/teaching/teacher/lessons/${lessonId}/registers/nonce`);
  }
}

export default new TeacherRegister();
