import BaseModel from '../BaseModel';
import { IAnswer } from './answer';

export interface IAnswerSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: string;
  state?: any;
  question_set_id?: number;
  meta?: any;
  answerable_type?: string;
  answerable_id?: number;
  creator_type?: string;
  creator_id?: number;
  answers?: IAnswer[];
}

interface IResponse {
  answer_sets: IAnswerSet[];
}

export class AnswerSet extends BaseModel<IAnswerSet, IResponse> {
  constructor() {
    super({
      name: 'answer_set',
      resource: 'answer_sets',
      namespace: '/comm/student',
    });
  }
}

export default new AnswerSet();
