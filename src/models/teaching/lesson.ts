import dayjs from 'dayjs';
import utils from '@/utils';
import BaseModel from '../BaseModel';
import { IHomework } from './homework';

type EvaluationState = 'done' | 'doing' | 'todo';
type RegisterState = 'done' | 'undo' | 'late' | 'absent';

interface IRegisterStat {
  done: number;
  late: number;
  undo: number;
}
interface IEvaluationStat {
  done: number;
  doing: number;
  todo: number;
  count: number;
}
interface IReportStat {
  total: number;
  pending: number;
  scored: number;
  published: number;
}
interface ITopicStat {
  total: number;
  pending: number;
  published: number;
}

export interface ILesson {
  id?: number;
  created_at?: string;
  updated_at?: string;
  start_time?: number | string;
  end_time?: number | string;
  start_unit?: number;
  end_unit?: number;
  week?: number;
  weekday?: number;
  course_id?: number;
  course_activity_id?: number;
  semester_id?: number;
  teacher_id?: number;
  classroom_id?: number;
  date?: string;
  course_name?: string;
  course_set_name?: string;
  course_set_exam_mode?: string;
  teacher_name?: string;
  classroom_name?: string;
  unit_count?: number;
  course_std_count?: number;
  evaluation_count?: number;
  student_evaluation_state?: EvaluationState;
  register_state?: RegisterState;
  register_count?: {
    done: number;
    late: number;
    undo: number;
  };
  // 教师
  register_stat?: IRegisterStat;
  evaluation_stat?: IEvaluationStat;
  report_stat?: IReportStat;
  topic_stat?: ITopicStat;
  // 其他
  evaluation_answer_statistic?: {
    answer_count: number;
    question_count: number;
    question_infos: IQuestionStatInfo[];
  };
  datetime?: string; // 业务属性
  homework?: IHomework;
}

interface IQuestionStatInfo {
  answer_count: number;
  title: string;
  type: string;
  stat_info: Array<{ key: string; value: string; count: number }>;
}

interface IResponse {
  lessons: ILesson[];
}

export interface ILessonRecord {
  date: string; // 日期，作为主键
  head: IObject; // 每行数据的头部信息
  items: ILesson[]; // 课程数据
}

export class Lesson extends BaseModel<ILesson, IResponse> {
  constructor() {
    super({
      name: 'lesson',
      resource: 'lessons',
      namespace: '/teaching/teacher/inspect',
    });
  }

  setRole(role: string = 'teacher/current_semester') {
    this.setConfig({ namespace: `/teaching/${role}` });
    return this;
  }

  static getLessonTimeState(lesson: ILesson) {
    const lessonStart = dayjs(`${lesson.date} ${utils.stringSplice(lesson.start_time!, 2, 0, ':')}:00`);
    const lessonEnd = dayjs(`${lesson.date} ${utils.stringSplice(lesson.end_time!, 2, 0, ':')}:00`);
    const startDuration = lessonStart.valueOf() - dayjs().valueOf();
    const endDuration = lessonEnd.valueOf() - dayjs().valueOf();
    return {
      canRegister: startDuration <= 600000 && endDuration >= 0,
      todoRegister: startDuration > 600000,
      doneRegister: endDuration < 0,
      canEvaluate: startDuration <= 300000,
    };
  }

  // 生成课程表数据
  // [{ date: '', head: {}, items: [] }, ...]
  static getScheduleRecords(originalLessons: ILesson[]) {
    type EntryItem = [string, ILesson];
    return Object.entries(utils.groupBy(originalLessons, 'date') as any[])
      .sort((a: EntryItem, b: EntryItem) => new Date(a[0]).valueOf() - new Date(b[0]).valueOf())
      .map((record: [string, ILesson[]]) => {
        const [date, lessons] = record;
        const dateObj = dayjs(date);
        const sortedLessons = lessons
          .filter(o => o.id)
          .sort((a: ILesson, b: ILesson) => Number(a.start_unit)! - Number(b.start_unit)!);
        return {
          date,
          head: {
            dayjs: dateObj,
            date: dateObj.format('MM/DD'),
            week: utils.weekDay(dateObj.day()),
            lessonCount: lessons.reduce((sum: number, a: ILesson) => sum + (a.unit_count || 1), 0),
          },
          items: sortedLessons,
        };
      }) as ILessonRecord[];
  }

  static getWeekLessons(originalLessons: ILesson[]) {
    const beginDate = originalLessons[0] ? dayjs(originalLessons[0].date) : dayjs();
    const weekStart = dayjs(beginDate).day(1);
    const weekDays = Array.from({ length: 7 }).map((o, index) =>
      dayjs(weekStart)
        .add(index, 'day')
        .format('YYYY-MM-DD'),
    );
    const dateLessonMap: any = utils.groupBy(originalLessons, 'date');
    const weekLessons: ILesson[] = [];
    weekDays.forEach((date: string) => {
      if (!dateLessonMap[date]) {
        weekLessons.push({ date });
      } else {
        weekLessons.push(...dateLessonMap[date]);
      }
    });
    return weekLessons;
  }
}

export default new Lesson();
