import BaseModel from '@/models/BaseModel';
import { IAnswerSet } from './answerSet';

type TState = 'done' | 'doing' | 'todo';

export interface IEvaluation {
  id?: number;
  created_at?: string;
  updated_at?: string;
  student_id?: number;
  teacher_id?: number;
  lesson_id?: number;
  course_id?: number;
  course_set_id?: number;
  grade?: any;
  major_id?: number;
  adminclass_id?: number;
  department_id?: any;
  date?: string;
  school_id?: number;
  semester_id?: number;
  score?: number;
  score_meta?: {
    [key: string]: number;
  };
  state?: TState;
  question_set_id?: number;
  answer_set?: IAnswerSet;
}

interface IResponse {
  teaching_evaluations: IEvaluation[];
}

export class TeacherEvaluation extends BaseModel<IEvaluation, IResponse> {
  constructor() {
    super({
      name: 'teaching_evaluation',
      resource: 'evaluations',
      namespace: '/teaching/teacher',
      parentResource: 'lessons',
    });
  }
}

export default new TeacherEvaluation();
