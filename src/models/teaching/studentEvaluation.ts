import BaseModel from '@/models/BaseModel';
import { IAnswerSet } from './answerSet';

type TState = 'done' | 'doing' | 'todo';

export interface IEvaluation {
  id?: number;
  created_at?: string;
  updated_at?: string;
  student_id?: number;
  teacher_id?: number;
  lesson_id?: number;
  course_id?: number;
  course_set_id?: number;
  grade?: any;
  major_id?: number;
  adminclass_id?: number;
  department_id?: any;
  date?: string;
  school_id?: number;
  semester_id?: number;
  score?: number;
  score_meta?: {
    [key: string]: number;
  };
  state?: TState;
  question_set_id?: number;
  answer_set?: IAnswerSet;
}

export class StudentEvaluation extends BaseModel<IEvaluation> {
  constructor() {
    super({
      name: 'teaching_evaluation',
      resource: 'evaluation',
      namespace: '/teaching/student',
      parentResource: 'lessons',
    });
  }

  createEvaluation(lessonId: number) {
    return this.request.post<IEvaluation>(`/teaching/student/lessons/${lessonId}/evaluation`);
  }
  updateEvaluation(lessonId: number, state: TState, answerAttributes: Array<{ id: number; value: string }>) {
    return this.request.patch(`/teaching/student/lessons/${lessonId}/evaluation`, {
      teaching_evaluation: {
        times: 2,
        state,
        answers_attributes: answerAttributes,
      },
    });
  }
  deleteEvaluation(lessonId: number) {
    return this.request.delete(`/teaching/student/lessons/${lessonId}/evaluation`);
  }
  findEvaluation(lessonId: number) {
    return this.request.get<IEvaluation>(`/teaching/student/lessons/${lessonId}/evaluation`);
  }
}

export default new StudentEvaluation();
