/**
 * lesson_plan
 * 课程资源
 * has_many: lesson_items
 * belongs_to: course_set
 */

import BaseModel from '@/models/BaseModel';
import { compact } from 'lodash';
import { ILessonItem } from './lesson_item';

export interface ILessonPlan {
  id?: number;
  created_at?: string;
  updated_at?: string;
  course_set_id?: number;
  sections?: string;
  title?: string;
  body?: string;
  subject?: string;
  position?: number;
  lesson_items?: ILessonItem[];
}

export interface ILessonPlansResponse {
  lesson_plans: ILessonPlan[];
}

export class LessonPlan extends BaseModel<ILessonPlan, ILessonPlansResponse> {
  constructor(role: 'student' | 'admin' | 'teacher/current_semester' | 'teacher' | 'teacher/inspect') {
    super({
      name: 'lesson_plan',
      resource: 'lesson_plans',
      namespace: '/teaching',
      parentResource: 'course_catalogs',
      role,
    });
  }

  // teacher | student
  indexByCourse(courseId: number, params: IObject = {}) {
    return this.request.get(`/teaching/${this.role}/courses/${courseId}/course_set/lesson_plans`, {
      params: {
        page: 1,
        per_page: 1000,
        ...params,
      },
    });
  }

  // teacher | student
  findByCourse(courseId: number, id: number) {
    return this.request.get(`/teaching/${this.role}/courses/${courseId}/course_set/lesson_plans/${id}`);
  }

  static getSectionTitle(plan: ILessonPlan) {
    const sections = compact((plan.sections || '').split(','));
    return sections.length ? sections.map(o => `课时${o}`).join('、') : '无安排课时';
  }
}
