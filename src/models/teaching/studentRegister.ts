import BaseModel from '../BaseModel';
import { IFile } from '../file';

export interface IRegister {
  id?: number;
  created_at?: string;
  updated_at?: string;
  user_type?: string;
  user_id?: number;
  source_type?: 'Teaching::Lesson';
  source_id?: number;
  state?: 'done' | 'undo' | 'late';
  type?: string;
  lon?: any;
  lat?: any;
  student: {
    name: string;
    avatar: IFile;
  };
}

export class StudentRegister extends BaseModel<IRegister> {
  constructor() {
    super({
      name: 'register',
      resource: 'register',
      namespace: '/comm/user',
    });
  }

  // 学生获取点名详情
  findByNonce(nonce: string) {
    return this.request.get<IRegister>(`/comm/user/register?nonce=${nonce}`);
  }
}

export default new StudentRegister();
