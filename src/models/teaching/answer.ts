import BaseModel from '@/models/BaseModel';

export interface IAnswer {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: any;
  answer_set_id?: number;
  question_id?: number;
  value?: string;
  meta?: any;
  question?: IQuestion;
}

export interface IQuestion {
  id: number;
  created_at: string;
  updated_at: string;
  type: string;
  title: string;
  choices: {
    options: Array<{ key: string; value: string }>;
  };
  question_set_id: number;
  position: number;
  teaching_evaluate_scores: ITeachingEvaluateScore[];
}

export interface ITeachingEvaluateScore {
  id: number;
  created_at: string;
  updated_at: string;
  question_id: number;
  score: number;
  evaluate_item_id: number;
  value: string;
  title: string;
  content: string;
}

interface IFormData {
  value: string;
}

interface IResponse {
  answers: IAnswer[];
}

export class Answer extends BaseModel<IAnswer, IResponse> {
  constructor() {
    super({
      name: 'answer',
      resource: 'answers',
      namespace: '/comm/student',
      parentResource: 'answer_sets',
    });
  }

  findByAnswerSet(answerSetId: number, answerId: number) {
    return this.request.get<IAnswer>(`/comm/student/answer_sets/${answerSetId}/answers/${answerId}`);
  }

  updateByAnswerSet(answerSetId: number, answerId: number, answer: IFormData) {
    return this.request.patch<IAnswer>(`/comm/student/answer_sets/${answerSetId}/answers/${answerId}`, {
      answer,
    });
  }
}

export default new Answer();
