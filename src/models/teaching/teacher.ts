import BaseModel from '../BaseModel';

export interface ITeacher {
  id?: number;
  name?: string;
  code?: string;
  state?: string;
  type?: 'Teacher';
  department_name?: string;
  department_short_name?: string;
  department_ids?: any[];
  school_id?: number;
  roles_name?: string[];
}

interface IResponse {
  teachers: ITeacher[];
}

export class Teacher extends BaseModel<ITeacher, IResponse> {
  constructor() {
    super({
      name: 'teacher',
      resource: 'teachers',
      namespace: '/hr',
      role: 'teacher',
    });
  }

  info() {
    return this.request('/hr/teacher/info');
  }
}

export default new Teacher();
