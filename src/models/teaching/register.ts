import BaseModel from '@/models/BaseModel';
import { IFile } from '../file';

export type RegisterState = 'done' | 'undo' | 'late' | 'absent';

export interface IRegister {
  id?: number;
  created_at?: string;
  updated_at?: string;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: number;
  state?: RegisterState;
  type?: string;
  lon?: any;
  lat?: any;
  student: {
    name: string;
    avatar: IFile;
  };
}

interface IResponse {
  registers: IRegister[];
}

interface NonceInfo {
  nonce: string;
  times: number;
  register_start_time: number;
  register_end_time: number;
  evaluate_start_time: number;
  evaluate_end_time: number;
}

export class Register extends BaseModel<IRegister, IResponse> {
  constructor(role: 'student' | 'teacher') {
    super({
      name: 'lesson_register',
      resource: 'registers',
      namespace: '/teaching',
      parentResource: 'lessons',
      role,
    });
  }

  init(lessonId: number) {
    return this.request.post<void>(`/teaching/${this.role}/lessons/${lessonId}/registers/init`);
  }

  checkInfo(lessonId: number) {
    return this.request.get<NonceInfo>(`/teaching/${this.role}/lessons/${lessonId}/registers/nonce`);
  }

  findByLesson(lessonId: number, id: number) {
    return this.request.get<IRegister>(`/teaching/${this.role}/lessons/${lessonId}/registers/${id}`);
  }

  updateByLesson(lessonId: number, register: IRegister) {
    return this.request.patch(`/teaching/${this.role}/lessons/${lessonId}/registers/${register.id}`, {
      [this.name]: register,
    });
  }
  // 学生签到
  checkInLesson(lessonId: number) {
    return this.request.patch<{ state: RegisterState }>(`/teaching/student/lessons/${lessonId}/register`, {
      register: {
        times: 1,
      },
    });
  }

  static get stateMap() {
    return {
      done: { value: 'done', label: '已签到', type: 'success' },
      undo: { value: 'undo', label: '未签到', type: 'warning' },
      late: { value: 'late', label: '迟到', type: 'warning' },
      absent: { value: 'absent', label: '缺席', type: 'danger' },
    } as IObject;
  }
}
