/**
 * lesson_item
 * 课程资源元素
 * belongs_to: lesson_plan
 */

import BaseModel from '@/models/BaseModel';
import { IRecorder } from '@/service/recorder';

export interface ILessonItem {
  id?: number;
  created_at?: string;
  updated_at?: string;
  lesson_plan_id?: number;
  item_type?: string;
  attachments?: IObject;
  title?: string;
  position?: number;
  student_recorder?: IRecorder;
}

interface IResponse {
  lesson_items: ILessonItem[];
}

export class LessonItem extends BaseModel<ILessonItem, IResponse> {
  constructor(role: 'admin' | 'teacher/current_semester') {
    super({
      name: 'lesson_item',
      resource: 'lesson_items',
      namespace: '/teaching',
      parentResource: 'lesson_plans',
      role,
    });
  }

  static get itemTypeZh() {
    return {
      audio: '音频',
      video: '视频',
      application: '附件',
      image: '图片',
      link: '链接',
    };
  }
}
