import { AxiosRequestConfig } from 'axios';
import BaseModel from './BaseModel';

export interface IAccount {
  id?: string;
  _id?: string;
  type?: 'Teacher' | 'Student' | 'Expert';
  name?: string;
  account?: string;
  token?: string;
  fileToken?: string;
  school?: string;
  password?: string;
}

export interface IAuthorizeData {
  appId: string; // appId
  response_type: 'code'; // 填写code
  redirect_url: string; // 注册应用时填写的回调地址
  state: string; // 返回时原样返回此参数,客户端可通过这个进行合法性验证
}
// cas 登录
export interface IThirdAuthAccount {
  thirdAuthId: string;
  account: string;
  password: string;
}
// IDAP 登录
export interface IAuthAccount {
  account: string;
  password: string;
}

export class Session extends BaseModel<IAccount> {
  constructor() {
    super({
      name: 'session',
      resource: 'session',
      namespace: '/auth',
      rootPath: '/soa-auth',
    });
  }

  // =============== account ===============
  signIn(account: IAuthAccount) {
    return this.request.post<IAccount>('/account/signin', account);
  }
  signOut(config?: AxiosRequestConfig) {
    return this.request.delete('/auth/session', config);
  }
  // 注册
  signUp(user: IAccount) {
    return this.request.post('/account/signup', user);
  }
  // 批量注册
  bulkSignUp(users: IAccount[]) {
    return this.request.post('/account/signup/bulk', {
      users,
    });
  }
  delete(account: string) {
    return this.request.delete(`/account/users/${account}`);
  }
  // 获取个人身份二维码，有效期 15 秒
  getIdCode() {
    return this.request.post<{ code: string }>('/user/id_code');
  }
  checkIdCode(code: string) {
    return this.request.post<IAccount>('/user/id_code/check', {
      code,
    });
  }
  // =============== oauth ===============
  // token 验证
  checkToken() {
    return this.request.post<IAccount>('/auth/session/check');
  }
  // 用户授权
  authorize(authorizeData: IAuthorizeData) {
    return this.request.post('/oauth2/authorize', authorizeData);
  }
  // =============== third auth ===============
  async thirdSignIn(account: IThirdAuthAccount) {
    const { status, data } = await this.request.post('/auth/session', account);
    return {
      status,
      data: {
        _id: data.code,
        type: data.type,
        name: data.name,
        account: data.code,
        token: data.token,
        fileToken: data.file_token,
        school: data.school,
      } as IAccount,
    };
  }
  // =============== other helper functions ===============
  static getNumberAsciiCode(o: number | string) {
    return Number(o) + 48;
  }
}

export default new Session();
