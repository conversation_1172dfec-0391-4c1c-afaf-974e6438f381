import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "auth" */ './Login.vue'),
    meta: {
      title: '登录',
      requireAuth: false,
      roles: ['Student', 'Teacher'],
    },
  },
  {
    path: '/wechat_auth',
    name: 'wechatAuth',
    component: () => import(/* webpackChunkName: "auth" */ './WechatAuth.vue'),
    meta: {
      title: '微信登录',
      requireAuth: false,
      roles: ['Student', 'Teacher'],
    },
  },
  {
    path: '/oauth',
    name: 'oauth',
    component: () => import(/* webpackChunkName: "oauth" */ './Oauth.vue'),
    meta: {
      title: '微信登录',
      requireAuth: false,
      roles: ['Student', 'Teacher'],
    },
  },
  {
    path: '/teacher_info',
    name: 'teacher_info_show',
    component: () => import(/* webpackChunkName: "teacher_info_show" */ './TeacherInfo.vue'),
    meta: {
      title: '个人信息',
      requireAuth: false,
      roles: ['Student', 'Teacher'],
    },
  },
];

export default routes;
