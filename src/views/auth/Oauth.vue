<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import { WechatService } from '@/service/wechat';
import { OauthService } from '@/service/oauth';

@Component
export default class Oauth extends Vue {
  loading: boolean = true;
  code: string = '';
  paramsvalue: string = '';
  openid: string = '';
  debug: boolean = process.env.VUE_APP_WECHAT_AUTH === 'false';

  mounted() {
    this.paramsvalue = this.$route.query.paramsvalue as string;
    // 判断从外部点入的直接清除token
    // const { internal } = this.$route.query;
    // if (internal === 'redirect') {
    //   this.clearToken();
    // }
    const { internal } = this.$route.query;
    if (internal === 'redirect') {
      this.enter();
      return;
    }

    if (this.paramsvalue) {
      this.oauthAuthorize();
    } else {
      this.wechatAuthorize();
    }
  }

  async oauthWithToken() {
    try {
      // 获取openid
      const {
        data: { token },
      } = await OauthService.fetchToken();
      const {
        data: { openid },
      } = await WechatService.getOpenid(this.code);
      this.openid = openid;

      window.localStorage.setItem('openid', openid);
      const redirectUri = `http://wchattest.stiei.edu.cn/campus-mobile/oauth`;
      const url = 'http://wechatoauth.stiei.edu.cn/oauth/authopenid';
      const href = `${url}?token=${token}&openid=${openid}&redirectUri=${redirectUri}`;
      window.location.href = href;
    } catch (error) {
      this.goToLogin();
    }
  }

  // 1、Development Env
  async debugAuthorize() {
    try {
      await sessionStore.check();
      this.enter();
    } catch (error) {
      this.goToLogin();
    }
  }

  oauthAuthorize() {
    this.oauthSignIn(this.paramsvalue);
  }

  async oauthSignIn(paramsvalue: string) {
    try {
      const openid = window.localStorage.getItem('openid') || this.openid;
      const {
        data: { account },
      } = await OauthService.fetchAccount(paramsvalue, openid);

      // 合并条件：如果用户已登录并且用户工号与 account 不一致，才需要重新登录
      if (!sessionStore.token || sessionStore.currentUser.code !== account) {
        await sessionStore.oauthSignIn(paramsvalue, openid, account);
      }

      // 绑定微信号
      WechatService.bindWechat(openid);
      this.enter();
    } catch (error) {
      this.goToLogin();
    }
  }

  // 2、Production Env
  wechatAuthorize() {
    this.code = this.$route.query.code as string;
    if (this.code) {
      this.validateToken();
    } else {
      WechatService.auth('oauth');
    }
  }

  validateToken() {
    // if (sessionStore.token) {
    //   this.checkToken();
    // } else {
    //   this.oauthWithToken();
    //   // this.signInWithWechatCode();
    // }
    this.oauthWithToken();
  }

  // 强制清除token
  async clearToken() {
    if (sessionStore.token) {
      const redirectPath = this.$utils.getRedirectPath();
      await sessionStore.signOut();
      window.localStorage.clear();
      this.$utils.setRedirectPath(redirectPath);
    }
  }

  async checkToken() {
    try {
      await sessionStore.check();
      this.bindWechat();
    } catch (error) {
      this.oauthWithToken();
      // this.signInWithWechatCode();
    }
  }

  async bindWechat() {
    try {
      await WechatService.bind(this.code);
      this.enter();
    } catch (error) {
      this.$message.warning('绑定微信失败');
      this.enter();
    }
  }

  async signInWithWechatCode() {
    try {
      await sessionStore.wechatSignIn(this.code);
      this.enter();
    } catch (error) {
      this.oauthWithToken();
      // this.goToLogin();
    }
  }

  enter() {
    this.loading = false;
    // 使用 redirectUrl 查询参数，如果不存在则使用保存的 redirectPath
    const redirectTo = this.$utils.getRedirectPath() || '/portal/user/todo';
    if (redirectTo.includes('oauth')) {
      this.$router.replace('/portal/user/todo').catch(() => {});
    } else {
      this.$router
        .replace({
          path: redirectTo,
          query: { ...this.$route.query, internal: 'redirect' },
        })
        .catch(() => {});
    }
  }

  goToLogin() {
    this.loading = false;
    this.$router.replace('/login').catch(() => {});
  }
}
</script>

<template lang="pug">
.container(v-loading='loading')
</template>

<style lang="stylus" scoped>
.container
  position fixed
  top 0
  right 0
  bottom 0
  left 0
</style>
