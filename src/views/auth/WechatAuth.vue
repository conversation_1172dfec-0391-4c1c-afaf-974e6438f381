<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import session from '@/models/session';
import { WechatService } from '@/service/wechat';

@Component
export default class WechatAuth extends Vue {
  loading: boolean = true;
  code: string = '';
  debug: boolean = process.env.VUE_APP_WECHAT_AUTH === 'false';

  mounted() {
    if (this.debug) {
      this.debugAuthorize();
    } else {
      this.wechatAuthorize();
    }
  }

  // 1、Development Env
  async debugAuthorize() {
    try {
      await sessionStore.check();
      this.enter();
    } catch (error) {
      this.goToLogin();
    }
  }

  // 2、Production Env
  wechatAuthorize() {
    this.code = this.$route.query.code as string;
    if (this.code) {
      this.validateToken();
    } else {
      WechatService.auth('wechat_auth');
    }
  }

  validateToken() {
    if (sessionStore.token) {
      this.checkToken();
    } else {
      this.signInWithWechatCode();
    }
  }

  async checkToken() {
    try {
      await sessionStore.check();
      this.bindWechat();
    } catch (error) {
      this.signInWithWechatCode();
    }
  }

  async bindWechat() {
    try {
      await WechatService.bind(this.code);
      this.enter();
    } catch (error) {
      this.$message.warning('绑定微信失败');
      this.enter();
    }
  }

  async signInWithWechatCode() {
    try {
      await sessionStore.wechatSignIn(this.code);
      this.enter();
    } catch (error) {
      this.goToLogin();
    }
  }

  enter() {
    this.loading = false;
    this.$router.replace(this.$utils.getRedirectPath()).catch(() => {});
  }

  goToLogin() {
    this.loading = false;
    this.$router.replace('/login').catch(() => {});
  }
}
</script>

<template lang="pug">
.container(v-loading="loading")
</template>

<style lang="stylus" scoped>
.container
  position fixed
  top 0
  right 0
  bottom 0
  left 0
</style>
