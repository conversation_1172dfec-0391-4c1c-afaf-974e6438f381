<template lang="pug">
NavContainer.container(title="基本信息" :loading="formworkStore.loading")
  van-cell-group
    van-cell(is-link)
      Avatar(
        :name="currentUser.name || currentUser.code"
        size="40px"
        :bgColor="currentUser.sex === '女' ? '#F28E36' : '#58A8EF'"
        slot="icon")
      .name(slot="title") {{ currentUser.name || currentUser.code }}
  van-cell-group(title="基本信息")
    van-cell(v-for="(item, index) in dataKeys" :key="index")
      template(slot="icon")
        .cell-label {{ item.label }}
      template(slot="title")
        .cell-value
          span(v-if="['identity_type'].includes(item.key)")
            | {{ currentUser[item.key] === '1' ? '身份证' : currentUser[item.key] }}
          span(v-else) {{ currentUser[item.key] }}
  van-cell-group(v-for="(group, index) in groups" :key="index" :title="group.name")
    van-cell(v-for="(item, key) in group.forms" :key="key")
      template(slot="icon")
        .cell-label {{ item.name }}
      template(slot="title")
        .cell-value
          span(v-if="item.layout.component === 'date'")
            | {{ currentUser.dynamic_attrs[item.key] | format('YYYY-MM-DD') }}
          span(v-else-if="item.layout.component === 'datetime'")
            | {{ currentUser.dynamic_attrs[item.key] | format('YYYY-MM-DD HH:mm:ss') }}
          span(v-else-if="item.layout.component === 'time'")
            | {{ currentUser.dynamic_attrs[item.key] | format('HH:mm:ss') }}
          span(v-else-if="item.layout.component === 'contacts'")
            | {{ getContacts(currentUser.dynamic_attrs[item.key]) }}
          span(v-else)
            | {{ currentUser.dynamic_attrs[item.key] }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import formworkStore from '@/store/modules/comm/formwork.store';

@Component({
  components: {},
})
export default class Info extends Vue {
  private groups: any[] = [];
  get currentUser() {
    const currentUser: any = sessionStore.currentUser || {};
    return {
      ...currentUser,
      degree_zh: this.$tools.formatDegree(currentUser.degree),
      educations_zh: this.$tools.formatEducation(currentUser.education),
      to_school_at_zh: currentUser.to_school_at ? this.$dayjs(currentUser.to_school_at).format('YYYY-MM-DD') : '',
      leave_school_at_zh: currentUser.leave_school_at
        ? this.$dayjs(currentUser.leave_school_at).format('YYYY-MM-DD')
        : '',
    };
  }
  get formworkStore() {
    return formworkStore;
  }
  private dataKeys: any[] = [
    {
      label: '姓名拼音',
      key: 'name_pinyin',
    },
    {
      label: '性别',
      key: 'sex',
    },
    {
      label: '工号',
      key: 'code',
    },
    {
      label: '手机号',
      key: 'phone',
    },
    {
      label: '邮箱',
      key: 'email',
    },
    {
      label: '所属部门',
      key: 'department_name',
    },
    {
      label: '所属学院',
      key: 'college_name',
    },
    {
      label: '出生日期',
      key: 'birthday',
    },
    {
      label: '证件类型',
      key: 'identity_type',
    },
    {
      label: '证件号',
      key: 'identity_id',
    },
    {
      label: '进校时间',
      key: 'to_school_at_zh',
    },
    {
      label: '离校时间',
      key: 'leave_school_at_zh',
    },
    {
      label: '用人方式',
      key: 'work_way',
    },
    {
      label: '学位',
      key: 'degree_zh',
    },
    {
      label: '学历',
      key: 'educations_zh',
    },
  ];

  public mounted() {
    this.fetchData();
  }

  public async fetchData() {
    const params = {
      page: 1,
      per_page: 10,
      q: {
        type_eq: 'Formwork::Teacher',
      },
    };
    const { data } = await formworkStore.fetch(params);
    const formwork = data.formworks[0] || {};
    if (formwork.id) {
      this.groups = formwork.meta && formwork.meta.groups ? formwork.meta.groups : [];
    }
  }
}
</script>

<style lang="stylus" scoped>
.container
  width 100%
  background #f5f5f5
  .van-cell-group
    margin-bottom 16px
    .van-cell
      display flex
      align-items center
      padding 16px 20px
      .name
        margin-left 16px
        font-weight 500
        font-size 18px
        line-height 20px
      .cell-label
        width 74px
        color #808080
        font-size 14px
        line-height 20px
      .cell-value
        color #383838
        font-size 14px
        line-height 20px
</style>
