<template lang="pug">
  .container
    .logo
      img(src="@/assets/images/logo.png", alt="LOGO" height="30px" width="30px")
    h3.title 登录上海电子信息职业技术学院
    h4.subtitle 欢迎回来
    //- 新生登录
    SimpleForm.form(ref="loginForm" v-if="isWelcomeFlow")
      InputField(name="account" v-model.trim="account.account" icon="manager" placeholder="身份证号")
      InputField(name="password" v-model.trim="account.password" icon="lock" placeholder="身份证号后6位")
      van-button.button-lg(
        type="info"
        native-type="submit"
        :disabled="!enabled"
        :loading="loading"
        size="large"
        block
        @click="welcomeSubmit")
        | 新生登录
    //- 日程登录
    SimpleForm.form(ref="loginForm" v-else)
      InputField(name="account" v-model.trim="account.account" icon="manager" placeholder="工号/学号")
      InputField(name="password" v-model.trim="account.password" type="password" icon="lock" placeholder="密码")
      van-button.button-lg(
        type="info"
        native-type="submit"
        :disabled="!enabled"
        :loading="loading"
        size="large"
        block
        @click="submit")
        | 登录
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { baseStore } from '@/store/modules/base.store';
import sessionStore from '@/store/modules/session.store';
import { IThirdAuthAccount } from '@/models/session';

@Component
export default class Login extends Vue {
  account: IThirdAuthAccount = {
    thirdAuthId: '5d2ee7620047759f710bce75',
    account: '',
    password: '',
  };
  isWelcomeFlow: boolean = false;

  get enabled() {
    return this.account.account && this.account.password;
  }

  get loading() {
    return baseStore.loading;
  }

  mounted() {
    sessionStore.RESET();
    this.isWelcomeFlow = decodeURIComponent(window.location.search).includes('/studying/welcome');
  }

  onLogin() {
    (this.$refs.loginForm as any).submit();
  }

  async submit() {
    try {
      await sessionStore.signIn(this.account);
      this.$router.replace('/wechat_auth').catch(() => {});
    } catch (error) {
      this.$message.error('账号或密码错误');
    }
  }

  async welcomeSubmit() {
    try {
      this.account.password = this.account.password.toLocaleUpperCase();
      await sessionStore.signIn(this.account);
      this.$router.replace('/studying/welcome').catch(() => {});
    } catch (error) {
      this.$message.error('信息校验失败，请检查个人信息');
    }
  }
}
</script>

<style lang="stylus" scoped>
.container
  padding 20px
  height 100vh
  background #fff
  .logo
    margin-bottom 60px
  .title
    margin-bottom 16px
    color rgba(56, 56, 56, 1)
    font-weight 500
    font-size 26px
    font-family PingFangSC-Medium
    line-height 1.2
  .subtitle
    margin-bottom 16px
    color rgba(166, 166, 166, 1)
    font-weight 400
    font-size 16px
    font-family PingFangSC-Regular
    line-height 22px
  .form
    padding 32px 0
    button
      margin-top 32px
</style>
