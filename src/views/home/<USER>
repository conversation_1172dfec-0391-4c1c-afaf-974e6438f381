<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class HomeNotifications extends Vue {}
</script>

<template lang="pug">
.container
  .content.scroll-y
    h3.text-center 即将上线
  van-tabbar.tabbar(:value="1" fixed)
    van-tabbar-item(icon="apps-o" to="/" :replace="true") 所有应用
    van-tabbar-item(icon="comment-o" to="/notifications" :replace="true") 消息通知
    van-tabbar-item(icon="user-o" to="/me" :replace="true") 我的
</template>

<style lang="stylus" scoped>
.container
  background rgba(245, 245, 245, 1)
  height 100%
  padding-bottom 50px
  .content
    height 100%
    h3
      margin-top 30px
</style>
