<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import instanceModel, { InstanceType } from '@/models/bpm/instance';
import sessionStore from '../../store/modules/session.store';
import { WorkflowTypes } from '../../models/bpm/workflow';

@Component({
  components: {},
})
export default class HomeApps extends Vue {
  financeCount: number = 0;
  timer: any = null;

  get isNewcommerStudent() {
    return sessionStore.currentUser.state === 'newcommer';
  }
  get isStudent() {
    return sessionStore.account.type === 'Student';
  }
  get WorkflowTypes() {
    return WorkflowTypes;
  }

  mounted() {
    this.loadFinanceStatistic();
  }
  beforeDestroy() {
    clearTimeout(this.timer);
  }

  async loadFinanceStatistic() {
    const { data } = await instanceModel.statistic(InstanceType.Voucher);
    this.financeCount = data.total!;
    this.timer = setTimeout(this.loadFinanceStatistic, 10000);
  }
}
</script>

<template lang="pug">
.container
  .content.scroll-y
    AppGroup(v-if='isNewcommerStudent')
      AppGroupItem(to='/studying/welcome', :icon='require("@/assets/images/meeting/icon_activity.png")', name='迎新系统')
    AppGroup(v-else-if='isStudent')
      AppGroupItem(
        to='/teaching/student/week_schedule',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='课程教学平台'
      )
      AppGroupItem(
        to='/pt/student/register',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='顶岗实习系统'
      )
      AppGroupItem(
        to='/ep/student/register',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='疫情统计系统'
      )
      AppGroupItem(to='/bpm/workflows', :icon='require("@/assets/images/meeting/icon_activity.png")', name='业务流程申请')
      AppGroupItem(
        to='http://wchattest.stiei.edu.cn/epay/thirdcharge/thirdcharge',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='一卡通充值'
      )
    AppGroup(v-else)
      AppGroupItem(
        to='/meeting/activities',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='活动会议系统'
      )
      AppGroupItem(
        to='/hr/teacher/contacts',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='人事管理系统'
      )
      AppGroupItem(
        to='/teaching/teacher/week_schedule',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='课程教学平台'
      )
      AppGroupItem(
        to='/assess/activities',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='人事考核系统'
      )
      AppGroupItem(
        to='/finance/teacher',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='资金卡管理',
        :count='financeCount'
      )
      AppGroupItem(
        to='/pt/teacher/activities',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='顶岗实习系统'
      )
      AppGroupItem(
        to='/ep/teacher/register',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='疫情统计系统'
      )
      AppGroupItem(to='/bpm/workflows', :icon='require("@/assets/images/meeting/icon_activity.png")', name='业务流程申请')
      AppGroupItem(
        :to='`/bpm/workflows?type=${WorkflowTypes.Wechat}`',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='微信矩阵管理'
      )
      AppGroupItem(
        :to='`/conference/teacher/week_schedule`',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='会议预约系统'
      )
      AppGroupItem(to='/portal/user/home', :icon='require("@/assets/images/meeting/icon_activity.png")', name='统一门户')
      AppGroupItem(
        to='http://wchattest.stiei.edu.cn/epay/thirdcharge/thirdcharge',
        :icon='require("@/assets/images/meeting/icon_activity.png")',
        name='一卡通充值'
      )
  van-tabbar.tabbar(:value='0', fixed, safe-area-inset-bottom)
    van-tabbar-item(icon='apps-o', to='/', :replace='true') 所有应用
    van-tabbar-item(icon='comment-o', to='/notifications', :replace='true') 消息通知
    van-tabbar-item(icon='user-o', to='/me', :replace='true') 我的
</template>

<style lang="stylus" scoped>
.container
  padding-bottom 50px
  height 100%
  background rgba(245, 245, 245, 1)
  .content
    height 100%
</style>
