import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/demo',
    name: 'home_index',
    component: () => import(/* webpackChunkName: "home" */ './Index.vue'),
    meta: {
      title: '上海电子信息职业技术学院',
      roles: ['Student', 'Teacher'],
      requireAuth: true,
    },
  },
  {
    path: '/',
    redirect: 'portal/user/home',
    // component: () => import(/* webpackChunkName: "home" */ './Apps.vue'),
    meta: {
      title: '上海电子信息职业技术学院',
      roles: ['Student', 'Teacher'],
      requireAuth: true,
    },
  },
  {
    path: '/applications',
    // redirect: 'portal/user/home',
    component: () => import(/* webpackChunkName: "home" */ './Apps.vue'),
    meta: {
      title: '上海电子信息职业技术学院',
      roles: ['Student', 'Teacher'],
      requireAuth: true,
    },
  },
  {
    path: '/notifications',
    name: 'home_notifications',
    component: () => import(/* webpackChunkName: "home" */ './Notifications.vue'),
    meta: {
      title: '上海电子信息职业技术学院',
      roles: ['Student', 'Teacher'],
      requireAuth: true,
    },
  },
  {
    path: '/me',
    name: 'home_me',
    component: () => import(/* webpackChunkName: "home" */ './Me.vue'),
    meta: {
      title: '上海电子信息职业技术学院',
      roles: ['Student', 'Teacher'],
      requireAuth: true,
    },
  },
];

export default routes;
