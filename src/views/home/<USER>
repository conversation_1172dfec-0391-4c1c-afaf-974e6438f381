<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import attendanceStore from '@/store/modules/meeting/attendance.store';
import { OauthService } from '@/service/oauth';
import sessionStore from '../../store/modules/session.store';
import session from '../../models/session';
import IDQrCode from '../../components/session/IDQrCode.vue';
import wechatSdk from '../../utils/wechatSdk';
import studentRegister from '../../models/teaching/studentRegister';
import { WechatService } from '../../service/wechat';

@Component({
  components: {
    IDQrCode,
  },
})
export default class HomeMe extends Vue {
  loading: boolean = false;
  isDevelopment: boolean = process.env.VUE_APP_WECHAT_AUTH === 'false';
  fetchTime: string = '';
  haveGoingCard: boolean = false;
  haveComingCard: boolean = false;
  cardColorMap: IObject = {
    // 返校离校卡: '#6846d5',
    返校离校卡: '#ad94f7',
    返校卡: '#46d5af',
    离校卡: '#4497d1',
    我的二维码: '#fff',
  };

  get user() {
    return sessionStore.currentUser;
  }
  get account() {
    return sessionStore.account;
  }

  async signOut() {
    if (this.loading) return;
    this.loading = true;
    if (this.isDevelopment) {
      this.deleteSession();
    } else {
      WechatService.unbind().finally(this.deleteSession);
    }
  }

  get attendanceStore() {
    return attendanceStore;
  }

  get cardType() {
    if (this.haveGoingCard && this.haveComingCard) {
      return '返校离校卡';
    }
    if (this.haveGoingCard) {
      return '离校卡';
    }
    if (this.haveComingCard) {
      return '返校卡';
    }
    return '我的二维码';
  }

  async checkGoingInfo() {
    const { data } = await this.attendanceStore.fetch({
      page: 1,
      q: { state_eq: '已报名', meeting_activity_title_eq: '离校申请' },
    });

    this.haveGoingCard = this.attendanceStore.records.length !== 0;
  }

  async checkComingInfo() {
    const { data } = await this.attendanceStore.fetch({
      page: 1,
      q: { state_eq: '已报名', meeting_activity_title_eq: '返校申请' },
    });

    this.haveComingCard = this.attendanceStore.records.length !== 0;
  }

  deleteSession() {
    const openid = window.localStorage.getItem('openid');
    if (openid) {
      OauthService.destroy(openid);
    }
    session.signOut().finally(() => {
      window.localStorage.clear();
      this.loading = false;
      // this.$router.replace('/login');
      window.location.href = 'http://wchattest.stiei.edu.cn/wx-test/?#/home';
    });
  }

  scanCode() {
    wechatSdk.scanQRCode(async (code: string) => {
      try {
        this.loading = true;
        const { data } = await studentRegister.findByNonce(code);
        // 课程签到码
        if (data.source_type === 'Teaching::Lesson') {
          this.$router.push(`/teaching/student/lessons/${data.source_id}?code=${code}`);
        }
        this.loading = false;
      } catch (error) {
        this.loading = false;
      }
    });
  }

  onInfo() {
    if (this.account.type === 'Teacher') {
      this.$router.push('/teacher_info');
    }
  }

  setFetchTime() {
    this.checkGoingInfo();
    this.checkComingInfo();
    this.fetchTime = this.$dayjs().format('YYYY/MM/DD HH:mm:ss');
  }
}
</script>

<template lang="pug">
.container
  .content.scroll-y
    .user-info
      van-row
        van-col(:span="16")
          .name {{ user.name }}
          .info 工号：{{ user.code }}
          .info 部门：{{ user.department_name }}
        van-col(:span="8")
          .right(@click="onInfo")
            Avatar(:name="user.name" size="50px")
    .sticky-content(v-loading="loading")
      .qrcode-card(:style="{ backgroundColor: cardColorMap[cardType]}")
        .tips(:style="{ color: cardType === '我的二维码' ? 'rgba(128, 128, 128, 1)': 'white' }") {{ cardType }}
        IDQrCode(@fetched="setFetchTime").qrcode
        .fetch-time(:style="{ color: cardType === '我的二维码' ? 'rgba(128, 128, 128, 1)': 'white' }") {{ fetchTime }}
      .menus
        van-cell(title="扫一扫" icon="scan" is-link @click="scanCode")
      .menus
        van-cell(title="注销登录" icon="share" is-link @click="signOut")
  van-tabbar.tabbar(:value="2" fixed safe-area-inset-bottom)
    van-tabbar-item(icon="apps-o" to="/" :replace="true") 所有应用
    van-tabbar-item(icon="comment-o" to="/notifications" :replace="true") 消息通知
    van-tabbar-item(icon="user-o" to="/me" :replace="true") 我的
</template>

<style lang="stylus" scoped>
.container
  padding-bottom 50px
  height 100%
  .content
    height 100%
    .user-info
      padding 26px 20px 78px
      background #fff
      .name
        margin-bottom 10px
        color rgba(10, 8, 5, 1)
        font-weight 500
        font-size 20px
        line-height 28px
      .info
        margin-bottom 6px
        color rgba(128, 128, 128, 1)
        font-weight 400
        font-size 15px
        line-height 20px
      .right
        text-align right
    .sticky-content
      margin-top -80px
      padding 20px
      width 100%
      .qrcode-card
        margin-bottom 1px
        padding 18px 18px 28px
        border-radius 4px
        background rgba(255, 255, 255, 1)
        box-shadow 0px 1px 6px 0px rgba(0, 0, 0, 0.06)
        text-align center
        .tips
          margin-bottom 12px
          color white
          font-weight 500
          font-size 17px
          line-height 24px
        .qrcode
          margin 0 auto
          width 150px
          height 150px
        .fetch-time
          margin-top 10px
          color white
      .menus
        margin-bottom 12px
</style>
