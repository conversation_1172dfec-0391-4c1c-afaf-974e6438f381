<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import sessionStore from '../../store/modules/session.store';

@Component({
  components: {},
})
export default class Home extends Vue {
  get isStudent() {
    return sessionStore.account.type === 'Student';
  }
}
</script>

<template lang="pug">
.container
  .content.scroll-y
    h3.text-center 即将上线
  van-tabbar.tabbar(:value="0" fixed safe-area-inset-bottom)
    van-tabbar-item(icon="wap-home-o" to="/" :replace="true") 首页
    van-tabbar-item(icon="apps-o" to="/apps" :replace="true") 所有应用
    van-tabbar-item(icon="comment-o" to="/notifications" :replace="true") 消息通知
    van-tabbar-item(icon="user-o" to="/me" :replace="true") 我的
</template>

<style lang="stylus" scoped>
.container
  padding-bottom 50px
  width 100%
  height 100%
  background rgba(245, 245, 245, 1)
  .content
    height 100%
    h3
      margin-top 30px
</style>
