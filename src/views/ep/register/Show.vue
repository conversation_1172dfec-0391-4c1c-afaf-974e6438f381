<template lang="pug">
NavContainer(title="每日健康统计" :loading="registerStore.loading")
  .container
    .header
      .top
        h1 每日健康调查
        p 疫情时期守护师生健康
      .middle
        .flex
          van-icon(name="certificate")
          span 师生隐私保护
        .flex
          van-icon(name="certificate")
          span 数据实时统计
      .bottom
        .name {{ register.user && register.user.name }}
        p 打卡时间：
          span(v-if="register.state === 'done'") {{ $dayjs(register.created_at).format('YYYY.MM.DD HH:mm')}}
          span(v-else) {{ $dayjs(new Date()).format('YYYY.MM.DD HH:mm') }}
        p 打卡地址：{{ register.address || '-' }}
    .main
      .question
        .question-header
          .title
            | 1. 今日居住地址：
          .action(@click='onAreaShow', v-show='!disabled')
            | 选择
        van-cell-group
          van-cell(@click='disabled ? "" : onAreaShow()')
            van-field(required, label='省/市/区(县、乡)')
              template(#input)
                .input
                  | {{ registerAddress.province }} {{ registerAddress.city }} {{ registerAddress.district }}
          van-cell
            van-field(required, label='详细地址（到门牌号）', v-model='registerAddress.address')

      .question(v-for="(question, index) in questions" :key="index")
        .title {{ index + 2 }}. {{ question.title }}

        van-radio-group(v-model="registerMeta[question.key]" :disabled="disabled")
          van-cell-group
            van-cell(
              v-for="(option, key) in question.options"
              :key="key"
              :title="option.value"
              clickable
              @click="disabled ? '' : registerMeta[question.key] = option.key")
              van-radio(slot="icon" :name="option.key")
      van-button(type="info" size="large" :disabled="disabled" @click="onSubmit") 提交
    van-action-sheet(v-model='visibleAreaSelector')
      van-area(
        title='选择地区',
        :area-list='areaList'
        @cancel='visibleAreaSelector = false'
        @confirm='onAreaConfirm'
      )
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { userRegisterStore } from '@/store/modules/ep/register.store';
import areaList from './area-list';

@Component({
  components: {},
})
export default class Show extends Vue {
  registerMeta: any = {};
  registerAddress: { province: string; city: string; district: string; address: string } = {
    province: '',
    city: '',
    district: '',
    address: '',
  };

  areaList = areaList;
  visibleAreaSelector = false;

  get registerStore() {
    return userRegisterStore || {};
  }
  get register() {
    return userRegisterStore.record || {};
  }
  get questions() {
    return this.register.questions || [];
  }
  get disabled() {
    const failure = this.register.created_at
      ? new Date().getTime() > new Date(this.register.created_at).setHours(23, 59, 59, 999)
      : false;
    return failure || this.register.state === 'done';
  }

  mounted() {
    this.fetchData();
  }

  async fetchData() {
    const { data } = await this.registerStore.find(this.$route.params.id);
    this.registerMeta = (data.questions || []).reduce((res: any, item: any) => {
      res[item.key] = data.meta && data.meta[item.key] ? data.meta[item.key] : '';
      return res;
    }, {});

    this.registerAddress = {
      province: data.province,
      city: data.city,
      district: data.district,
      address: data.address,
    };
  }

  async onSubmit() {
    const finish = [...Object.values(this.registerMeta), ...Object.values(this.registerAddress)].every((e: any) => !!e);

    if (!finish) {
      this.$message.warning('请完成答题！');
      return;
    }
    this.update();
  }

  async update() {
    try {
      const obj: any = {
        id: this.$route.params.id,
        noCompare: true,
        meta: this.registerMeta,
        state: 'done',
        created_at: new Date(),
        ...this.registerAddress,
      };
      await this.registerStore.update(obj);
      this.$router.go(-1);
    } catch (error) {
      this.$message.error('提交失败！');
    }
  }

  onAreaShow() {
    this.visibleAreaSelector = true;
  }

  onAreaConfirm(data: { code: string; name: string }[]) {
    this.registerAddress.province = data[0].name;
    this.registerAddress.city = data[1].name;
    this.registerAddress.district = data[2].name;
    this.visibleAreaSelector = false;
  }
}
</script>

<style lang="stylus" scoped>
.container
  overflow auto
  width 100%
  height 100%
  .header
    width 100%
    background linear-gradient(180deg, rgba(49, 170, 255, 1) 0%, rgba(38, 124, 231, 1) 58%, rgba(255, 255, 255, 0) 100%)
    color #fff
    .top
      padding 70px 24px 50px
      width 100%
      h1
        letter-spacing 2px
        font-size 30px
        line-height 30px
      p
        margin-top 12px
        letter-spacing 4px
        font-size 14px
        line-height 20px
    .middle
      display flex
      justify-content space-between
      align-items center
      margin 0px auto
      max-width 240px
      .van-icon
        margin-right 4px
        font-size 18px
      span
        font-weight 500
        font-size 14px
        line-height 20px
    .bottom
      margin 14px 20px 4px
      padding 16px
      border-radius 3px
      background #fff
      box-shadow 0px 0px 4px 0px rgba(0, 0, 0, 0.1)
      .name
        color #383838
        font-weight 500
        font-size 15px
        line-height 24px
      p
        margin-top 6px
        color #808080
        font-size 15px
        line-height 24px
  .main
    padding 0px 20px 40px
    width 100%
    background linear-gradient(180deg, #f5f5f5, #fff)
    .question
      padding-top 20px
      width 100%
      border-top 1px #E9E9E9 solid
      &:first-child
        border none
      .input
        color #808080
      .question-header
        padding-bottom 15px
        display flex
        justify-content space-between
      .title
        color #262626
        font-weight 500
        font-size 15px
        line-height 24px
      .van-radio-group
        margin 18px 0px 24px
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
        .van-cell
          padding 14px 0px
          color rgba(38, 38, 38, 0.65)
          font-size 14px
          line-height 20px
        .van-radio
          margin 0px 20px 0px 14px
</style>
