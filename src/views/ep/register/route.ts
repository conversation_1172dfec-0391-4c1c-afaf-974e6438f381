import { RouteConfig } from '@/interfaces/IRoute';

const Index = () => import(/* webpackChunkName: "ep_student_attendance_register_index" */ './Index.vue');
const Show = () => import(/* webpackChunkName: "ep_student_attendance_register_show" */ './Show.vue');

export default [
  {
    path: '/ep/:role/register',
    name: 'ep_attendance_register_index',
    component: Index,
    meta: {
      title: '签到打卡',
      roles: ['Student', 'Teacher'],
    },
  },
  {
    path: '/ep/:role/register/:id',
    name: 'ep_attendance_register_show',
    component: Show,
    meta: {
      title: '每日健康统计',
      roles: ['Student', 'Teacher'],
    },
  },
] as RouteConfig[];
