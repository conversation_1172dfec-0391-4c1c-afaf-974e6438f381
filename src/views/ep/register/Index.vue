<template lang="pug">
NavContainer(title="打卡" :loading="registerStore.loading" @click-right="showStatistic")
  template(slot="right")
    span 统计
  Empty(desc="暂无打卡任务" v-if="!todayRegister.id")
  template(v-else)
    ListView(
      :data="registerStore.records"
      :loading="registerStore.loading"
      :pullup="!registerStore.finish"
      :pulldown="true"
      emptyText="暂无内容"
      @loadMore="fetchData(registerStore.currentPage + 1)"
      @refresh="fetchData()")
      .attendance-register
        .header(:class="todayRegister.state === 'done' ? 'success-bg' : 'primary-bg'")
          template(v-if="todayRegister.state === 'done'")
            .round(style="margin: 0px auto" @click="onDialog")
              .title.text-success 打卡成功
              .time.text-success {{ $dayjs(todayRegister.created_at).format('HH:mm') }}
          template(v-else)
            .round-loop
              .middle-loop
                .round(@click="onDialog")
                  .title 点击打卡
                  .time {{ currentTime }}
          .date(:style="{marginTop: todayRegister.state === 'done' ? '30px' : '12px'}")
            span(v-if="todayRegister.state === 'done'") {{ $dayjs(todayRegister.created_at).format('YYYY.MM.DD') }}
            span(v-else) {{  $dayjs().format('YYYY.MM.DD') }}
          //- .address(@click="registerAddress")
          //-   van-icon(name="location-o")
          //-   span {{ todayRegister.address || location.address || '重新定位' }}
        .main
          .title 打卡记录
          .register(v-for="(item, index) in registerStore.records" :key="index" @click="onShow(item)")
            .date-box
              .date {{ $dayjs(item.created_at).format('MM-DD')}}
              .time(v-if="item.state === 'done'") {{ $dayjs(item.created_at).format('HH:mm')}}
            .address(:class="{'text-gray': item.state === 'undo'}")
              | {{ item.state === 'undo' ? '未打卡' : item.address || '已打卡' }}

  van-dialog(
    v-model="visible"
    show-cancel-button
    @cancel="visible = false"
    @confirm="onSubmit")
    .van-dialog__header(slot="title") {{ todayRegister.state === 'done' ? '更新打卡' : '确认打卡' }}
    .van-dialog__body
      .cell
        .key 打卡时间
        .value {{ $dayjs().format('YYYY.MM.DD HH:mm')}}
      //- .cell(style="border: none" @click="registerAddress")
      //-   .key 打卡地点
      //-   .value {{ location.address || '重新定位'}}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import wechatSdk from '@/utils/wechatSdk';
import attendanceStore from '@/store/modules/ep/attendance.store';
import { userRegisterStore } from '@/store/modules/ep/register.store';
import sessionStore from '../../../store/modules/session.store';

@Component({
  components: {},
})
export default class Register extends Vue {
  currentTime: string = '';
  attendance: any = {};
  location: any = {
    address: '',
    lon: '',
    lat: '',
  };
  visible: boolean = false;

  get isTeacher() {
    return sessionStore.role === 'teacher';
  }
  get registerStore() {
    attendanceStore.changeNamespace('user');
    return userRegisterStore;
  }
  get todayRegister() {
    const register: any = this.registerStore.records[0] || {};
    const registerDate = register.id ? this.$dayjs(register.created_at).format('YYYY-MM-DD') : '';
    return registerDate === this.$dayjs().format('YYYY-MM-DD') ? register : {};
  }

  mounted() {
    this.nowTimes();
    this.fetchData();
    // this.fetchAddress();
  }

  async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 1,
      q: {
        user_type_eq: sessionStore.account.type,
      },
    };
    const { data } = await attendanceStore.fetch(params);
    this.attendance = data.attendances[0] || {};
    if (this.attendance.id) {
      this.fetchRegisters();
    }
  }

  async registerAddress() {
    await wechatSdk.register();
    this.fetchAddress();
  }

  fetchAddress() {
    wechatSdk.wx.checkJsApi({
      jsApiList: ['getLocation'],
      success: (res: any) => {
        if (!res.checkResult && res.checkResult.getLocation) {
          this.$message.error('你的微信版本太低，不支持微信定位，请升级到最新的微信版本！');
        } else {
          wechatSdk.wx.getLocation({
            type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
            success: async (result: any) => {
              const params = {
                lon: result.longitude,
                lat: result.latitude,
              };
              const { data } = await this.registerStore.getAddress(params);
              this.location = {
                ...params,
                ...data,
                address: `${data.province} ${data.city} ${data.district}`,
              };
            },
            cancel: (result: any) => {
              this.$message.error('你拒绝授权获取地理位置！');
            },
            fail: (err: any) => {
              this.$message.error(JSON.stringify(err));
            },
          });
        }
      },
      fail: () => {
        this.$message.error('无法调用定位接口');
      },
    });
  }

  fetchRegisters(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.attendance.id,
      shouldAppend: true,
      q: {
        s: ['created_at desc'],
      },
    };
    this.registerStore.fetchByParent(params);
  }

  onShow(val: any) {
    if (val.state === 'undo') {
      this.$message.warning('暂无打卡信息！');
      return;
    }
    this.$router.push(`register/${val.id}`);
  }

  onDialog() {
    if (this.todayRegister.id) {
      this.visible = true;
    } else {
      this.$message.warning('今日不可打卡');
    }
  }

  onSubmit() {
    // const { lon, lat, address } = this.location;
    // if (!lon || !lat || !address) {
    //   this.$message.error('请先获取定位信息');
    //   return;
    // }
    this.update();
  }

  async update() {
    try {
      // const obj: any = {
      //   ...this.location,
      //   id: this.todayRegister.id,
      //   noCompare: true,
      // };
      // await this.registerStore.update(obj);
      this.$router.push(`register/${this.todayRegister.id}`);
    } catch (error) {
      this.$message.error('打卡不成功');
    }
  }

  // 计时器
  nowTimes() {
    window.setInterval(() => {
      this.currentTime = this.$dayjs().format('HH:mm:ss');
    }, 1000);
  }

  showStatistic() {
    if (this.isTeacher) {
      this.$router.push('/ep/activities');
    }
  }
}
</script>

<style lang="stylus" scoped>
.attendance-register
  min-height 100%
  width 100%
  background #fff
  .header
    width 100%
    text-align center
    .round-loop
      margin 0px auto
      padding 7px
      width 166px
      height 166px
      border 3px solid rgba(255, 255, 255, 0.1)
      border-radius 50%
      box-shadow 0px 6px 16px 0px rgba(61, 168, 245, 0.3)
      .middle-loop
        padding 5px
        width 146px
        height 146px
        border 3px solid rgba(255, 255, 255, 0.4)
        border-radius 50%
        box-shadow 0px 6px 16px 0px rgba(61, 168, 245, 0.3)
    .round
      padding 40px 0px 34px
      width 130px
      height 130px
      border-radius 50%
      background #fff
      &:active
        opacity 0.8
      .title
        color #3DA8F5
        font-size 20px
        line-height 30px
      .time
        margin-top 2px
        color #3DA8F5
        font-size 15px
        line-height 24px
    .date
      margin 12px 0px 10px
      color #fff
      font-weight 500
      font-size 17px
      line-height 24px
    .address
      display flex
      justify-content center
      padding 0px 20px
      color #fff
      font-size 14px
      line-height 20px
      &:active
        opacity 0.8
      span
        margin-left 4px
      .van-icon
        font-size 16px
        line-height 20px
  .main
    width 100%
    .title
      padding 26px 0px 24px
      color #383838
      text-align center
      font-weight 500
      font-size 16px
      line-height 24px
    .register
      display flex
      align-items center
      margin-bottom 10px
      padding 10px 16px
      width 100%
      .date-box
        width 60px
        .date
          color #383838
          font-size 15px
          line-height 20px
        .time
          margin-top 2px
          color #a6a6a6
          font-size 13px
          line-height 16px
      .address
        padding 12px
        width 100%
        background #F5F5F5
        color #383838
        font-size 15px
        line-height 24px

.primary-bg
  padding 24px 0px 40px
  background linear-gradient(180deg, rgba(131, 211, 248, 1) 0%, rgba(61, 168, 245, 1) 100%)

.success-bg
  padding 42px 0px 40px
  background linear-gradient(360deg, rgba(134, 195, 109, 1) 0%, rgba(109, 195, 125, 1) 100%)

.van-dialog__header
  margin-top -8px
  padding 0px 0px 16px

.van-dialog__body
  padding 0px 16px
  width 100%
  border-top 1px #e8e8e8 solid
  border-bottom 1px #e8e8e8 solid
  .cell
    display flex
    padding 16px 0px
    width 100%
    border-bottom 1px #e8e8e8 solid
    &:active
      opacity 0.8
    .key
      min-width 60px
      color #808080
      font-size 15px
      line-height 24px
    .value
      margin-left 20px
      color #383838
      font-size 15px
      line-height 24px
</style>
