import { RouteConfig } from '@/interfaces/IRoute';

const Index = () => import(/* webpackChunkName: "ep_activity_index" */ './Index.vue');
const Show = () => import(/* webpackChunkName: "ep_activity_show" */ './Show.vue');
// children
const AttendanceIndex = () => import(/* webpackChunkName: "ep_activity_attendance_index" */ './attendance/Index.vue');
const AttendanceShow = () => import(/* webpackChunkName: "ep_activity_attendance_index" */ './attendance/Show.vue');
const WorkbenchIndex = () => import(/* webpackChunkName: "ep_activity_workbench_index" */ './workbench/Index.vue');
const Registers = () => import(/* webpackChunkName: "ep_activity_register_index" */ './workbench/Registers.vue');
const RegisterStatistic = () =>
  import(/* webpackChunkName: "ep_activity_register_index" */ './workbench/RegisterStatistic.vue');
const Register = () => import(/* webpackChunkName: "ep_activity_register_show" */ './workbench/Register.vue');
const QuestionAttendance = () =>
  import(/* webpackChunkName: "ep_activity_register_index" */ './workbench/QuestionAttendance.vue');

export default [
  {
    path: '/ep/activities',
    name: 'ep_activity_index',
    component: Index,
    meta: {
      title: '疫情统计',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id',
    component: Show,
    children: [
      {
        path: '',
        name: 'ep_activity_workbench_index',
        component: WorkbenchIndex,
        meta: {
          title: '工作台',
          roles: ['Teacher'],
        },
      },
      {
        path: 'attendances',
        name: 'ep_activity_attendance_index',
        component: AttendanceIndex,
        meta: {
          title: '人员列表',
          roles: ['Teacher'],
        },
      },
    ],
  },
  // workbench
  {
    path: '/ep/activities/:id/teacher_registers',
    name: 'ep_activity_teacher_register_index',
    component: Registers,
    meta: {
      title: '教师打卡情况',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id/student_registers',
    name: 'ep_activity_student_register_index',
    component: Registers,
    meta: {
      title: '学生打卡情况',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id/teacher_register_statistic',
    name: 'ep_activity_teacher_register_statistic',
    component: RegisterStatistic,
    meta: {
      title: '教师打卡统计',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id/student_register_statistic',
    name: 'ep_activity_student_register_statistic',
    component: RegisterStatistic,
    meta: {
      title: '学生打卡统计',
      roles: ['Teacher'],
    },
  },
  // register
  {
    path: '/ep/activities/:id/teacher_registers/:registerId',
    name: 'ep_activity_teacher_register_show',
    component: Register,
    meta: {
      title: '打卡情况',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id/student_registers/:registerId',
    name: 'ep_activity_student_register_show',
    component: Register,
    meta: {
      title: '打卡情况',
      roles: ['Teacher'],
    },
  },
  // question attendances
  {
    path: '/ep/activities/:id/teacher_registers/:questionKey/question_attendances',
    name: 'ep_activity_teacher_register_question_attendance_index',
    component: QuestionAttendance,
    meta: {
      title: '详情数据',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id/student_registers/:questionKey/question_attendances',
    name: 'ep_activity_student_register_question_attendance_index',
    component: QuestionAttendance,
    meta: {
      title: '详情数据',
      roles: ['Teacher'],
    },
  },
  // attendance list
  {
    path: '/ep/activities/:id/attendances/:attendanceId',
    name: 'ep_activity_attendance_show',
    component: AttendanceShow,
    meta: {
      title: '打卡情况',
      roles: ['Teacher'],
    },
  },
  {
    path: '/ep/activities/:id/attendances/:attendanceId/registers/:registerId',
    name: 'ep_activity_attendance_register_show',
    component: Register,
    meta: {
      title: '打卡情况',
      roles: ['Teacher'],
    },
  },
] as RouteConfig[];
