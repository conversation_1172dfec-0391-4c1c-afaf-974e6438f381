<template lang="pug">
NavContainer(title="打卡情况" :loading="registerStore.loading")
  ListView(
    :data="registerStore.records"
    :loading="registerStore.loading"
    :pullup="!registerStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(registerStore.currentPage + 1)"
    @refresh="fetchData()")
    .registers
      .header
        RegisterChart(:register="register" :question="question" pageType="Show")
        .tag-cell
          van-tag(
            v-for="(item, index) in tabs"
            :key="index"
            :color="tabIndex === item.key ? '#EDF7FF' : '#f5f5f5'"
            :text-color="tabIndex === item.key ? '#3DA8F5' : '#808080'"
            size="medium"
            @click="onTab(item)") {{ item.label }}({{ item.count }})
      .main
        .register(v-for="(item, index) in registerStore.records" :key="index" @click="onShow(item)")
          .flex
            .avatar(:style="{background: initBackground() }") {{ (item.user_name || '').slice(0, 1) }}
            .info-box
              .name {{ item.user_name }}
              .address {{ item.address }}
          .date(v-if="item.state === 'done'") {{ $dayjs(item.created_at).format('HH:mm') }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import RegisterChart from '@/components/ep/RegisterChart.vue';
import { adminRegisterStore } from '@/store/modules/ep/register.store';

@Component({
  components: {
    RegisterChart,
  },
})
export default class Show extends Vue {
  tabIndex: string = 'done';
  store: any = {
    info: {
      register: {},
      question: {},
    },
    records: [],
  };

  get registerStore() {
    return adminRegisterStore || {};
  }
  get register() {
    return this.store.info.register || {};
  }
  get question() {
    return this.store.info.question || {};
  }
  get tabs() {
    return [
      {
        label: '已打卡',
        key: 'done',
        count: this.register.done,
      },
      {
        label: '未打卡',
        key: 'undo',
        count: this.register.undo,
      },
    ];
  }
  mounted() {
    this.fetchData();
  }

  async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.attendanceId,
      shouldAppend: true,
      q: {
        s: ['state desc'],
        state_eq: this.tabIndex,
      },
    };
    const { data } = await this.registerStore.fetchByParent(params);
    this.store = data;
  }

  onShow(val: any) {
    if (val.state === 'done') {
      this.$router.push(
        `ep/activities/${this.$route.params.id}/attendances/${this.$route.params.attendanceId}/registers/${val.id}`,
      );
    } else {
      this.$message.warning('暂无打卡信息');
    }
  }

  onTab(val: any) {
    if (this.tabIndex !== val.key) {
      this.tabIndex = val.key;
      this.fetchData();
    }
  }

  initBackground() {
    const colors = ['#CDD080', '#E3A26D', '#7FB2E5', '#7FB2E5'];
    const index = Math.floor(Math.random() * 4);
    return colors[index] || '#CDD080';
  }
}
</script>

<style lang="stylus" scoped>
.registers
  width 100%
  background #fff
  .header
    padding 20px 16px 0px
    .tag-cell
      width 100%
      .van-tag
        margin-right 10px
  .main
    padding 10px 16px
    width 100%
    .register
      display flex
      justify-content space-between
      margin-bottom 10px
      padding 12px
      min-height 68px
      width 100%
      background #F5F5F5
      .avatar
        min-width 36px
        width 36px
        height 36px
        border-radius 50%
        background #CDD184
        color #fff
        text-align center
        font-weight 500s
        font-size 16px
        line-height 36px
      .info-box
        padding 0px 12px
        .name
          color #383838
          font-size 15px
          line-height 24px
        .address
          color #A6A6A6
          font-size 13px
          line-height 20px
      .date
        min-width 40px
        color #A6A6A6
        font-size 15px
        line-height 24px
</style>
