<template lang="pug">
NavContainer(
  :title="tabIndex === 'Teacher' ? '教师列表' : '学生列表'"
  :loading="attendanceStore.loading"
  search
  v-model="queryObject"
  :placeholder="tabIndex === 'Teacher' ? '输入姓名、工号' : '输入姓名、学号'"
  :variables="['user_of_Teacher_type_name', 'user_of_Teacher_type_code']")
  template(slot="title")
    Tabs(v-model="tabIndex" :tabs="tabs")
  ListView(
    :data="attendanceStore.attendances"
    :loading="attendanceStore.loading"
    :pullup="!attendanceStore.finish"
    :pulldown="true"
    @loadMore="fetchData(attendanceStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .attendances
      van-panel.attendance(v-for="(item, index) in attendanceStore.attendances" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.user_name }}
        template(slot="default")
          template(v-if="tabIndex === 'Teacher'")
            .cell(style="margin-top: 10px")
              .key 工号
              .value {{ item.user.code }}
            .cell
              .key 部门
              .value {{ item.user.college_name || '-' }}
          template(v-else)
            .cell(style="margin-top: 10px")
              .key 学号
              .value {{ item.user_code }}
            .cell
              .key 专业
              .value {{ item.user.major_name || '-' }}
            .cell
              .key 学院
              .value {{ item.user.college_name || '-' }}
          .cell
            .key 今日打卡
            .value {{ item.register && item.register.today === 'done' ? '已打卡' : '未打卡' }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import attendanceStore from '@/store/modules/ep/attendance.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  queryObject: object = {};
  tabIndex: string = 'Teacher';

  get attendanceStore() {
    return attendanceStore || {};
  }
  get tabs() {
    return [
      {
        text: '教师列表',
        key: 'Teacher',
      },
      {
        text: '学生列表',
        key: 'Student',
      },
    ];
  }

  @Watch('queryObject')
  watchQuery() {
    this.fetchData();
  }

  @Watch('tabIndex')
  watchChange() {
    this.fetchData();
  }

  mounted() {
    attendanceStore.changeNamespace('admin');
    this.fetchData();
  }

  fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      shouldAppend: true,
      q: {
        ...this.queryObject,
        user_type_eq: this.tabIndex,
      },
    };
    attendanceStore.fetchByParent(params);
  }

  onShow(val: any) {
    const path = `/ep/activities/${this.$route.params.id}/attendances/${val.id}`;
    this.$router.push(path);
  }
}
</script>

<style lang="stylus" scoped>
.attendances
  padding 12px
  width 100%
  background #f5f5f5
  &:empty
    padding 0
  .attendance
    margin-bottom 12px
    padding 16px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 80px
</style>
