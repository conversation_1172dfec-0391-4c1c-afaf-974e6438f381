<template lang="pug">
NavContainer(title="工作台" :loading="activityStore.loading")
  .work
    MenuList(:menus="steps")
      template(#scope="{ item }")
        van-button.btn(type="info" plain @click="showStatistic(item)")
          | 打卡统计
        van-button.btn(type="info" plain @click="showUsers(item)")
          | 人员列表
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import MenuList from '@/components/ep/MenuList.vue';
import { inspectActivityStore } from '@/store/modules/ep/activity.store';

@Component({
  components: {
    MenuList,
  },
})
export default class Index extends Vue {
  private info: any = {
    report: {},
    register: {
      today: {},
    },
    company: {},
    thesis: {},
    defence: {},
  };
  get activityId() {
    return +this.$route.params.id;
  }
  get activityStore() {
    return inspectActivityStore || {};
  }
  get register() {
    const activity = this.activityStore.activity || {};
    const infos = activity.infos || {};
    return {
      student: {},
      teacher: {},
      ...infos.register,
    };
  }
  get steps() {
    const { teacher, student } = this.register;
    return [
      {
        label: '教师打卡情况',
        key: 'teacher',
        icon: 'bar-chart-o',
        path: 'ep_activity_teacher_register_index',
        tag: {
          text: `今日：${+teacher.done}/${+teacher.total}`,
          type: 'primary',
        },
      },
      {
        label: '学生打卡情况',
        key: 'student',
        icon: 'chart-trending-o',
        path: 'ep_activity_student_register_index',
        tag: {
          text: `今日：${+student.done}/${+student.total}`,
          type: 'primary',
        },
      },
    ];
  }

  public created() {
    this.fetchData();
  }

  public async fetchData() {
    const { data } = await this.activityStore.find(this.activityId);
    this.info = data.info;
  }

  showStatistic(item: any) {
    this.$router.push(`${this.activityId}/${item.key}_register_statistic`);
  }

  showUsers(item: any) {
    this.$router.push(`${this.activityId}/${item.key}_registers`);
  }
}
</script>

<style lang="stylus" scoped>
.work
  padding 10px
  width 100%
  height 100%
  background #fff
  .btn
    margin-right 16px
</style>
