<template lang="pug">
NavContainer(title="详情数据")
  ListView(
    :data="store.users"
    :loading="store.loading"
    :pullup="!store.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="loadMore"
    @refresh="fetchData()")
    .attendances
      van-tag(
        v-for="(item, index) in tabs"
        :key="index"
        :color="tabIndex === item.key ? '#EDF7FF' : '#f5f5f5'"
        :text-color="tabIndex === item.key ? '#3DA8F5' : '#808080'"
        size="medium"
        @click="onTab(item)")
          span {{ item.label }}
          span(v-if="store.total_count && tabIndex === item.key") （{{ store.total_count }}）
      .attendance(v-for="(item, index) in store.users" :key="index")
        .flex
          .avatar(:style="{background: initBackground() }") {{ (item.name || '').slice(0, 1) }}
          .info-box
            .name {{ item.name }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { inspectActivityStore } from '@/store/modules/ep/activity.store';

@Component({
  components: {},
})
export default class QuestionAttendance extends Vue {
  private tabIndex: string = 'A';
  private tabs: any[] = [];
  private store: any = {
    current_page: 1,
    total_pages: 1,
    total_count: 0,
    users: [],
  };
  private question: any = {
    options: [],
  };
  private activeOption: any = {};
  get userType() {
    return this.$route.name === 'ep_activity_teacher_register_question_attendance_index' ? 'Teacher' : 'Student';
  }

  @Watch('tabIndex')
  public watchChange() {
    this.fetchData();
    this.activeOption = (this.question.options || {}).find((e: any) => e.key === this.tabIndex) || {};
  }

  public created() {
    this.initData();
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const nowDate = this.$route.query.date ? this.$dayjs(this.$route.query.date as string) : this.$dayjs();
    const filterDate = {
      created_at_gteq: nowDate.format('YYYY-MM-DD HH:mm:ss'),
      created_at_lt: nowDate.add(1, 'd').format('YYYY-MM-DD HH:mm:ss'),
    };
    const params = {
      parentId: this.$route.params.id,
      page,
      per_page: 10,
      q: {
        find_users_by_question: [this.$route.params.questionKey, this.tabIndex],
        user_type_eq: this.userType,
        ...filterDate,
      },
    };
    const { data } = await inspectActivityStore.fetchQuestions(params);
    this.store = data;
  }

  public async initData() {
    const { data } = await inspectActivityStore.find(this.$route.params.id);
    const questions = data.meta && data.meta.questions;
    this.question = (questions || []).find((e: any) => e.key === Number(this.$route.params.questionKey)) || {
      options: [],
    };
    this.tabs = (this.question.options || {}).map((item: any) => ({
      label: item.value,
      key: item.key,
    }));
    this.activeOption = (this.question.options || {}).find((e: any) => e.key === this.tabIndex) || {};
  }

  public loadMore() {
    if (this.store.current_page < this.store.total_pages) {
      this.fetchData(Number(this.store.current_page) + 1);
    }
  }

  public onTab(val: any) {
    if (this.tabIndex !== val.key) {
      this.tabIndex = val.key;
    }
  }

  public initBackground() {
    const colors = ['#CDD080', '#E3A26D', '#7FB2E5', '#7FB2E5'];
    const index = Math.floor(Math.random() * 4);
    return colors[index] || '#CDD080';
  }
}
</script>

<style lang="stylus" scoped>
.attendances
  padding 16px
  width 100%
  background #fff
  .van-tag
    margin-right 10px
  .attendance
    display flex
    justify-content space-between
    margin-top 10px
    padding 12px
    min-height 68px
    width 100%
    background #F5F5F5
    .avatar
      min-width 36px
      width 36px
      height 36px
      border-radius 50%
      background #CDD184
      color #fff
      text-align center
      font-weight 500s
      font-size 16px
      line-height 36px
    .info-box
      padding 0px 12px
      .name
        color #383838
        font-size 15px
        line-height 24px
</style>
