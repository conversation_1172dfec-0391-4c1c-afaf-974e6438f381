<template lang="pug">
NavContainer(title="打卡统计" :loading="activityStore.loading")
  .registers
    .header
      .date-cell(@click="visibieDate = true")
        span {{ $dayjs(currentDate).format('YYYY/MM/DD') }}
        van-icon(name="arrow-down")
      RegisterChart(
        :register="register"
        :question="question"
        pageType="Index"
        @detail="onDetail")

  van-popup(
    v-model="visibieDate"
    position="bottom")
    van-datetime-picker(
      v-model="currentDate"
      :min-date="minDate"
      type="date"
      @cancel="onCancel"
      @confirm="changeDate")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import RegisterChart from '@/components/ep/RegisterChart.vue';
import { inspectActivityStore } from '@/store/modules/ep/activity.store';
import qs from 'qs';

@Component({
  components: {
    RegisterChart,
  },
})
export default class RegisterStatistic extends Vue {
  private currentDate: any = new Date();
  private visibieDate: boolean = false;
  private minDate: any = new Date(2020, 0, 1);
  statistic: any = {
    register: {},
    question: {},
  };
  get activityStore() {
    return inspectActivityStore || {};
  }
  get register() {
    return this.statistic.register || {};
  }
  get question() {
    return this.statistic.question || {};
  }
  get userType() {
    return this.$route.name!.includes('teacher') ? 'Teacher' : 'Student';
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const filterDate = this.currentDate
      ? {
          created_at_gteq: this.$dayjs(new Date(new Date(this.currentDate).setHours(0, 0, 0, 0))).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
          created_at_lteq: this.$dayjs(new Date(new Date(this.currentDate).setHours(23, 59, 59, 999))).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
        }
      : {};
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      q: {
        s: ['state desc'],
        user_type_eq: this.userType,
        ...filterDate,
      },
    };
    const { data } = await this.activityStore.getRegisters(params);
    this.statistic = data.info;
  }

  public onDetail(question: any) {
    const { id } = this.$route.params;
    const params = qs.stringify({
      date: this.$dayjs(this.currentDate).format('YYYY-MM-DD'),
    });
    const path =
      this.userType === 'Teacher'
        ? `/ep/activities/${id}/teacher_registers/${question.key}/question_attendances?${params}`
        : `/ep/activities/${id}/student_registers/${question.key}/question_attendances?${params}`;
    this.$router.push(path);
  }

  public changeDate(date: any) {
    this.currentDate = date;
    this.fetchData();
    this.visibieDate = false;
  }

  public onCancel() {
    this.visibieDate = false;
    this.currentDate = new Date();
  }
}
</script>

<style lang="stylus" scoped>
.registers
  width 100%
  background #fff
  .header
    padding 20px 16px 0px
    .date-cell
      width 100%
      color #383838
      font-weight 500s
      font-size 16px
      line-height 20px
      .van-icon
        margin-left 8px
        color #808080
</style>
