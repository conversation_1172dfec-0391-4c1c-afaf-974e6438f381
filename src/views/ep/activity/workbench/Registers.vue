<template lang="pug">
NavContainer(title="打卡人员列表" :loading="activityStore.loading")
  ListView(
    :data="registerStore.records"
    :loading="activityStore.loading"
    :pullup="!registerStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="loadMore"
    @refresh="fetchData()")
    .registers
      .header
        .date-cell(@click="visibieDate = true")
          span {{ $dayjs(currentDate).format('YYYY/MM/DD') }}
          van-icon(name="arrow-down")
        .tag-cell
          van-tag(
            v-for="(item, index) in tabs"
            :key="index"
            :color="tabIndex === item.key ? '#EDF7FF' : '#f5f5f5'"
            :text-color="tabIndex === item.key ? '#3DA8F5' : '#808080'"
            size="medium"
            @click="onTab(item)") {{ item.label }}({{ item.count }})
      .main
        .register(v-for="(item, index) in registerStore.records" :key="index" @click="onShow(item)")
          .flex
            .avatar(:style="{background: initBackground() }") {{ (item.user_name || '').slice(0, 1) }}
            .info-box
              .name {{ item.user_name }}
              .address {{ item.address }}
          .date(v-if="item.state === 'done'") {{ $dayjs(item.created_at).format('HH:mm') }}

  van-popup(
    v-model="visibieDate"
    position="bottom")
    van-datetime-picker(
      v-model="currentDate"
      :min-date="minDate"
      type="date"
      @cancel="onCancel"
      @confirm="changeDate")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { inspectActivityStore } from '@/store/modules/ep/activity.store';
import qs from 'qs';

@Component({
  components: {},
})
export default class Register extends Vue {
  private currentDate: any = new Date();
  private visibieDate: boolean = false;
  private minDate: any = new Date(2020, 0, 1);
  private tabIndex: string = 'done';
  private registerStore: any = {
    info: {
      register: {},
      question: {},
    },
    records: [],
  };
  get activityStore() {
    return inspectActivityStore || {};
  }
  get register() {
    return this.registerStore.info.register || {};
  }
  get question() {
    return this.registerStore.info.question || {};
  }
  get tabs() {
    return [
      {
        label: '已打卡',
        key: 'done',
        count: this.register.done,
      },
      {
        label: '未打卡',
        key: 'undo',
        count: this.register.undo,
      },
    ];
  }
  get userType() {
    return this.$route.name === 'ep_activity_teacher_register_index' ? 'Teacher' : 'Student';
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const filterDate = this.currentDate
      ? {
          created_at_gteq: this.$dayjs(new Date(new Date(this.currentDate).setHours(0, 0, 0, 0))).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
          created_at_lteq: this.$dayjs(new Date(new Date(this.currentDate).setHours(23, 59, 59, 999))).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
        }
      : {};
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      q: {
        s: ['state desc'],
        user_type_eq: this.userType,
        state_eq: this.tabIndex,
        ...filterDate,
      },
    };
    const { data } = await this.activityStore.getRegisters(params);
    data.registers = data.registers.map((item: any) => ({
      ...item,
      user_name: item.user && item.user.name,
    }));
    this.registerStore =
      data.current_page === 1
        ? {
            ...data,
            records: data.registers,
          }
        : {
            ...data,
            records: this.registerStore.records.concat(data.registers),
          };

    this.registerStore.finish = data.current_page >= data.total_pages;
  }

  public loadMore() {
    if (this.registerStore.current_page < this.registerStore.total_pages) {
      this.fetchData(Number(this.registerStore.current_page) + 1);
    }
  }

  public onDetail(question: any) {
    const { id } = this.$route.params;
    const params = qs.stringify({
      date: this.$dayjs(this.currentDate).format('YYYY-MM-DD'),
    });
    const path =
      this.userType === 'Teacher'
        ? `/ep/activities/${id}/teacher_registers/${question.key}/question_attendances?${params}`
        : `/ep/activities/${id}/student_registers/${question.key}/question_attendances?${params}`;
    this.$router.push(path);
  }

  public onShow(val: any) {
    if (val.state === 'done') {
      const { params } = this.$route;
      const path =
        this.userType === 'Teacher'
          ? `/ep/activities/${params.id}/teacher_registers/${val.id}`
          : `/ep/activities/${params.id}/student_registers/${val.id}`;
      this.$router.push(path);
    } else {
      this.$message.warning('暂无打卡信息');
    }
  }

  public changeDate(date: any) {
    this.currentDate = date;
    this.fetchData();
    this.visibieDate = false;
  }

  public onCancel() {
    this.visibieDate = false;
    this.currentDate = new Date();
  }

  public onTab(val: any) {
    if (this.tabIndex !== val.key) {
      this.tabIndex = val.key;
      this.fetchData();
    }
  }

  public initBackground() {
    const colors = ['#CDD080', '#E3A26D', '#7FB2E5', '#7FB2E5'];
    const index = Math.floor(Math.random() * 4);
    return colors[index] || '#CDD080';
  }
}
</script>

<style lang="stylus" scoped>
.registers
  width 100%
  background #fff
  .header
    padding 20px 16px 0px
    .date-cell
      margin-bottom 20px
      width 100%
      color #383838
      font-weight 500s
      font-size 16px
      line-height 20px
      .van-icon
        margin-left 8px
        color #808080
    .tag-cell
      width 100%
      .van-tag
        margin-right 10px
  .main
    padding 10px 16px
    width 100%
    .register
      display flex
      justify-content space-between
      margin-bottom 10px
      padding 12px
      min-height 68px
      width 100%
      background #F5F5F5
      .avatar
        min-width 36px
        width 36px
        height 36px
        border-radius 50%
        background #CDD184
        color #fff
        text-align center
        font-weight 500s
        font-size 16px
        line-height 36px
      .info-box
        padding 0px 12px
        .name
          color #383838
          font-size 15px
          line-height 24px
        .address
          color #A6A6A6
          font-size 13px
          line-height 20px
      .date
        min-width 40px
        color #A6A6A6
        font-size 15px
        line-height 24px
</style>
