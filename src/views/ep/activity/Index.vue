<template lang="pug">
NavContainer(title="健康活动" :loading="activityStore.loading")
  ListView(
    :data="activityStore.activities"
    :loading="activityStore.loading"
    :pullup="!activityStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(activityStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .activities
      van-panel.activity(v-for="(item, index) in activityStore.activities" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.name }}
        template(slot="default")
          .cell(style="margin-top: 20px")
            .key 时间
            .value {{ $dayjs(item.start_at).format('YYYY/MM/DD') }} - {{ $dayjs(item.end_at).format('YYYY/MM/DD') }}
          .cell
            .key 人数
            .value 教师：{{ item.teacher_count }} 人、学生：{{ item.student_count }} 人
          .cell
            .key 已打卡
            .value {{ getRegisterCount(item, 'done') }}
          .cell
            .key 未打卡
            .value {{ getRegisterCount(item, 'undo') }}
          .cell
            .key 状态
            van-tag(color="#F0F9F2" text-color="#6DC37D") {{ item.zh_state }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { inspectActivityStore } from '@/store/modules/ep/activity.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  get activityStore() {
    return inspectActivityStore;
  }

  created() {
    this.fetchData();
  }

  fetchData(page: number = 1) {
    this.activityStore.fetch({
      page,
      per_page: 10,
      shouldAppend: true,
    });
  }

  onShow(val: any) {
    this.$router.push(`activities/${val.id}`);
  }

  getRegisterCount(activity: any, key: string) {
    const { teacher, student } = activity.info || { teacher: {}, student: {} };
    const teacherCount = (teacher.register || {})[key] || 0;
    const studentCount = (student.register || {})[key] || 0;
    return teacherCount + studentCount;
  }
}
</script>

<style lang="stylus" scoped>
.activities
  padding 12px
  width 100%
  background #f5f5f5
  .activity
    margin-bottom 12px
    padding 16px 16px 12px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 60px
</style>
