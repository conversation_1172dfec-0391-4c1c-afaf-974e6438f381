<template lang="pug">
.page
  router-view
  van-tabbar.tabbar(:value="tabIndex" fixed safe-area-inset-bottom)
    van-tabbar-item(v-for="(tab, index) in tabs" :key="index" :icon="tab.icon" @click="onTab(tab, index)")
      | {{ tab.label }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Show extends Vue {
  tabIndex: number = 0;

  get tabs() {
    return [
      {
        label: '工作台',
        path: 'ep_activity_workbench_index',
        icon: 'wap-home',
      },
      {
        label: '人员列表',
        path: 'ep_activity_attendance_index',
        icon: 'friends',
      },
    ];
  }

  created() {
    this.tabIndex = this.$route.name === 'ep_activity_workbench_index' ? 0 : 1;
  }

  onTab(val: any, index: number) {
    if (this.tabIndex !== index) {
      this.tabIndex = index;
      this.$router.replace({ name: val.path });
    }
  }
}
</script>

<style lang="stylus" scoped>
.page
  padding-bottom 50px
  width 100%
  height 100%
  background #fff
  .tabbar
    position fixed
    bottom 0px
    left 0px
    width 100%
    border-top 1px #fff solid
    background #f4f5f5
</style>
