<template lang="pug">
NavContainer(:loading="thesisStore.loading")
  Tabs(v-model="tabIndex" :tabs="tabs" slot="title")
  template(v-if="tabIndex === 'thesis'")
    .pannel
      .pannel-top
        .score-card(v-if="role !== 'student' && thesis.state !== 'pending'")
          .flex-between
            .flex
              van-icon(name="sign")
              label 评分
            .edit-cell(
              @click="visibleScore = true"
              v-if="!visibleScore && +thesis.score > 0")
              van-icon(name="edit")
              span 修改分数
          .score-cell
            template(v-if="visibleScore")
              van-field(
                v-model="thesis.score"
                type="number"
                placeholder="请输入分数")
              van-button(type="info" :disabled="+thesis.score === 0" @click="onScore") 确认
            template(v-else)
              .score {{ +thesis.score }} 分
        .title {{ thesis.title }}
        .info-cell(v-for="(info, index) in infos" :key="index")
          .key {{ info.label }}
          .value
            span(:class="+info.value > 0 ? 'text-primary' : 'text-gray'" v-if="info.key === 'score'")
              | {{ +info.value > 0 ? +info.value + '分' : '等待评分' }}
            span(v-else) {{ info.value }}
      .pannel-middle
        strong 目录
        dl
          dt(v-for="(dt, index) in directories" :key="index") {{ index + 1 }} {{ dt.title }}
            dd(v-for="(dd, key) in dt.children" :key="key" style="margin-left: 12px")
              .flex.ck-content
                span {{ index + 1 }}.{{ key + 1 }}
                span(v-html="dd.title" style="margin-left: 4px")
              dt(v-for="(item, ind) in dd.children" :key="ind" style="margin-left: 20px")
                .flex.ck-content
                  span {{ index + 1 }}.{{ key + 1 }}.{{ ind + 1}}
                  span(v-html="item.title" style="margin-left: 4px")
      .pannel-botttom
        .module(v-for="(item, index) in theses" :key="index")
          .module-top
            strong {{ item.title }}
            template(v-if="item.body")
              .rate(:class="+item.similarity > 30 ? 'text-danger' : 'text-success'") 查重率 {{ +item.similarity }}%
          .module-middle
            RichText(:value="item.body")
            p(v-if="!item.body") 待完善
  template(v-else)
    ListView(
      :data="ptCommentStore.records"
      :loading="ptCommentStore.loading"
      :pullup="!ptCommentStore.finish"
      :pulldown="true"
      emptyText="暂无内容"
      @loadMore="fetchComments(ptCommentStore.currentPage + 1)"
      @refresh="fetchComments(1)")
      .comments(:style="{paddingBottom: visibleForm ? '160px' : '60px'}")
        Empty(v-if="ptCommentStore.records.length === 0")
        .comment-cell(v-for="(item, index) in ptCommentStore.records" :key="index")
          .comment-top
            .flex
              .avatar {{ (item.user_name || '').slice(0, 1) }}
              .name {{ item.user_name }}
            .date
              span {{ $dayjs(item.created_at).format('MM月DD日 HH:mm') }}
          .comment-content
            .text-pre {{ item.body }}
            .actions(
              @click="onDelete(item)")
              van-icon(name="delete")
  template(v-if="tabIndex === 'comment'")
    .comment(@click="onCreate")
      van-icon(name="comment-o")
    .submit-box(v-if="visibleForm")
      van-field(
        v-model="comment.body"
        autosize
        label="评论"
        type="textarea"
        placeholder="添加评论")
      .submit
        van-button(
          @click="visibleForm = false") 取消
        van-button(
          type="info"
          :loading="ptCommentStore.loading"
          :disabled="!comment.body || ptCommentStore.loading"
          @click="submit") 发布
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';
import thesisStore from '@/store/modules/pt/thesis.store';
import commStore from '@/store/modules/comm/thesis.store';
import commentStore from '@/store/modules/comm/comment.store';
import ptCommentStore from '@/store/modules/pt/comment.store';

@Component({
  components: {},
})
export default class Thesis extends Vue {
  private tabIndex: string = 'thesis';
  private visibleScore: boolean = false;
  private visibleForm: boolean = false;
  private comment: any = {
    body: '',
  };
  get role() {
    return this.$tools.getRole();
  }
  get tabs() {
    return [
      {
        text: '实习报告',
        key: 'thesis',
      },
      {
        text: '评论',
        key: 'comment',
      },
    ];
  }
  get thesisStore() {
    return thesisStore || {};
  }
  get ptCommentStore() {
    return ptCommentStore || {};
  }
  get practice() {
    return practiceStore.record || {};
  }
  get infos() {
    const student = {
      ...this.practice.student,
      published_at:
        this.practice.thesis && this.practice.thesis.published_at
          ? this.$dayjs(this.practice.thesis.published_at).format('YYYY/MM/DD')
          : '-',
      score: this.thesis.score,
    };
    return [
      {
        label: '姓名',
        key: 'name',
      },
      {
        label: '学号',
        key: 'code',
      },
      {
        label: '专业',
        key: 'major_name',
      },
      {
        label: '班级',
        key: 'adminclass_name',
      },
      {
        label: '提交日期',
        key: 'published_at',
      },
      {
        label: '评分',
        key: 'score',
      },
    ].map((item: any) => ({
      ...item,
      value: student[item.key] || '-',
    }));
  }
  get thesis() {
    return this.practice.thesis || {};
  }
  get theses() {
    const template = this.practice.template || {};
    const template_infos = template.infos || [];
    const infos = this.thesis.infos || [];
    return template_infos.map((item: any) => ({
      ...item,
      ...infos.find((e: any) => e.title === item.title),
      body: (infos.find((e: any) => e.title === item.title) || { body: '' }).body,
    }));
  }
  get directories() {
    return this.theses.map((item: any) => ({
      title: item.title,
      children: this.$tools.formatDirectory(item.body),
    }));
  }

  @Watch('tabIndex')
  public watchChange() {
    if (this.tabIndex === 'comment') {
      this.fetchComments();
    }
  }

  public mounted() {
    practiceStore.changeNamespace(this.role);
    thesisStore.changeNamespace(this.role);
    ptCommentStore.changeNamespace(this.role);
    ptCommentStore.changeParentResource('theses');
    this.fetchData();
  }

  public async fetchData() {
    await practiceStore.find(this.$route.params.practiceId);
    this.visibleScore = this.thesis.state === 'published';
  }

  public onScore() {
    if (+this.thesis.score > 100 || +this.thesis.score < 0) {
      this.$message.warning('评分不能超过100分!');
      return;
    }
    const obj: any = {
      id: this.thesis.id,
      score: this.thesis.score,
      noCompare: true,
    };
    this.update(obj);
  }

  public async update(val: any) {
    try {
      await thesisStore.update(val);
      this.visibleScore = false;
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  // 评论
  public fetchComments(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.thesis.id,
      shouldApped: true,
    };
    ptCommentStore.fetchByParent(params);
  }

  public onCreate() {
    this.comment = {
      body: '',
    };
    this.visibleForm = true;
  }

  public async submit() {
    try {
      const val = {
        ...this.comment,
        commentable_id: this.thesis.id,
        commentable_type: 'Thesis',
      };
      await commentStore.create(val);
      this.fetchComments();
      this.visibleForm = false;
      this.$message.success('创建成功！');
    } catch (error) {
      this.$message.error('创建失败！');
    }
  }

  public onDelete(val: any) {
    this.$dialog
      .confirm({
        title: '提醒',
        message: '确定要继续操作吗？',
      })
      .then(async () => {
        this.delete(val);
      })
      .catch(() => {});
  }

  public async delete(val: any) {
    try {
      const params = {
        id: val.id,
        role: this.role,
        parentResource: 'theses',
        parentId: this.thesis.id,
      };
      await ptCommentStore.deleteByParent(params);
      this.fetchComments();
      this.$message.success('删除成功！');
    } catch (error) {
      this.$message.error('删除失败！');
    }
  }
}
</script>

<style lang="stylus" scoped>
.pannel
  padding 0px 20px
  width 100%
  background #fff
  .pannel-top
    padding 14px 0px
    width 100%
    border-bottom 1px #e8e8e8 solid
    .title
      padding 10px 0px
      color #383838
      font-size 19px
      line-height 28px
    .info-cell
      display flex
      justify-content space-between
      padding 6px 0px
      font-size 15px
      line-height 24px
      .key
        color #808080
      .value
        color #383838
    .score-card
      padding 14px 0px 20px
      border-bottom 1px #e8e8e8 solid
      label
        margin-left 12px
        color #808080
        font-size 15px
        line-height 24px
      .edit-cell
        text-align right
        span
          margin-left 4px
          color #808080
          font-size 14px
          line-height 24px
        .van-icon
          color #A6A6A6
      .score-cell
        display flex
        align-items center
        margin-top 16px
        width 100%
        .van-field
          margin-right 8px
          width 100%
          height 42px
          border 1px solid #CCCCCC
          border-radius 3px
        .van-button
          padding 0px
          min-width 70px
        .score
          margin-left 30px
          color #3DA8F5
          font-weight 600
          font-size 16px
          line-height 24px
  .pannel-middle
    padding 20px 0px
    width 100%
    border-bottom 1px #e8e8e8 solid
    strong
      color #383838
      font-weight 500
      font-size 17px
      line-height 24px
  .module
    margin 0px auto
    padding 24px 0px 22px
    max-width 800px
    width 100%
    border-top 1px #e8e8e8 solid
    &:first-child
      border none
    .module-top
      display flex
      justify-content space-between
      align-items center
      width 100%
      strong
        color #383838
        font-weight 500
        font-size 17px
        line-height 24px
      .rate
        font-size 14px
        line-height 20px
    .module-middle
      margin-top 14px
      width 100%
      color #383838
      font-size 15px
      line-height 26px

.comments
  padding 4px 20px
  width 100%
  background #fff
  .comment-cell
    padding 16px 0px
    width 100%
    border-bottom 1px #e8e8e8 solid
    .comment-top
      display flex
      justify-content space-between
      align-items center
      width 100%
      .avatar
        width 28px
        height 28px
        border-radius 50%
        background #CDD184
        color #fff
        text-align center
        font-weight 500s
        font-size 12px
        line-height 28px
      .name
        margin-left 8px
        color #383838
        font-weight 500s
        font-size 14px
        line-height 20px
    .comment-content
      display flex
      flex-wrap wrap
      align-items center
      margin-top 4px
      padding-left 36px
      width 100%
      .actions
        margin-left 14px
    span
      color #808080
      font-size 14px
      line-height 20px

.submit-box
  position fixed
  bottom 0px
  left 0px
  overflow auto
  width 100%
  height 160px
  border-top 1px #e8e8e8 solid
  background #fff
  box-shadow 0px 2px 12px 0px #eee
  .submit
    position fixed
    bottom 0px
    left 0px
    display flex
    justify-content flex-end
    padding 10px 20px
    width 100%
    border-top 1px #e8e8e8 solid
    background #fff
    .van-button
      margin-left 10px
      width 100px

.comment
  position fixed
  right 20px
  bottom 10px
  display flex
  justify-content center
  align-items center
  width 44px
  height 44px
  border-radius 50%
  background #fff
  box-shadow 0px 2px 4px 0px rgba(0, 0, 0, 0.1)

.van-icon
  color #A6A6A6
  font-size 16px

dl, dt, dd
  margin-top 8px
</style>
