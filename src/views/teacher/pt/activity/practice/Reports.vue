<template lang="pug">
NavContainer(title="实习周记" :loading="reportStore.loading")
  ListView(
    ref="listView"
    :data="reportStore.records"
    :loading="reportStore.loading"
    :pullup="!reportStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(reportStore.currentPage + 1)"
    @refresh="fetchData()")
    .reports
      van-panel.report(v-for="(item, index) in reportStore.records" :key="index" @click="onShow(item)")
        template(slot="header")
          strong 第{{ item.windex }}周
        template(slot="default")
          .cell(style="margin-top: 20px")
            .key 时间
            .value {{ $dayjs(item.start_at).format('YYYY/MM/DD') }}-{{ $dayjs(item.end_at).format('YYYY/MM/DD') }}
          .cell(v-if="item.state !== 'pending'")
            .key 提交时间
            .value {{ $dayjs(item.published_at).format('YYYY/MM/DD HH:mm') }}
          .cell
            .key 评论
            .value {{ item.comment_count || '-' }}
        template(slot="footer")
          .state
            van-tag(color="#F0F9F2" text-color="#6DC37D" v-if="item.state === 'scored'") 已评分
            van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else-if="item.state === 'published'") 已上传
            van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else) 待上传
            //- van-tag(color="#f5f5f5" text-color="#808080") 待开始
          template(v-if="item.state !== 'pending'")
            van-tag(color="#F0F9F2" text-color="#6DC37D" v-if="+item.score > 0") 评分：{{ +item.score }}分
            van-tag(color="#f5f5f5" text-color="#808080" v-else) 等待打分
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import reportStore from '@/store/modules/pt/report.store';

@Component({
  components: {},
})
export default class Reports extends Vue {
  get role() {
    return this.$tools.getRole();
  }
  get reportStore() {
    return reportStore || {};
  }
  get practice() {
    return reportStore.record || {};
  }

  public created() {
    reportStore.changeNamespace(this.role);
    this.fetchData();
  }

  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.practiceId,
      shouldAppend: true,
      q: {
        s: ['windex desc'],
      },
    };
    reportStore.fetchByParent(params);
  }

  public onShow(val: any) {
    if (val.state === 'pending') {
      this.$message.warning('暂无周记详情！');
      return;
    }
    this.$router.push(
      `/pt/teacher/activities/${this.$route.params.id}/practices/${this.$route.params.practiceId}/reports/${val.id}`,
    );
  }
}
</script>

<style lang="stylus" scoped>
.reports
  overflow hidden
  padding 12px
  width 100%
  height 100%
  background #f5f5f5
  .report
    margin-bottom 12px
    padding 16px 16px 12px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 68px
    .state
      display inline
      margin-right 10px

.van-panel__footer
  padding 12px 0px 0px
</style>
