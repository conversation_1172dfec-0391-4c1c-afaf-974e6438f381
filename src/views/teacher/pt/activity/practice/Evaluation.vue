<template lang="pug">
NavContainer.container(:title="pageTitle" :loading="practiceStore.loading")
  .steps
    van-steps(direction="vertical" :active="stepIndex")
      van-step(v-for="(item, index) in steps" :key="index" active-color="#3DA8F5")
        template(slot="active-icon")
          .point.active {{ index + 1 }}
        template(slot="inactive-icon")
          .point.inactive(v-if="stepIndex === 0") {{ index + 1 }}
          .point.success(v-else)
            van-icon(name="success" color="#3DA8F5")
        .module
          .flex-between
            .title {{ item.label }}
            .edit-cell(
              @click="visibleScore = true"
              v-if="item.key === 'score' && !visibleScore && +company_info.score > 0")
              van-icon(name="edit")
              span 修改分数
          template(v-if="item.key === 'uploader'")
            Attachments(
              v-if="files.length > 0"
              :attachments="files"
              :showActions="false")
            .hint(v-else) 请前往电脑端上传附件
          .score-cell(v-else)
            template(v-if="visibleScore")
              van-field(
                v-model="company_info.score"
                type="number"
                placeholder="请输入分数")
              van-button(type="info" :disabled="+company_info.score === 0" @click="onScore") 确认
            template(v-else)
              .score {{ +company_info.score }} 分
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';

@Component({
  components: {},
})
export default class Evaluation extends Vue {
  private visibleScore: boolean = false;
  get role() {
    return this.$tools.getRole();
  }
  get practiceStore() {
    return practiceStore || {};
  }
  get practice() {
    return practiceStore.record || {};
  }
  get steps() {
    return [
      {
        label: '上传附件',
        key: 'uploader',
      },
      {
        label: +this.company_info.score > 0 ? '老师评分' : '等待老师评分',
        key: 'score',
      },
    ];
  }
  get company_info() {
    return this.practice.company_info || {};
  }
  get files() {
    return this.company_info.files || [];
  }
  get stepIndex() {
    const fileLength = this.files.length > 0 ? 1 : 0;
    return +this.company_info.score > 0 ? 2 : fileLength;
  }
  get pageTitle() {
    const student = this.practice.student || {};
    return this.$route.name === 'pt_teacher_activity_evaluation_show' ? `${student.name}的企业评价` : '企业评价';
  }

  public async created() {
    practiceStore.changeNamespace(this.role);
    await practiceStore.find(this.$route.params.practiceId);
    this.visibleScore = +this.company_info.score === 0;
  }

  public onScore() {
    if (+this.company_info.score > 100 || +this.company_info.score < 0) {
      this.$message.warning('评分不能超过100分!');
      return;
    }
    const obj: any = {
      id: this.$route.params.practiceId,
      practice_infos_attributes: {
        id: this.company_info.id,
        score: this.company_info.score,
      },
      noCompare: true,
    };
    this.update(obj);
  }

  public async update(val: any) {
    try {
      await practiceStore.update(val);
      this.visibleScore = false;
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }
}
</script>

<style lang="stylus" scoped>
.container
  width 100%
  height 100%
  background #fff
  .steps
    padding 14px 10px
    width 100%
    .point
      display flex
      justify-content center
      align-items center
      margin-top 8px
      width 28px
      height 28px
      border-radius 50%
      font-weight 600
      font-size 16px
    .active
      background #3DA8F5
      color #fff
    .inactive
      border 1px #CCCCCC solid
      background #fff
      color #CCCCCC
    .success
      border 1px #3DA8F5 solid
      background #fff
    .module
      padding 0px 0px 40px 4px
      width 100%
      .title
        color #383838
        font-size 15px
        line-height 24px
      .hint
        color #808080
        font-size 15px
        line-height 24px
      .edit-cell
        text-align right
        span
          margin-left 4px
          color #808080
          font-size 14px
          line-height 24px
        .van-icon
          color #A6A6A6
      .score-cell
        display flex
        align-items center
        margin-top 10px
        width 100%
        .van-field
          margin-right 8px
          width 120px
          height 42px
          border 1px solid #CCCCCC
          border-radius 3px
        .score
          margin-left 30px
          color #3DA8F5
          font-weight 600
          font-size 16px
          line-height 24px
</style>
