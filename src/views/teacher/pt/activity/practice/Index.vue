<template lang="pug">
NavContainer(
  title="学生列表"
  :loading="practiceStore.loading"
  search
  v-model="queryObject"
  placeholder="输入姓名、学号、专业、班级"
  :variables="['student_name', 'student_code', 'student_major_name', 'student_adminclass_name']")
  ListView(
    :data="practices"
    :loading="practiceStore.loading"
    :pullup="!practiceStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(practiceStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .practices
      van-panel.practice(v-for="(item, index) in practices" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.student_name }}
        template(slot="default")
          .cell(style="margin-top: 10px")
            .key 学号
            .value {{ item.student_code }}
          .cell
            .key 专业
            .value {{ item.student_major_name || '-' }}
          .cell
            .key 班级
            .value {{ item.student_adminclass_name || '-' }}
          .cell
            .key 时间
            .value {{ item.start_at }} - {{ item.end_at }}
          .cell
            .key 最近签到
            .value {{ item.last_register_at ? $dayjs(item.last_register_at).format('MM月DD日 HH:mm') : '-' }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  private queryObject: object = {};
  get role() {
    return this.$tools.getRole();
  }
  get practiceStore() {
    return practiceStore || {};
  }
  get practices() {
    return (practiceStore.records || []).map((item: any) => ({
      ...item,
      student_name: item.student.name,
      student_code: item.student.code,
      student_major_name: item.student.major_name,
      student_adminclass_name: item.student.adminclass_name,
      start_at: item.start_at ? this.$dayjs(item.start_at).format('MM月DD日') : '',
      end_at: item.end_at ? this.$dayjs(item.end_at).format('MM月DD日') : '',
    }));
  }

  @Watch('queryObject')
  public watchQuery() {
    this.fetchData();
  }

  public mounted() {
    practiceStore.changeNamespace(this.role);
    this.fetchData();
  }

  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      shouldAppend: true,
      q: {
        ...this.queryObject,
      },
    };
    practiceStore.fetchByParent(params);
  }

  public onShow(val: any) {
    const path = `/pt/teacher/activities/${this.$route.params.id}/practices/${val.id}`;
    this.$router.push(path);
  }
}
</script>

<style lang="stylus" scoped>
.practices
  padding 12px
  width 100%
  background #f5f5f5
  .practice
    margin-bottom 12px
    padding 16px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 68px
</style>
