import { RouteConfig } from '@/interfaces/IRoute';

const Index = () => import(/* webpackChunkName: "pt_teacher_activity_index" */ './Index.vue');
const Show = () => import(/* webpackChunkName: "pt_teacher_activity_show" */ './Show.vue');
// practice
const Practices = () => import(/* webpackChunkName: "pt_teacher_activity_practice_index" */ './practice/Index.vue');
const Practice = () => import(/* webpackChunkName: "pt_teacher_activity_practice_show" */ './practice/Show.vue');
const Reports = () =>
  import(/* webpackChunkName: "pt_teacher_activity_practice_report_index" */ './practice/Reports.vue');
const Report = () => import(/* webpackChunkName: "pt_teacher_activity_practice_report_show" */ './practice/Report.vue');
const Evaluation = () =>
  import(/* webpackChunkName: "pt_teacher_activity_practice_evaluation_show" */ './practice/Evaluation.vue');
const Thesis = () => import(/* webpackChunkName: "pt_teacher_activity_practice_thesis_show" */ './practice/Thesis.vue');
const Reply = () => import(/* webpackChunkName: "pt_teacher_activity_practice_reply_index" */ './practice/Reply.vue');
const Register = () =>
  import(/* webpackChunkName: "pt_teacher_activity_practice_register_index" */ './practice/Register.vue');
// workbench
const teaWorkbench = () =>
  import(/* webpackChunkName: "pt_teacher_activity_workbench_index" */ './workbench/Index.vue');
const teaRegister = () =>
  import(/* webpackChunkName: "pt_teacher_activity_register_index" */ './workbench/Register.vue');
const teaReports = () => import(/* webpackChunkName: "pt_teacher_activity_report_index" */ './workbench/Reports.vue');
const teaReport = () => import(/* webpackChunkName: "pt_teacher_activity_report_show" */ './practice/Report.vue');
const teaEvaluations = () =>
  import(/* webpackChunkName: "pt_teacher_activity_evaluation_index" */ './workbench/Evaluations.vue');
const teaEvaluation = () =>
  import(/* webpackChunkName: "pt_teacher_activity_evaluation_show" */ './practice/Evaluation.vue');
const teaTheses = () => import(/* webpackChunkName: "pt_teacher_activity_thesis_index" */ './workbench/Theses.vue');
const teaThesis = () => import(/* webpackChunkName: "pt_teacher_activity_thesis_show" */ './practice/Thesis.vue');
const teaReplies = () => import(/* webpackChunkName: "pt_teacher_activity_reply_index" */ './workbench/Replies.vue');
const teaReply = () => import(/* webpackChunkName: "pt_teacher_activity_reply_show" */ './practice/Reply.vue');

export default [
  {
    path: '/pt/teacher/activities',
    name: 'pt_teacher_activity_index',
    component: Index,
    meta: {
      title: '实习活动',
      roles: ['Teacher'],
    },
  },
  {
    path: '/pt/teacher/activities/:id',
    component: Show,
    children: [
      {
        path: '',
        name: 'pt_teacher_activity_workbench_index',
        component: teaWorkbench,
        meta: {
          title: '工作台',
          roles: ['Teacher'],
        },
      },
      {
        path: 'practices',
        name: 'pt_teacher_activity_practice_index',
        component: Practices,
        meta: {
          title: '学生列表',
          roles: ['Teacher'],
        },
      },
    ],
  },
  {
    path: '/pt/teacher/activities/:id/registers',
    name: 'pt_teacher_activity_register_index',
    component: teaRegister,
    meta: {
      title: '打卡情况',
    },
  },
  {
    path: '/pt/teacher/activities/:id/reports',
    name: 'pt_teacher_activity_report_index',
    component: teaReports,
    meta: {
      title: '实习周记',
    },
  },
  {
    path: '/pt/teacher/activities/:id/reports/:reportId',
    name: 'pt_teacher_activity_report_show',
    component: teaReport,
    meta: {
      title: '周记详情',
    },
  },
  {
    path: '/pt/teacher/activities/:id/evaluations',
    name: 'pt_teacher_activity_evaluation_index',
    component: teaEvaluations,
    meta: {
      title: '企业评价',
    },
  },
  {
    path: '/pt/teacher/activities/:id/evaluations/:practiceId',
    name: 'pt_teacher_activity_evaluation_show',
    component: teaEvaluation,
    meta: {
      title: '企业评价详情',
    },
  },
  {
    path: '/pt/teacher/activities/:id/theses',
    name: 'pt_teacher_activity_thesis_index',
    component: teaTheses,
    meta: {
      title: '实习报告',
    },
  },
  {
    path: '/pt/teacher/activities/:id/theses/:practiceId',
    name: 'pt_teacher_activity_thesis_show',
    component: teaThesis,
    meta: {
      title: '实习报告详情',
    },
  },
  {
    path: '/pt/teacher/activities/:id/replies',
    name: 'pt_teacher_activity_reply_index',
    component: teaReplies,
    meta: {
      title: '答辩管理',
    },
  },
  {
    path: '/pt/teacher/activities/:id/replies/:replyId',
    name: 'pt_teacher_activity_reply_show',
    component: teaReply,
    meta: {
      title: '答辩详情',
    },
  },
  // practice show
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId',
    name: 'pt_teacher_activity_practice_show',
    component: Practice,
    meta: {
      title: '学生详情',
    },
  },
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId/reports',
    name: 'pt_teacher_activity_practice_report_index',
    component: Reports,
    meta: {
      title: '实习周记',
    },
  },
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId/reports/:reportId',
    name: 'pt_teacher_activity_practice_report_show',
    component: Report,
    meta: {
      title: '周记详情',
    },
  },
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId/evaluation',
    name: 'pt_teacher_activity_practice_evaluation_show',
    component: Evaluation,
    meta: {
      title: '企业评价',
    },
  },
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId/thesis',
    name: 'pt_teacher_activity_practice_thesis_show',
    component: Thesis,
    meta: {
      title: '实习报告',
    },
  },
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId/reply',
    name: 'pt_teacher_activity_practice_reply_index',
    component: Reply,
    meta: {
      title: '实习答辩',
    },
  },
  {
    path: '/pt/teacher/activities/:id/practices/:practiceId/registers',
    name: 'pt_teacher_activity_practice_register_index',
    component: Register,
    meta: {
      title: '打卡情况',
    },
  },
] as RouteConfig[];
