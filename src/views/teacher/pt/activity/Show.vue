<template lang="pug">
.page
  router-view
  van-tabbar.tabbar(:value="tabIndex" fixed safe-area-inset-bottom)
    van-tabbar-item(v-for="(tab, index) in tabs" :key="index" :icon="tab.icon" @click="onTab(tab, index)")
      | {{ tab.label }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Show extends Vue {
  get role() {
    return this.$tools.getRole();
  }
  private tabIndex: number = 0;
  get tabs() {
    return [
      {
        label: '工作台',
        path: 'pt_teacher_activity_workbench_index',
        icon: 'wap-home',
      },
      {
        label: '学生列表',
        path: 'pt_teacher_activity_practice_index',
        icon: 'friends',
      },
    ];
  }

  public created() {
    this.tabIndex = this.$route.name === 'pt_teacher_activity_workbench_index' ? 0 : 1;
  }

  public onTab(val: any, index: number) {
    if (this.tabIndex !== index) {
      this.tabIndex = index;
      this.$router.replace({ name: val.path });
    }
  }
}
</script>

<style lang="stylus" scoped>
.page
  padding-bottom 50px
  width 100%
  height 100%
  background #fff
  .tabbar
    position fixed
    bottom 0px
    left 0px
    width 100%
    border-top 1px #fff solid
    background #f4f5f5
</style>
