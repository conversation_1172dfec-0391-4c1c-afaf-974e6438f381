<template lang="pug">
NavContainer(
  title="实习报告"
  :loading="activityStore.loading"
  search
  v-model="queryObject"
  placeholder="输入姓名、学号、专业、班级"
  :variables="variables")
  Tabs.tabs(v-model="tabIndex" :tabs="tabs")
  ListView(
    :data="thesisStore.records"
    :loading="activityStore.loading"
    :pullup="!activityStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(thesisStore.currentPage + 1)"
    @refresh="fetchData()")
    .theses
      van-panel.report(v-for="(item, index) in thesisStore.records" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.student_name}}
        template(slot="default")
          .cell(style="margin-top: 20px")
            .key 学号
            .value {{ item.student_code }}
          .cell
            .key 专业
            .value {{ item.student_major_name || '-' }}
          .cell
            .key 班级
            .value {{ item.student_adminclass_name || '-' }}
          .cell
            .key 提交时间
            .value {{ item.published_at ? $dayjs(item.published_at).format('YYYY/MM/DD HH:mm') : '-' }}
          .cell
            .key 评论
            .value {{ item.comment_count || '-' }}
        template(slot="footer")
          van-tag(color="#F5F5F5" text-color="#A6A6A6" v-if="item.state === 'published'") 等待打分
          van-tag(color="#F0F9F2" text-color="#6DC37D" v-else-if="item.state === 'scored'") 已评分： {{ +item.score }}分
          van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else) 未提交
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import activityStore from '@/store/modules/pt/activity.store';

@Component({
  components: {},
})
export default class Theses extends Vue {
  private queryObject: object = {};
  private tabIndex: string = 'published';
  private thesisStore: any = {
    info: {},
    records: [],
  };
  get variables() {
    return [
      'user_of_Student_type_name',
      'user_of_Student_type_code',
      'user_of_Student_type_major_name',
      'user_of_Student_type_adminclass_name',
    ];
  }
  get role() {
    return this.$tools.getRole();
  }
  get activityStore() {
    return activityStore || {};
  }
  get tabs() {
    return [
      {
        text: '待评分',
        key: 'published',
      },
      {
        text: '已评分',
        key: 'scored',
      },
      {
        text: '未提交',
        key: 'pending',
      },
    ];
  }
  @Watch('queryObject')
  public watchQuery() {
    this.fetchData();
  }
  @Watch('tabIndex')
  public watchChange() {
    this.fetchData();
  }

  public mounted() {
    activityStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      q: {
        ...this.queryObject,
        state_eq: this.tabIndex,
      },
    };
    const { data } = await activityStore.getTheses(params);
    data.theses = data.theses.map((item: any) => ({
      ...item,
      student_name: item.student && item.student.name,
      student_code: item.student && item.student.code,
      student_major_name: item.student && item.student.major_name,
      student_adminclass_name: item.student && item.student.adminclass_name,
    }));
    this.thesisStore =
      data.current_page === 1
        ? {
            ...data,
            records: data.theses,
          }
        : {
            ...data,
            records: this.thesisStore.records.concat(data.theses),
          };
  }

  public onShow(val: any) {
    if (val.state === 'pending') {
      this.$message.warning('暂无实习报告详情！');
      return;
    }
    this.$router.push(`/pt/teacher/activities/${this.$route.params.id}/theses/${val.practice_id}`);
  }

  public loadMore() {
    if (this.thesisStore.current_page < this.thesisStore.total_pages) {
      this.fetchData(Number(this.thesisStore.current_page) + 1);
    }
  }
}
</script>

<style lang="stylus" scoped>
.tabs
  position fixed
  top 46px
  left 0px
  z-index 99
  width 100%
  border-top 1px #e8e8e8 solid

.theses
  padding 56px 16px 8px
  width 100%
  background #f5f5f5
  .report
    margin-bottom 12px
    padding 16px 16px 12px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 68px

.van-panel__footer
  padding 12px 0px 0px
</style>
