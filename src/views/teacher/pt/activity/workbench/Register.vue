<template lang="pug">
NavContainer(title="打卡情况" :loading="activityStore.loading")
  ListView(
    :data="registerStore.records"
    :loading="activityStore.loading"
    :pullup="!activityStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="loadMore"
    @refresh="fetchData()")
    .registers
      .header
        .date-cell(@click="visibieDate = true")
          span {{ $dayjs(currentDate).format('YYYY/MM/DD') }}
          van-icon(name="arrow-down")
        .ring-box(v-if="registerStore.info.total > 0")
          G2Pie(:charData.sync="basePie" unit="次" :colors="['#1FA0FF', '#CDCBCE']" id="A1" v-if="basePie.length")
        .count-box
          .count-item(v-for="(item, index) in infos" :key="index")
            .key {{ item.title }}
            .value
              strong.count {{ item.count }}
              strong.unit 次
        .tag-cell
          van-tag(
            v-for="(item, index) in tabs"
            :key="index"
            :color="tabIndex === item.key ? '#EDF7FF' : '#f5f5f5'"
            :text-color="tabIndex === item.key ? '#3DA8F5' : '#808080'"
            size="medium"
            @click="onTab(item)") {{ item.label }}({{ item.count }})
      .main
        Empty(v-if="registerStore.records.length === 0")
        .register(v-for="(item, index) in registerStore.records" :key="index")
          .flex
            .avatar(:style="{background: initBackground() }") {{ (item.student_name || '').slice(0, 1) }}
            .info-box
              .name {{ item.student_name }}
              .address {{ item.address }}
          .date(v-if="item.state === 'done'") {{ $dayjs(item.created_at).format('HH:mm') }}

  van-popup(
    v-model="visibieDate"
    position="bottom")
    van-datetime-picker(
      v-model="currentDate"
      :min-date="minDate"
      type="date"
      @cancel="onCancel"
      @confirm="changeDate")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2Pie from '@/components/pt/G2Pie.vue';
import activityStore from '@/store/modules/pt/activity.store';

@Component({
  components: {
    G2Pie,
  },
})
export default class Register extends Vue {
  private currentDate: any = new Date();
  private visibieDate: boolean = false;
  private minDate: any = new Date(2020, 0, 1);
  private tabIndex: string = 'done';
  private registerStore: any = {
    info: {},
    records: [],
  };
  get role() {
    return this.$tools.getRole();
  }
  get activityStore() {
    return activityStore || {};
  }
  get infos() {
    return [
      {
        title: '应打卡',
        key: 'total',
      },
      {
        title: '已打卡',
        key: 'done',
      },
      {
        title: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      ...item,
      count: this.registerStore.info[item.key] || 0,
    }));
  }

  get basePie() {
    return [
      {
        label: '已打卡',
        key: 'done',
      },
      {
        label: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      state: item.label,
      amount: this.registerStore.info[item.key] || 0,
      percent: +((this.registerStore.info[item.key] / this.registerStore.info.total) * 100).toFixed(2),
    }));
  }
  get tabs() {
    return [
      {
        label: '已打卡',
        key: 'done',
        count: this.registerStore.info.done,
      },
      {
        label: '未打卡',
        key: 'undo',
        count: this.registerStore.info.undo,
      },
    ];
  }

  public mounted() {
    activityStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      date: this.currentDate ? this.$dayjs(this.currentDate).format('YYYY-MM-DD') : '',
      q: {
        state_eq: this.tabIndex,
      },
    };
    const { data } = await activityStore.getRegisters(params);
    data.registers = data.registers.map((item: any) => ({
      ...item,
      student_name: item.student && item.student.name,
    }));
    this.registerStore =
      data.current_page === 1
        ? {
            ...data,
            records: data.registers,
          }
        : {
            ...data,
            records: this.registerStore.records.concat(data.registers),
          };
  }

  public loadMore() {
    if (this.registerStore.current_page < this.registerStore.total_pages) {
      this.fetchData(Number(this.registerStore.current_page) + 1);
    }
  }

  public changeDate(date: any) {
    this.currentDate = date;
    this.fetchData();
    this.visibieDate = false;
  }

  public onCancel() {
    this.visibieDate = false;
    this.currentDate = new Date();
  }

  public onTab(val: any) {
    if (this.tabIndex !== val.key) {
      this.tabIndex = val.key;
      this.fetchData();
    }
  }

  public initBackground() {
    const colors = ['#CDD080', '#E3A26D', '#7FB2E5', '#7FB2E5'];
    const index = Math.floor(Math.random() * 4);
    return colors[index] || '#CDD080';
  }
}
</script>

<style lang="stylus" scoped>
.registers
  width 100%
  background #fff
  .header
    padding 20px 16px 0px
    .date-cell
      width 100%
      color #383838
      font-weight 500s
      font-size 16px
      line-height 20px
      .van-icon
        margin-left 8px
        color #808080
    .ring-box
      margin-top 24px
      width 100%
    .count-box
      display flex
      justify-content space-around
      align-items center
      width 100%
      .count-item
        margin-top 36px
        .key
          margin-bottom 8px
          color #A6A6A6
          font-weight 500
          font-size 12px
          line-height 14px
        .count
          color #383838
          font-weight 500
          font-size 30px
          font-family DINCond-Medium, DINCond
          line-height 30px
        .unit
          margin-left 4px
          color #808080
          font-size 12px
          line-height 18px
    .tag-cell
      margin-top 30px
      width 100%
      .van-tag
        margin-right 10px
  .main
    padding 10px 16px
    width 100%
    .register
      display flex
      justify-content space-between
      margin-bottom 10px
      padding 12px
      min-height 68px
      width 100%
      background #F5F5F5
      .avatar
        min-width 36px
        width 36px
        height 36px
        border-radius 50%
        background #CDD184
        color #fff
        text-align center
        font-weight 500s
        font-size 16px
        line-height 36px
      .info-box
        padding 0px 12px
        .name
          color #383838
          font-size 15px
          line-height 24px
        .address
          color #A6A6A6
          font-size 13px
          line-height 20px
      .date
        min-width 40px
        color #A6A6A6
        font-size 15px
        line-height 24px
</style>
