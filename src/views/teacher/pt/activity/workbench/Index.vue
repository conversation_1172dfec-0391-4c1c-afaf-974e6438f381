<template lang="pug">
NavContainer(title="工作台" :loading="activityStore.loading")
  .work
    .cell(
      v-for="(item, index) in modules" :key="index"
      :style="{marginBottom: index % 4 === 0 ? '12px' : '100px'}"
      @click="onMenu(item)")
      van-cell(
        is-link
        :title="item.label"
        :icon="item.icon"
        :border="false")
        template(slot="title")
          label {{ item.label }}
        template(slot="default")
          van-tag(:color="item.tag.color" :text-color="item.tag.textColor")
            span {{ item.tag.text }}
      .divider(v-if="index === 0")
      .info-box(v-if="['reports', 'evaluations', 'theses'].includes(item.key)")
        .count-card
          .count-item(v-for="(item, index) in item.infos" :key="index")
            .key {{ item.title }}
            .value
              strong.count {{ item.count }}
              strong.unit 份
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import activityStore from '@/store/modules/pt/activity.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  private info: any = {
    report: {},
    register: {
      today: {},
    },
    company: {},
    thesis: {},
    defence: {},
  };
  get role() {
    return this.$tools.getRole();
  }
  get activityStore() {
    return activityStore || {};
  }
  get modules() {
    return [
      {
        label: '打卡情况',
        icon: 'location-o',
        key: 'registers',
        infos: [],
      },
      {
        label: '实习周记',
        icon: 'notes-o',
        key: 'reports',
        infos: [
          {
            title: '当前应收',
            key: 'total',
          },
          {
            title: '正常提交',
            key: 'normal',
          },
          {
            title: '迟交',
            key: 'late',
          },
          {
            title: '未提交',
            key: 'pending',
          },
        ].map((item: any) => ({
          ...item,
          count: this.info.report[item.key] || 0,
        })),
      },
      {
        label: '企业评价',
        icon: 'comment-o',
        key: 'evaluations',
        infos: [
          {
            title: '应收',
            key: 'total',
          },
          {
            title: '已提交',
            key: 'released',
          },
          {
            title: '未提交',
            key: 'pending',
          },
        ].map((item: any) => ({
          ...item,
          count: this.info.company[item.key] || 0,
        })),
      },
      {
        label: '实习报告',
        icon: 'orders-o',
        key: 'theses',
        infos: [
          {
            title: '应收',
            key: 'total',
          },
          {
            title: '已提交',
            key: 'published',
          },
          {
            title: '未提交',
            key: 'pending',
          },
        ].map((item: any) => ({
          ...item,
          count: this.info.thesis[item.key] || 0,
        })),
      },
      {
        label: '答辩',
        icon: 'description',
        key: 'replies',
      },
    ].map((item: any) => ({
      ...item,
      tag: this.initTag(item.key),
    }));
  }

  public created() {
    activityStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData() {
    const params = {
      parentId: this.$route.params.id,
    };
    const { data } = await activityStore.getStatistics(params);
    this.info = data.info;
  }

  public onMenu(val: any) {
    this.$router.push(`/pt/teacher/activities/${this.$route.params.id}/${val.key}`);
  }

  public initTag(key: string): any {
    const { report, register, company, thesis, defence } = this.info;
    return (
      ({
        registers: {
          color: '#f5f5f5',
          textColor: '#808080',
          text: `今日：${register.today.done}/${register.today.total}`,
        },
        reports: {
          color: '#EDF7FF',
          textColor: '#3DA8F5',
          text: `待评分 ${report.published}`,
        },
        evaluations: {
          color: '#EDF7FF',
          textColor: '#3DA8F5',
          text: `待评分 ${company.submited}`,
        },
        theses: {
          color: '#EDF7FF',
          textColor: '#3DA8F5',
          text: `待评分 ${thesis.published}`,
        },
        replies: {
          color: '#f5f5f5',
          textColor: '#808080',
          text: '等待分配时间',
        },
      } as any)[key] || {
        color: '#f5f5f5',
        textColor: '#808080',
        text: '加载中',
      }
    );
  }
}
</script>

<style lang="stylus" scoped>
.work
  padding 8px 4px
  width 100%
  height 100%
  background #fff
  .cell
    position relative
    i
      color #A6A6A6
    label
      margin-left 7px
      color #808080
      font-size 15px
      line-height 24px
    .divider
      position absolute
      top 40px
      left 24px
      z-index 99999
      height 20px
      border-left 1px #E5E5E5 solid
    .info-box
      position absolute
      top 40px
      right 20px
      left 24px
      box-sizing border-box
      padding 6px 0px 18px 20px
      height 108px
      border-left 1px #E5E5E5 solid
      .count-card
        display flex
        align-items center
        box-sizing border-box
        padding 16px
        width 100%
        background #FAFAFA
        .count-item
          width 80px
          .key
            margin-bottom 8px
            color #A6A6A6
            font-weight 500
            font-size 12px
            line-height 14px
          .count
            color #383838
            font-weight 500
            font-size 30px
            font-family DINCond-Medium, DINCond
            line-height 30px
          .unit
            margin-left 4px
            color #808080
            font-size 12px
            line-height 18px
</style>
