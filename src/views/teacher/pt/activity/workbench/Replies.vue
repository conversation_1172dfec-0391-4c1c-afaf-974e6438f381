<template lang="pug">
NavContainer.container(title="实习答辩" :loading="activityStore.loading")
  Empty
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import activityStore from '@/store/modules/pt/activity.store';

@Component({
  components: {},
})
export default class Replies extends Vue {
  get activityStore() {
    return activityStore || {};
  }

  public mounted() {
    this.fetchData();
  }

  public fetchData() {}
}
</script>

<style lang="stylus" scoped>
.container
  overflow hidden
  padding-top 48px
  width 100%
  height 100%
  background #fff
  .header
    float left
    margin-top -48px
    width 100%
  .main
    overflow auto
    width 100%
    height 100%
</style>
