<template lang="pug">
NavContainer.container(title="实习活动" :loading="activityStore.loading")
  ListView(
    :data="activityStore.activities"
    :loading="activityStore.loading"
    :pullup="!activityStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(activityStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .reports
      van-panel.report(v-for="(item, index) in activityStore.activities" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.name }}
        template(slot="default")
          .cell(style="margin-top: 20px")
            .key 时间
            .value {{ $dayjs(item.start_at).format('YYYY/MM/DD') }}-{{ $dayjs(item.end_at).format('YYYY/MM/DD') }}
          .cell
            .key 状态
            van-tag(color="#F0F9F2" text-color="#6DC37D") {{ item.zh_state }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import activityStore from '@/store/modules/pt/activity.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  get role() {
    return this.$tools.getRole();
  }
  get activityStore() {
    return activityStore || {};
  }

  public created() {
    activityStore.changeNamespace(this.role);
    this.fetchData();
  }

  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      shouldAppend: true,
      q: {
        s: ['windex desc'],
      },
    };
    activityStore.fetch(params);
  }

  public onShow(val: any) {
    this.$router.push(`/pt/teacher/activities/${val.id}`);
  }

  public formatState(val: any) {
    return ({
      starting: '进行中',
    } as any)[val.state];
  }
}
</script>

<style lang="stylus" scoped>
.reports
  padding 12px
  width 100%
  background #f5f5f5
  .report
    margin-bottom 12px
    padding 16px 16px 12px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 60px
</style>
