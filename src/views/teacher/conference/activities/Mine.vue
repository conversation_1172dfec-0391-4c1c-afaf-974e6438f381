<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ActivityMeetingStore from '@/store/modules/conference/activity_meeting.store';
import ActivityMeetingCard from '@/components/conference/ActivityMeetingCard.vue';
import { IActivityMeeting } from '@/models/conference/activity_meeting';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    ActivityMeetingCard,
  },
})
export default class TeacherActivitiesMine extends Vue {
  query: object = {};
  tabIndex: string = 'joined';
  dropdownIndex: string = '';
  meetings: IActivityMeeting[] = [];
  popupVisible: boolean = false;
  selectedMeeting: IActivityMeeting = {};

  get store() {
    return ActivityMeetingStore;
  }

  get tabs() {
    return [
      {
        text: '我参与的',
        key: 'joined',
      },
      {
        text: '我预约的',
        key: 'booked',
      },
      // {
      //   text: '待我审核',
      //   key: 'unreviewed',
      // },
    ];
  }

  get dropdownOpts() {
    return [
      { text: '已预约', value: 'booked' },
      { text: '草稿箱', value: 'draft' },
      { text: '已撤销', value: 'canceled' },
    ];
  }

  get fetchBookedQuery() {
    return {
      q: {
        reserver_type_eq: 'Teacher',
        reserver_id_eq: sessionStore.currentUser.id,
        newest_first: true,
        state_in: ['reviewed', 'unreviewed'],
      },
    };
  }

  get fetchJoinedQuery() {
    return {
      joined: true,
      q: {
        newest_first: true,
      },
    };
  }

  mounted() {
    if (this.$route.query.dropdown) {
      this.dropdownIndex = this.$route.query.dropdown as string;
    }
    if (this.$route.query.tab) {
      this.tabIndex = this.$route.query.tab as string;
    }
    this.tabChangeQuery(this.tabIndex);
    this.dropdownChangeQuery(this.dropdownIndex);
    this.fetchData();
  }

  tabChange(key: string) {
    this.tabChangeQuery(key);
    this.fetchData();
  }

  tabChangeQuery(key: string) {
    switch (key) {
      case 'joined':
        this.dropdownIndex = '';
        this.query = this.fetchJoinedQuery;
        break;
      case 'booked':
        this.dropdownIndex = 'booked';
        this.query = this.fetchBookedQuery;
        break;
      default:
        break;
    }
  }

  dropdownChange(key: string) {
    this.dropdownChangeQuery(key);
    this.fetchData();
  }

  dropdownChangeQuery(key: string) {
    switch (key) {
      case 'booked':
        this.query = this.fetchBookedQuery;
        break;
      case 'draft':
        this.query = {
          q: {
            reserver_type_eq: 'Teacher',
            reserver_id_eq: sessionStore.currentUser.id,
            state_eq: 'draft',
            newest_first: true,
          },
        };
        break;
      case 'canceled':
        this.query = {
          q: {
            reserver_type_eq: 'Teacher',
            reserver_id_eq: sessionStore.currentUser.id,
            state_eq: 'canceled',
            newest_first: true,
          },
        };
        break;
      default:
        break;
    }
  }

  async fetchData() {
    this.meetings = [];
    await this.store.fetch({
      per_page: 99999,
      page: 1,
      ...this.query,
    });
    this.query = {};
    this.meetings = this.store.records;
  }

  showPopup(meeting: IActivityMeeting) {
    if (this.dropdownIndex === 'booked') {
      this.selectedMeeting = meeting;
      this.popupVisible = true;
    }
  }

  cancelPopup() {
    this.popupVisible = false;
  }

  cancelMeeting() {
    this.store.update({
      id: this.selectedMeeting.id,
      state: 'canceled',
    });
    this.dropdownChange('booked');
    this.popupVisible = false;
  }
}
</script>

<template lang="pug">
  NavContainer(
    title="我的会议"
    :loading="store.loading"
    search
    v-model="query"
    placeholder="输入会议名称、会议室名称"
    :variables="['title', 'meeting_room_name']"
  )
    //- template(slot="title")
    Tabs.tabs(v-model="tabIndex" :tabs="tabs" @change="tabChange")
    .booked(v-if="tabIndex === 'joined'")
      .meeting-cell(v-for="meeting in meetings")
        ActivityMeetingCard(:record="meeting")
    .booked(v-if="tabIndex === 'booked'")
      van-dropdown-menu.dropdown-menu
        van-dropdown-item(v-model='dropdownIndex' :options="dropdownOpts" @change="dropdownChange")
      .meeting-cell(v-for="meeting in meetings" @click="showPopup(meeting)")
        ActivityMeetingCard(:record="meeting")
          template(#tags="{ meeting }")
            .reviewed-tag.tag(v-show="meeting.state === 'reviewed' && !meeting.is_over")
              van-icon.icon(name="checked")
              span.tag-text 预约成功
            .unreviewed-tag.tag(v-show="meeting.state === 'unreviewed' && !meeting.is_over")
              van-icon.icon(name="clear")
              span.tag-text 待审核
            .gray-tag.tag(v-show="meeting.state === 'reviewed' && meeting.is_over")
              van-icon.icon(name="checked")
              span.tag-text 预约成功
            .gray-tag.tag(v-show="meeting.state === 'unreviewed' && meeting.is_over")
              van-icon.icon(name="clear")
              span.tag-text 待审核
      van-popup(
        v-model="popupVisible"
        round
        position='bottom'
      )
        .popup
          .title 操作
          .cancel-meeting(@click="cancelMeeting") 取消预约
          .cancel(@click="cancelPopup") 取消

</template>

<style lang="stylus" scoped>
.tabs
  position sticky
  top 0
  z-index 1000

.dropdown-menu
  position sticky
  top 46px
  z-index 999

.meeting-cell
  margin 12px
.reviewed-tag
  background-color #F0F9F2
  color #6DC37D

.unreviewed-tag
  background-color #EDF7FF
  color #3DA8F5

.gray-tag
  background-color #F5F5F5
  color #A6A6A6

.tag
  padding 2px

.icon
  margin-right 4px
  line-height 12px
  z-index 1
.popup
  text-align center
  .title
    padding 16px
    font-weight 600
  .cancel-meeting
    color red
    border 1px solid #E8E8E8
    padding 16px
  .cancel
    color #A6A6A6
    background-color #FAFAFA
    padding 16px
</style>
