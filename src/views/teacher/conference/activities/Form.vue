<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ActivityMeetingStore from '@/store/modules/conference/activity_meeting.store';
import ConferenceRoomStore from '@/store/modules/conference/conference_room.store';
import TemplateForm from '@/components/form/TemplateForm.vue';
import { cloneDeep } from 'lodash/fp';
import sessionStore from '@/store/modules/session.store';
import { resTeacherDepartmentStore } from '@/store/modules/res/teacher/department.store';
import { meetingTemplate } from './meetingTemplate';

@Component({
  components: {
    TemplateForm,
  },
})
export default class TeacherActivitiesForm extends Vue {
  formData: object = {};
  template: object[] = meetingTemplate;

  get store() {
    return ActivityMeetingStore;
  }

  get roomStore() {
    return ConferenceRoomStore;
  }

  get departmentStore() {
    return resTeacherDepartmentStore;
  }

  async mounted() {
    this.departmentStore.init({ params: { q: { depth_eq: 1 } } });
    const { department_ids } = sessionStore.currentUser;
    if (department_ids) {
      // Object.assign(this.formData, { department_ids: String(department_ids[0]) });
    }
    this.updateTemplateDepartment();
    this.updateTemplateRoom();
    this.updateTemplateTimeRange();
  }

  async updateTemplateDepartment() {
    const {
      data: { departments },
    } = await this.departmentStore.index({ page: 1, per_page: 100 });
    const options = departments.map((d: IObject) => ({ label: d.name, value: d.id }));
    this.template.splice(8, 1, {
      key: 'meeting_department_ids',
      name: '组织部门',
      layout: {
        component: 'select',
        required: true,
        type: 'string',
        options,
      },
      model: { attr_type: 'string' },
    });
  }

  async updateTemplateRoom() {
    await this.fetchRoom();
    const options = this.roomStore.records.map(room => {
      return { label: room.name, value: room.id };
    });
    this.template.splice(4, 1, {
      key: 'meeting_room_id',
      name: '会议室',
      layout: {
        component: 'select',
        placeholder: '请选择会议室',
        required: true,
        type: 'string',
        options,
      },
      model: { attr_type: 'string' },
    });
  }

  async fetchRoom() {
    const params = {
      page: 1,
      per_page: 999999,
    };
    await this.roomStore.fetch(params);
  }

  updateTemplateTimeRange() {
    const startTimeInt = 800;
    const endTimeInt = 2200;
    const iMax = (endTimeInt - startTimeInt) / 50;
    const result: object[] = [];
    for (let i = 0; i <= iMax; i += 1) {
      if (i % 2 !== 0) {
        const val = this.timeIntToTimeStr(startTimeInt + Math.floor((i - 1) / 2) * 100 + 30);
        result.push({ label: val, value: val });
      } else {
        const val = this.timeIntToTimeStr(startTimeInt + Math.floor(i / 2) * 100);
        result.push({ label: val, value: val });
      }
    }
    this.template.splice(2, 1, {
      key: 'begin_time',
      name: '会议开始时间',
      layout: {
        component: 'select',
        required: true,
        type: 'string',
        options: result,
      },
      model: { attr_type: 'string' },
    });
    this.template.splice(3, 1, {
      key: 'end_time',
      name: '会议结束时间',
      layout: {
        component: 'select',
        required: true,
        type: 'string',
        options: result,
      },
      model: { attr_type: 'string' },
    });
  }

  timeIntToTimeStr(timeInt: number) {
    return `${Math.floor(timeInt / 100)}:${this.minuteStr(timeInt % 100)}`;
  }

  minuteStr(num: number) {
    if (`${num}`.length < 2) {
      return `0${num}`;
    }
    return `${num}`;
  }

  parsedFormData(payload: IObject, state: string) {
    const result: any = Object.assign(cloneDeep(this.formData), payload);
    result.moderator_ids = ((result.moderator_ids as any[]) || []).map(o => o.id);
    result.user_ids = ((result.user_ids as any[]) || []).map(o => o.id);
    result.begin_time = this.$dayjs(`${this.$dayjs(result.date).format('YYYY-MM-DD')} ${result.begin_time}`);
    result.end_time = this.$dayjs(`${this.$dayjs(result.date).format('YYYY-MM-DD')} ${result.end_time}`);
    result.reserver_id = sessionStore.currentUser.id;
    result.reserver_type = 'Teacher';
    result.meeting_department_ids = [result.meeting_department_ids];
    ['files', 'meeting_type'].forEach(column => {
      result.meta = Object.assign(result.meta || {}, { [column]: (result as any)[column] });
    });
    result.state = state;
    return result;
  }

  onCreate(state: string) {
    (this.$refs.form as any).submit({
      success: (payload: IObject) => {
        const dropdown = state === 'draft' ? 'draft' : 'booked';
        this.store
          .create(this.parsedFormData(payload, state))
          .then(data => {
            this.$message.success('创建成功');
            this.$router.replace(`/conference/teacher/mine?dropdown=${dropdown}&tab=booked`);
          })
          .catch(error => {
            if (error.response) {
              error = error.response.data.message;
            }
            this.$message.error(error);
          });
      },
      fail: (error: any) => {
        this.$message.error('请完善表单信息');
      },
    });
  }
}
</script>

<template lang="pug">
.container
  NavContainer(:loading="store.loading" title="预约")
    TemplateForm(
      ref="form"
      :template="template"
      :formData="formData"
    )
      template(slot="actions")
        .bottom-actions
          .draft(@click="onCreate('draft')") 存为草稿箱
          .confirm(@click="onCreate('unreviewed')") 申请预约
</template>

<style lang="stylus" scoped>
.bottom-actions
  position fixed
  bottom 0
  left 0
  display flex
  justify-content space-between
  padding 0
  width 100%
  height 47px
  line-height 47px
  .draft
    width 50%
    background-color white
    color #3DA8F5
    text-align center
  .confirm
    width 50%
    background-color #3DA8F5
    color white
    text-align center
</style>
