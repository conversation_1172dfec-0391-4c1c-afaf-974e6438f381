<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IActivityMeeting, ActivityMeeting } from '@/models/conference/activity_meeting';
import ActivityMeetingStore from '@/store/modules/conference/activity_meeting.store';
import semesterModel, { ISemester } from '@/models/teaching/semester';
import ActivityMeetingCard from '@/components/conference/ActivityMeetingCard.vue';

@Component({
  components: { ActivityMeetingCard },
})
export default class TeacherActivitiesWeekSchedule extends Vue {
  semester: ISemester = {};
  week: number = 1; // 当前周
  meetings: IActivityMeeting[] = [];
  weeksCount: number = 20;
  loading: boolean = false;
  startDate: string = '';
  endDate: string = '';
  date: string = this.$dayjs().format('YYYY-MM-DD');
  timeLine: IObject[] = [];
  query: object = {};
  activeTimeIndex: number = -1;

  get store() {
    return ActivityMeetingStore;
  }

  mounted() {
    this.fetchSemesters();
  }

  isActive(dateStr: string) {
    return this.date.length > 0 && this.$dayjs(this.date).format('MM/DD') === dateStr;
  }

  changeDate(date: string) {
    this.date = date;
    this.activeTimeIndex = this.$dayjs(this.date).diff(this.$dayjs(this.startDate), 'day');
    this.fetchMeetings();
  }

  async fetchSemesters() {
    const { data } = await semesterModel.setRole('teacher').current();
    this.semester = data;
    this.week = data.current_week || 1;
    this.weeksCount = data.weeks_count || 20;

    this.updateTimeLine();
    this.fetchMeetings();
  }

  async fetchMeetings(emitData: any = {}) {
    try {
      this.loading = true;
      await this.store.fetch({
        page: 1,
        per_page: 999999,
        q: {
          date_eq: this.date,
          state_in: ['unreviewed', 'reviewed'],
          ...this.query,
          ...emitData,
        },
      });
      this.meetings = this.store.records;

      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  updateTimeLine() {
    this.startDate = this.$dayjs(this.semester.begin_on)
      .add((this.week - 1) * 7, 'day')
      .format('YYYY-MM-DD');

    this.endDate = this.$dayjs(this.semester.begin_on)
      .add(this.week * 7 - 1, 'day')
      .format('YYYY-MM-DD');

    if (this.activeTimeIndex === -1) {
      this.activeTimeIndex = this.$dayjs(this.date).diff(this.$dayjs(this.startDate), 'day');
    } else {
      // this.activeTimeIndex = this.$dayjs(this.date).diff(this.$dayjs(this.startDate), 'day');
      this.date = this.$dayjs(this.startDate)
        .add(this.activeTimeIndex, 'day')
        .format('YYYY-MM-DD');
    }

    this.timeLine = Array(...Array(7)).map((item, i) => {
      const date = this.$dayjs(this.startDate).add(i, 'day');
      return {
        origin: date.format('YYYY-MM-DD'),
        date: date.format('MM/DD'),
        weekday: date.format('dddd').replace('星期', '周'),
      };
    });
  }

  changeWeek(val: number) {
    this.week = this.week + val;
    if (this.week < 1) {
      this.week = 1;
      return;
    }
    if (this.week > this.weeksCount) {
      this.week = this.weeksCount;
      return;
    }
    this.updateTimeLine();
    this.fetchMeetings();
  }
}
</script>

<template lang="pug">
NavContainer.container(
  :loading="store.loading"
  search
  v-model="query"
  placeholder="输入会议名称、会议室名称"
  :variables="['title', 'meeting_room_name']"
  @change="fetchMeetings"
)
  template(#title)
    .header
      van-icon.week-icon(name="arrow-left" @click="changeWeek(-1)")
      .week 第 {{ week }} 周
      van-icon.week-icon(name="arrow" @click="changeWeek(+1)")
  .time-line-shell
    .time-line
      .time-cell(
        v-for="item in timeLine"
        :style="isActive(item.date) ? { borderBottom: '2px solid #3DA8F5'} : {}"
        @click="changeDate(item.origin)"
      )
        .time-text {{ item.weekday }}
        .time-date-text {{ item.date }}
  Empty(v-if="meetings.length === 0" desc="无会议")
  .content
    .meeting-cell(v-for="meeting in meetings")
      ActivityMeetingCard(:record="meeting")


</template>

<style lang="stylus" scoped>
.container
  >>> .nav-bar-container__content
    overflow visible
  .header
    display flex
    justify-content center
    align-items center
    height 100%
    background rgba(255, 255, 255, 1)
    box-shadow 0px 1px 2px 0px rgba(0, 0, 0, 0.08)
    font-weight 500
    font-size 17px
    .week
      flex-shrink 0
      margin 0 16px
      color rgba(56, 56, 56, 1)
      white-space nowrap
      word-break keep-all
    .week-icon
      color #A6A6A6
  .content
    padding-bottom 50px
    .meeting-cell
      margin 12px
  .time-line-shell
    width 100%
    position sticky
    top 46px
    z-index 1000
    .time-line
      display flex
      justify-content flex-start
      overflow-x scroll
      background-color white
      .time-cell
        margin 12px 20px 0px 20px
        width 68px
        .time-text
          text-align center
          white-space nowrap
          font-size 14px
        .time-date-text
          margin-bottom 10px
          font-size 8px
          text-align center
</style>
