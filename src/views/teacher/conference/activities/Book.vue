<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import TeacherActivitiesWeekSchedule from './WeekSchedule.vue';

@Component({
  components: {
    TeacherActivitiesWeekSchedule,
  },
})
export default class TeacherActivitiesBook extends Vue {
  startBooking() {
    this.$router.push('/conference/teacher/form');
  }
}
</script>

<template lang="pug">
.container
  TeacherActivitiesWeekSchedule
  .add-buttom(@click="startBooking") +

</template>

<style lang="stylus" scoped>
.add-buttom
  position fixed
  right 16px
  bottom 68px
  z-index 9999
  width 44px
  height 44px
  border-radius 22px
  background-color #3DA8F5
  color white
  text-align center
  font-size 20px
  line-height 44px
</style>
