<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import session from '@/models/session';

@Component
export default class ConferenceTeacherActivitiesEntry extends Vue {
  get activeIndex() {
    return ['/conference/teacher/week_schedule', '/conference/teacher/mine', '/conference/teacher/book'].indexOf(
      this.$route.path,
    );
  }
}
</script>

<template lang="pug">
.lessons-container
  router-view
  van-tabbar.tabbar(:value="activeIndex" fixed safe-area-inset-bottom :zIndex="1000")
    van-tabbar-item(icon="calender-o" to="/conference/teacher/week_schedule" :replace="true") 一周会议安排
    van-tabbar-item(icon="orders-o" to="/conference/teacher/mine" :replace="true") 我的会议
    van-tabbar-item(icon="records" to="/conference/teacher/book" :replace="true") 预约会议
</template>

<style lang="stylus" scoped>
.lessons-container
  position relative
  overflow auto
  padding 0px 0px 50px
  height 100vh
  -webkit-overflow-scrolling touch
</style>
