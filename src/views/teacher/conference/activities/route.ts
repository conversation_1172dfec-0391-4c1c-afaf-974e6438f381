import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/conference/teacher',
    name: 'conference_teacher_activities_entry',
    component: () => import(/* webpackChunkName: "conference_teacher_activities_entry" */ './Entry.vue'),
    meta: {
      title: '会议预约系统',
      requireAuth: true,
      roles: ['Teacher'],
    },
    children: [
      {
        path: 'book',
        name: 'conference_teacher_activities_book',
        component: () => import(/* webpackChunkName: "conference_teacher_book" */ './Book.vue'),
        meta: {
          title: '预约会议',
          requireAuth: true,
          roles: ['Teacher'],
        },
      },
      {
        path: 'mine',
        name: 'conference_teacher_activities_mine',
        component: () => import(/* webpackChunkName: "conference_teacher_mine" */ './Mine.vue'),
        meta: {
          title: '我的会议',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: false,
        },
      },
      {
        path: 'week_schedule',
        name: 'conference_teacher_activities_week_schedule',
        component: () => import(/* webpackChunkName: "conference_teacher_week_schedule" */ './WeekSchedule.vue'),
        meta: {
          title: '一周会议安排',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: '/conference/teacher/form',
    name: 'conference_teacher_activities_form',
    component: () => import(/* webpackChunkName: "conference_teacher_form" */ './Form.vue'),
    meta: {
      title: '预约',
      requireAuth: true,
      roles: ['Teacher'],
      keepAlive: true,
    },
  },
];

export default routes;
