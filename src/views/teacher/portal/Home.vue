<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Tofu from '@/components/portal/Tofu.vue';
import TofuAppStore from '@/store/modules/tofu/app.store';
import TofuStarStore from '@/store/modules/tofu/star.store';
import TofuWebsiteStore from '@/store/modules/tofu/website.store';
import { ITofu } from '@/models/tofu/app';

@Component({
  components: {
    Tofu,
  },
})
export default class PortalHome extends Vue {
  mounted() {
    this.starStore.reset();
    this.appStore.reset();
    this.websiteStore.reset();
    this.appStore.fetch({ nested: true });
    this.starStore.fetch();
    this.websiteStore.fetch();
  }

  get appStore() {
    return TofuAppStore;
  }

  get starStore() {
    return TofuStarStore;
  }

  get websiteStore() {
    return TofuWebsiteStore;
  }

  clickTofu(tofu: ITofu) {
    const url = tofu.mobile_url as string;
    if (url) {
      if (url.startsWith('http')) {
        window.open(url, '_blank');
      } else {
        this.$router.replace(url);
      }
    }
  }
}
</script>

<template lang="pug">
.container
  h3.title 我的星标
  .star-group
    .tofu(v-for='tofu in starStore.records', :key='tofu.id', v-if='tofu.mobile_url')
      Tofu(:tofu='tofu', @clickTofu='clickTofu', :starVisible='true', :subscript='true')
        template(#rightTop)
          .star
            van-icon(name='star')
  h3.title 全部应用
  .app-group
    .tofu(v-for='tofu in appStore.records', :key='tofu.id', v-if='tofu.mobile_url')
      Tofu(:tofu='tofu', titleSize='11px', @clickTofu='clickTofu')
    .tofu(v-for='tofu in websiteStore.records', :key='tofu.id', v-if='tofu.mobile_url')
      Tofu(:tofu='tofu', titleSize='11px', @clickTofu='clickTofu')
</template>

<style lang="stylus" scoped>
.container
  padding 0 13px
  width 100%
  background-color rgba(247, 247, 248, 1)
  .title
    padding 11px 0
    font-size 14px
  .star-group
    display grid
    gap 13px 11px
    grid-template-columns repeat(3, 1fr)
    .tofu
      width 100%
      height 140px
  .app-group
    display grid
    gap 8px 10px
    grid-template-columns repeat(4, 1fr)

.star
  color #3DA8F5

.gray
  filter grayscale(100%)
  filter gray
  -webkit-filter grayscale(100%)
  -moz-filter grayscale(100%)
  -ms-filter grayscale(100%)
  -o-filter grayscale(100%)
</style>
