<script lang="ts">
import sessionStore from '@/store/modules/session.store';
import { Component, Vue } from 'vue-property-decorator';

@Component
export default class PortalEntry extends Vue {
  get activeIndex() {
    return ['/portal/user/informs', '/portal/user/todo', '/portal/user/home', '/portal/user/mine'].indexOf(
      this.$route.path,
    );
  }

  get isTeacher() {
    return sessionStore.role === 'teacher';
  }
}
</script>

<template lang="pug">
.lessons-container
  NavContainer(title='上海电子信息职业技术学院')
    router-view
  van-tabbar.tabbar(:value='activeIndex', fixed, safe-area-inset-bottom, :zIndex='1000')
    van-tabbar-item(v-if='isTeacher', icon='notes-o', to='/portal/user/informs', :replace='true') 通知公告
    //- van-tabbar-item(icon='apps-o', to='/portal/user/todo', :replace='true') 我的待办
    van-tabbar-item(icon='wap-home-o', to='/portal/user/home', :replace='true') 应用入口
    van-tabbar-item(icon='contact', to='/portal/user/mine', :replace='true') 我的
</template>

<style lang="stylus" scoped>
.lessons-container
  position relative
  overflow auto
  padding 0px 0px 50px
  height 100vh
  -webkit-overflow-scrolling touch
</style>
