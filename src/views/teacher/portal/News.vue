<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Tofu from '@/components/portal/Tofu.vue';
import TofuAppStore from '@/store/modules/tofu/app.store';
import TofuStarStore from '@/store/modules/tofu/star.store';
import InformNewsStore from '@/store/modules/inform/news.store';

import InformModStore from '@/store/modules/inform/mod.store';
import sessionStore from '@/store/modules/session.store';
import { IMod } from '../../../models/inform/mod';
import FileServer from '../../../models/file';
import InformNewsList from '../../../components/inform/NewsList.vue';

@Component({
  components: {
    InformNewsList,
  },
})
export default class PortalNews extends Vue {
  newsTabs: IObject[] = [];
  newsTabKey: string = '';

  get modStore() {
    return InformModStore;
  }

  get newsStore() {
    return InformNewsStore;
  }

  async fetchMods() {
    const { data } = await this.modStore.fetch({
      page: 1,
      per_page: 999999,
      q: {
        school_id_eq: sessionStore.currentUser.school_id,
        mod_type_eq: 'Inform::Mod::News',
        platform_cont: '统一门户',
        title_in: ['学校概况'],
      },
    });

    this.newsTabs = (data.inform_mods as IMod[]).map(mod => ({ key: `${mod.id}`, text: mod.title }));
    this.newsTabKey = data.inform_mods[0] ? `${data.inform_mods[0].id}` : '';
    this.fetchNews();
  }

  fetchNews(page = 1) {
    this.newsStore.fetch({
      shouldAppend: true,
      page,
      per_page: 10,
      q: { inform_mod_id_eq: +this.newsTabKey, state_eq: '已发布' },
    });
  }

  changeNewsTab(key: number) {
    this.fetchNews();
  }

  showMoreNews() {
    this.$router.push('/portal/user/news');
  }
  mounted() {
    this.fetchMods();
  }
}
</script>

<template lang="pug">
.container
  .module
    .header.flex-between
      h3.header-title 新闻
      //- .more.gray(@click='showMoreNews') 更多
    .content-contianer
      .tabs
        Tabs(:tabs='newsTabs', v-model='newsTabKey', @change='changeNewsTab')
      ListView(
        :data='newsStore.records',
        :loading='newsStore.loading',
        :pullup='!newsStore.finish',
        :pulldown='true',
        emptyText='暂无记录',
        @loadMore='fetchNews(newsStore.currentPage + 1)',
        @refresh='fetchNews(1)'
      )
        .newses
          .news(v-for='news in newsStore.records', :key='news.id')
            router-link(:to='`/portal/user/news/${news.id}`')
              .title.one-line {{ news.title }}
              .footer.flex-between
                span {{ news.tag_list.map((tag) => `#${tag}`).join(" ") }}
                span {{ $dayjs(news.publish_time).format("YYYY-MM-DD") }}
</template>

<style lang="stylus" scoped>
.container
  padding 0 13px
  width 100%
  height 100%
  background-color rgba(247, 247, 248, 1)
  .header-title
    padding 11px 0
    font-weight 500
    font-size 14px
  .module
    margin-bottom 9px
    padding-top 12px
    height 100%
    .content-contianer
      padding 0 20px
      height 100%
      border-radius 6px
      background-color white
      .tabs
        padding-top 7px
      .newses
        .news
          display block
          margin-top 20px
          &:last-child
            padding-bottom 26px
          .title
            margin-bottom 6px
            color rgba(56, 56, 56, 1)
            font-weight 500
            font-size 14px
            line-height 20px
          .footer
            margin-top 4px
            color rgba(166, 166, 166, 1)
            font-weight 400
            font-size 12px
            line-height 12px

.one-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 1

.gray
  color #A6A6A6
</style>
