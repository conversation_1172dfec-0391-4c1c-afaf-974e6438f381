<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import InformAdviseStore from '@/store/modules/inform/advise.store';
import sessionStore from '@/store/modules/session.store';
import InformModStore from '@/store/modules/inform/mod.store';
import { IMod } from '@/models/inform/mod';

@Component({
  components: {},
})
export default class InformAdviseIndex extends Vue {
  adviseTabs: IObject[] = [];
  adviseTabKey: string = '';

  get adviseStore() {
    return InformAdviseStore;
  }

  get modStore() {
    return InformModStore;
  }

  async fetchMods() {
    const { data } = await this.modStore.fetch({
      page: 1,
      per_page: 10,
      q: {
        school_id_eq: sessionStore.currentUser.school_id,
        type_eq: 'Inform::Mod::Advise',
        platform_cont: '统一门户',
      },
    });

    const adviseMods = data.inform_mods as IMod[];

    this.adviseTabs = adviseMods.map(mod => ({ key: `${mod.id}`, text: mod.title }));
    this.adviseTabKey = adviseMods[0] ? `${adviseMods[0].id}` : '';
    this.fetchAdvises(1);
  }
  public mounted() {
    this.fetchMods();
  }

  fetchAdvises(page: number = 1) {
    this.adviseStore.fetch({
      shouldAppend: true,
      page,
      per_page: 10,
      q: { inform_mod_id_eq: +this.adviseTabKey, state_eq: '已发布' },
    });
  }

  changeAdviseTab(key: number) {
    this.fetchAdvises();
  }
}
</script>

<template lang="pug">
.container
  Tabs(:tabs='adviseTabs', v-model='adviseTabKey', @change='changeAdviseTab')
  ListView(
    :data='adviseStore.records',
    :loading='adviseStore.loading',
    :pullup='!adviseStore.finish',
    :pulldown='true',
    emptyText='暂无记录',
    @loadMore='fetchAdvises(adviseStore.currentPage + 1)',
    @refresh='fetchAdvises(1)'
  )
    .advises
      .advise(v-for='advise in adviseStore.records', :key='advise.id')
        router-link(:to='`/portal/user/advises/${advise.id}`')
          .title.one-line {{ advise.title }}
          .footer.flex-between
            span {{ advise.tag_list.map((tag) => `#${tag}`).join(" ") }}
            span {{ $dayjs(advise.publish_time).format("YYYY-MM-DD") }}
</template>

<style lang="stylus" scoped>
.container
  height 100%
  background-color white
  .advises
    margin 0 18px
    .advise
      display block
      margin-top 20px
      padding 20px 16px
      border-radius 6px
      background-color #F7F7F8
      &:last-child
        padding-bottom 26px
      .title
        margin-bottom 6px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 14px
        line-height 20px
      .footer
        margin-top 4px
        color rgba(166, 166, 166, 1)
        font-weight 400
        font-size 12px
        line-height 12px

.one-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 1
</style>
