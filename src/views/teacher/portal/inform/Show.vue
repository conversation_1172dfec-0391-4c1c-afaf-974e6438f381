<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import InformAdviseStore from '@/store/modules/inform/advise.store';
import InformNewsStore from '@/store/modules/inform/news.store';

@Component({
  components: {},
})
export default class InformShow extends Vue {
  inform: IObject = {};

  get store() {
    return this.$route.path.includes('news') ? InformNewsStore : InformAdviseStore;
  }

  async fetchRecord() {
    await this.store.find(this.$route.params.id);
    this.inform = this.store.record;
  }

  mounted() {
    this.fetchRecord();
  }
}
</script>

<template lang="pug">
.inform-show
  .title {{ inform.title }}
  .sub-title
    span.tabs {{ inform.tag_list.map(tag=> `#${tag}`).join(' ') }}
    span.publish-time {{ $dayjs(inform.publish_time).format('YYYY/MM/DD') }}
  .content(v-html="inform.content")
</template>

<style lang="stylus" scoped>
.inform-show
  padding 20px 15px
  background-color white
  .title
    font-weight 500
    font-size 18px
  .sub-title
    margin 20px 0
    color #A6A6A6
    .tabs
      margin-right 10px
  .content >>> img, p, span
    width 100%
</style>
