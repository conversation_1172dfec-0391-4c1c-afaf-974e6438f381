<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import InformNewsStore from '@/store/modules/inform/news.store';
import sessionStore from '@/store/modules/session.store';
import InformModStore from '@/store/modules/inform/mod.store';
import { IMod } from '../../../../models/inform/mod';
import InformNewsList from '../../../../components/inform/NewsList.vue';

@Component({
  components: {
    InformNewsList,
  },
})
export default class InformNewsIndex extends Vue {
  newsTabs: IObject[] = [];
  newsTabKey: string = '';

  get newsStore() {
    return InformNewsStore;
  }

  get modStore() {
    return InformModStore;
  }

  async fetchMods() {
    const { data } = await this.modStore.fetch({
      shouldAppend: true,
      page: 1,
      per_page: 10,
      q: { school_id_eq: sessionStore.currentUser.school_id, type_eq: 'Inform::Mod::news', platform_cont: '统一门户' },
    });

    const newsMods = data.inform_mods as IMod[];

    this.newsTabs = newsMods.map(mod => ({ key: `${mod.id}`, text: mod.title }));
    this.newsTabKey = newsMods[0] ? `${newsMods[0].id}` : '';
    this.fetchNews(1);
  }
  public mounted() {
    this.fetchMods();
  }

  fetchNews(page: number = 1) {
    this.newsStore.fetch({
      page,
      per_page: 10,
      q: { inform_mod_id_eq: +this.newsTabKey, state_eq: '已发布' },
    });
  }

  changeNewsTab(key: number) {
    this.fetchNews();
  }
}
</script>

<template lang="pug">
.container
  ListView(
    :data='newsStore.records',
    :loading='newsStore.loading',
    :pullup='!newsStore.finish',
    :pulldown='true',
    emptyText='暂无记录',
    @loadMore='fetchNews(newsStore.currentPage + 1)',
    @refresh='fetchNews(1)'
  )
    InformNewsList(v-model='newsTabKey', :newsList='newsStore.records', :tabs='newsTabs', @changeTab='changeNewsTab')
</template>

<style lang="stylus" scoped>
.container
  height 100%
  background-color white
  .newss
    margin 0 18px
    .news
      display block
      margin-top 20px
      padding 20px 16px
      border-radius 6px
      background-color #F7F7F8
      &:last-child
        padding-bottom 26px
      .title
        margin-bottom 6px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 14px
        line-height 20px
      .footer
        margin-top 4px
        color rgba(166, 166, 166, 1)
        font-weight 400
        font-size 12px
        line-height 12px

.one-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 1
</style>
