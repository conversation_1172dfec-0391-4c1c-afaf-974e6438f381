<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Tofu from '@/components/portal/Tofu.vue';
import TofuAppStore from '@/store/modules/tofu/app.store';
import TofuStarStore from '@/store/modules/tofu/star.store';
import InformNewsStore from '@/store/modules/inform/news.store';
import InformAdviseStore from '@/store/modules/inform/advise.store';
import InformModStore from '@/store/modules/inform/mod.store';
import sessionStore from '@/store/modules/session.store';
import { IMod } from '../../../models/inform/mod';
import FileServer from '../../../models/file';
import InformNewsList from '../../../components/inform/NewsList.vue';

@Component({
  components: {
    InformNewsList,
  },
})
export default class PortalInform extends Vue {
  adviseTabs: IObject[] = [];
  adviseTabKey: string = '';

  get modStore() {
    return InformModStore;
  }

  get adviseStore() {
    return InformAdviseStore;
  }

  async fetchMods() {
    const { data } = await this.modStore.fetch({
      page: 1,
      per_page: 999999,
      q: {
        school_id_eq: sessionStore.currentUser.school_id,
        mod_type_eq: 'Inform::Mod::Advise',
        platform_cont: '统一门户',
        title_in: ['学院', '部门'],
      },
    });

    this.adviseTabs = (data.inform_mods as IMod[]).map(mod => ({ key: `${mod.id}`, text: mod.title }));
    this.adviseTabKey = data.inform_mods[0] ? `${data.inform_mods[0].id}` : '';
    this.fetchAdvises();
  }

  fetchAdvises() {
    this.adviseStore.reset();
    this.adviseStore.fetch({ page: 1, per_page: 6, q: { inform_mod_id_eq: +this.adviseTabKey, state_eq: '已发布' } });
  }

  changeAdviseTab(key: number) {
    this.fetchAdvises();
  }

  showMoreAdvises() {
    this.$router.push('/portal/user/advises');
  }
  mounted() {
    this.fetchMods();
  }
}
</script>

<template lang="pug">
.container
  .module
    .header.flex-between
      h3.header-title 通知公告
      .more.gray(@click='showMoreAdvises') 更多
    .content-contianer
      .tabs
        Tabs(:tabs='adviseTabs', v-model='adviseTabKey', @change='changeAdviseTab')
      .advises
        .advise(v-for='advise in adviseStore.records', :key='advise.id')
          router-link(:to='`/portal/user/advises/${advise.id}`')
            .title.one-line {{ advise.title }}
            .footer.flex-between
              span {{ advise.tag_list.map((tag) => `#${tag}`).join(" ") }}
              span {{ $dayjs(advise.publish_time).format("YYYY-MM-DD") }}
</template>

<style lang="stylus" scoped>
.container
  padding 0 13px
  width 100%
  background-color rgba(247, 247, 248, 1)
  .header-title
    padding 11px 0
    font-weight 500
    font-size 14px
  .module
    margin-bottom 9px
    padding-top 12px
    .content-contianer
      padding 0 20px
      border-radius 6px
      background-color white
      .tabs
        padding-top 7px
      .advises
        .advise
          display block
          margin-top 20px
          &:last-child
            padding-bottom 26px
          .title
            margin-bottom 6px
            color rgba(56, 56, 56, 1)
            font-weight 500
            font-size 14px
            line-height 20px
          .footer
            margin-top 4px
            color rgba(166, 166, 166, 1)
            font-weight 400
            font-size 12px
            line-height 12px

.one-line
  display -webkit-box
  overflow hidden
  -webkit-box-orient vertical
  -webkit-line-clamp 1

.gray
  color #A6A6A6
</style>
