import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/portal/user/news',
    name: 'portal_user_news',
    component: () => import(/* webpackChunkName: "portal_user_news" */ './News.vue'),
    meta: {
      title: '学校要闻',
      roles: ['Teacher', 'Student'],
      requireAuth: false,
    },
  },
  {
    path: '/portal/user',
    name: 'portal_teacher',
    component: () => import(/* webpackChunkName: "portal_teacher" */ './Entry.vue'),
    meta: {
      title: '统一门户',
      requireAuth: true,
      roles: ['Teacher'],
    },
    children: [
      {
        path: 'home',
        name: 'portal_teacher_home',
        component: () => import(/* webpackChunkName: "portal_teacher_home" */ './Home.vue'),
        meta: {
          title: '首页',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
      {
        path: 'informs',
        name: 'portal_teacher_informs',
        component: () => import(/* webpackChunkName: "portal_teacher_informs" */ './Inform.vue'),
        meta: {
          title: '新闻通知',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
      {
        path: 'todo',
        name: 'portal_teacher_todo_index',
        component: () => import(/* webpackChunkName: "portal_teacher_todo_index" */ './Todo.vue'),
        meta: {
          title: '我的待办',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
      {
        path: 'news',
        name: 'portal_teacher_inform_news',
        component: () => import(/* webpackChunkName: "portal_teacher_inform_news" */ './inform/NewsIndex.vue'),
        meta: {
          title: '新闻',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: false,
        },
      },
      {
        path: 'advises',
        name: 'portal_teacher_inform_advise',
        component: () => import(/* webpackChunkName: "portal_teacher_inform_advise" */ './inform/AdviseIndex.vue'),
        meta: {
          title: '通知公告',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: false,
        },
      },
      {
        path: 'news/:id',
        name: 'portal_teacher_inform_news_show',
        component: () => import(/* webpackChunkName: "portal_teacher_inform_news_show" */ './inform/Show.vue'),
        meta: {
          title: '新闻',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: false,
        },
      },
      {
        path: 'advises/:id',
        name: 'portal_teacher_inform_advise_show',
        component: () => import(/* webpackChunkName: "portal_teacher_inform_advise_show" */ './inform/Show.vue'),
        meta: {
          title: '通知公告',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: false,
        },
      },
      {
        path: 'mine',
        name: 'portal_teacher_mine',
        component: () => import(/* webpackChunkName: "portal_teacher_mine" */ './Mine.vue'),
        meta: {
          title: '我的',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
