<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import InstanceStore from '@/store/modules/bpm/instance.store';
import { IInstance, InstanceType } from '@/models/bpm/instance';
import TofuAppStore from '@/store/modules/tofu/app.store';
import session from '@/models/session';
import utils from '@/utils';
import { ITofu } from '../../../models/tofu/app';
import sessionStore from '../../../store/modules/session.store';

@Component({
  components: {},
})
export default class PortalTodo extends Vue {
  typeToInfoMap: IObject = {};
  visible: boolean = false;

  get store() {
    return InstanceStore;
  }

  get appStore() {
    return TofuAppStore;
  }

  get map() {
    return {
      'Bpm::Instance': '业务流程申请',
      'Ems::CourseSetInstance': '课程教学平台',
      'Exam::ActivityInstance': '考试系统',
      'Finance::LoanVoucherInstance': '财务报销系统',
      'Finance::ProjectInstance': '财务报销系统',
      'Finance::VoucherInstance': '财务报销系统',
      'Hr::ModificationInstance': '人事管理系统',
      'Meeting::ApplicationFormInstance': '会议室预约系统',
      'Wechat::Instance': '微信矩阵管理系统',
      'Studying::Welcome::Instance': '迎新系统',
    };
  }

  fetchInstances(page: number = 1) {
    this.store.fetch({
      page,
      per_page: 4,
      q: { approving: [sessionStore.currentUser.id, sessionStore.account.type] },
    });
  }

  async mounted() {
    // 传入工号和本地token，如果工号和本地token不一致，则需要重新登录
    const code = this.$route.query.code as string;
    if (code === 'test' && !(sessionStore.currentUser.code === code)) {
      session.signOut().finally(() => {
        window.localStorage.clear();
        utils.setRedirectPath();
        this.$router.replace('/oauth');
      });
    } else {
      this.fetchInstances();
      await this.appStore.fetch();
      Object.keys(this.map).forEach(key => {
        const name = (this.map as IObject)[key];
        const info = this.appStore.records.find((tofu: ITofu) => tofu.name === name) || {};
        if (info.name || info.image) {
          this.typeToInfoMap[key] = {
            name: info.name,
            image: info.image,
          };
        }
      });
    }
  }

  getInfo(type: string) {
    return this.typeToInfoMap[type] || {};
  }

  more() {
    this.fetchInstances(this.store.currentPage + 1);
  }

  showInstance(instance: IInstance) {
    this.$router.push(`/bpm/instances/${instance.id}`);
  }
}
</script>

<template lang="pug">
.portal-todo
  h3.title 我的待办
  .instance-shell
    .header
      .left-part.flex
        .image-circle
          img(src="@/assets/icons/portal/review_blue.png")
        p 审批
    .instances
      .instance(v-for="instance in store.records" @click="showInstance(instance)")
        img.tofu-image(v-if="getInfo(instance.type).image" :src="getInfo(instance.type).image")
        img.tofu-image(v-else src="@/assets/icons/portal/tofu_placeholder.png")
        .content
          .tofu-info.flex-between
            .tofu-title.text-gray(v-if="getInfo(instance.type).name") {{ getInfo(instance.type).name }}
            .tofu-title.text-gray(v-else) 上海电子信息职业技术学院
            .time.text-gray {{ $dayjs(instance.created_at).format('MM-DD HH:mm') }}
          .body {{ instance.workflow_name }}
    .more(@click="more")
      van-icon.icon(name="arrow-down")
</template>

<style lang="stylus" scoped>
.portal-todo
  margin 10px
  .title
    margin 14px 0 16px 0
  .instance-shell
    border-radius 8px
    background-color white
    .header
      display flex
      align-items center
      padding 0 13px
      height 48px
      border-radius 8px 8px 0 0
      background-color #2494E4
      color white
      .image-circle
        display flex
        justify-content center
        align-items center
        margin-right 6px
        width 28px
        height 28px
        border-radius 14px
        background white
        -moz-border-radius 14px
        -webkit-border-radius 14px
        img
          width 14px
          height 14px
    .instances
      padding 0 12px
      .instance
        display flex
        padding 16px 10px 16px 0
        border-bottom 1px solid #E5E5E5
        cursor pointer
        &:last-child
          border-bottom 0px solid white
        .tofu-image
          margin-right 4px
          width 20px
          height 20px
        .content
          width 100%
          .tofu-info
            width 100%
            .tofu-title
              font-size 14px
    .more
      display flex
      justify-content center
      align-items center
      height 30px
      .icon
        color #A6A6A6
</style>
