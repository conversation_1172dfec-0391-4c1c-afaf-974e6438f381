import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/assess/activities/:id/self_assess',
    name: 'assess_activity_self_assess',
    component: () => import(/* webpackChunkName: "assess_activity_self_assess" */ './SelfAssess.vue'),
    meta: {
      title: '人事考核系统',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/assess/activities/:id/score_entries',
    name: 'assess_activity_score_entries',
    component: () => import(/* webpackChunkName: "assess_activity_score_entries" */ './Index.vue'),
    meta: {
      title: '人事考核系统',
      requireAuth: true,
      roles: ['Teacher'],
      keepAlive: true,
    },
  },
];

export default routes;
