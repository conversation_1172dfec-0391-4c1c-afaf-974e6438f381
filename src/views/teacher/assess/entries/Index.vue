<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import scoreEntryStore from '@/store/modules/assess/scoreEntry.store';
import { IEntry } from '../../../../models/assess/scoreEntry';

@Component({
  components: {},
})
export default class AssessActivityEntries extends Vue {
  keyword: string = '';
  scrollTop: number = 0;
  state: string = '全部';
  stateValue: string = 'all';
  actions: any[] = [
    { name: '全部', value: 'all' },
    { name: '待评', value: 'noscore' },
    { name: '已评', value: 'score' },
  ];
  filterVisible: boolean = false;

  get activityId() {
    return this.$route.params.id;
  }
  get store() {
    return scoreEntryStore;
  }

  async activated() {
    this.store.SET_RECORDS({ current_page: 0 });
    await this.fetchData(1);
    this.$nextTick(() => {
      (this.$refs.listView as any).backPosition();
    });
  }

  async fetchData(page: number = 1) {
    await this.store.fetchByParent({
      parentId: this.activityId,
      page,
      per_page: 100,
      type: this.stateValue,
      q: {
        teacher_name_or_teacher_code_cont_any: this.keyword,
      },
    });
  }
  onSearch() {
    this.fetchData(1);
  }
  onResetSearch() {
    this.keyword = '';
    this.fetchData(1);
  }
  show(item: IEntry) {
    this.$router.push({
      path: `/assess/score_entries/${item.id}/update_score`,
      query: {
        activityId: this.activityId,
        teacherId: String(item.teacher.id),
        teacherName: item.teacher.name,
      },
    });
  }
  onFilter(action: any) {
    this.state = action.name;
    this.stateValue = action.value;
    this.filterVisible = false;
    this.fetchData(1);
  }
}
</script>

<template lang="pug">
NavContainer.container(title="部门考核")
  template(slot="right")
    span.text-primary(@click="filterVisible = true") {{ state }}
  .list-view-container(v-loading="store.loading")
    form(action="." @submit.prevent="")
      van-search.search(
        placeholder="请输入考核人员名称"
        v-model.trim="keyword"
        @search="onSearch"
        type="search"
        :show-action="!!keyword")
        div(slot="action" @click="onResetSearch" v-if="keyword") 重置
    ListView(
      ref="listView"
      :data="store.records"
      :loading="store.loading"
      :pullup="!store.finish"
      :pulldown="true"
      emptyText="未查询到数据"
      @loadMore="fetchData(store.currentPage + 1)"
      @refresh="fetchData(1)")
      .entries
        .entry(v-for="item in store.records" :key="item.id" @click="show(item)")
          .header.flex-between
            .name {{ item.teacher.name }}
            van-icon.icon(name="arrow")
          KvCell(name="部门" :value="item.teacher.department_name")
          KvCell(name="评分情况")
            span.text-success(v-if="item.access_score.state === 'done'") 已评
            span.text-primary(v-else) 待评
          KvCell(name="总分" :value="`${item.access_score.score} 分`" v-if="item.access_score.score")

  van-action-sheet(
    v-model="filterVisible"
    :actions="actions"
    cancel-text="取消"
    @cancel="filterVisible = false"
    @select="onFilter")
</template>

<style lang="stylus" scoped>
.container
  .list-view-container
    position relative
    padding-top 54px
    height 100%
    .search
      position absolute
      top 0
      left 0
      width 100%
    .entries
      padding 12px
      .entry
        margin-bottom 12px
        padding 14px 16px
        border-radius 4px
        background rgba(255, 255, 255, 1)
        &:last-child
          margin-bottom 0
        .header
          .icon
            color #A6A6A6
          .name
            margin-bottom 6px
            color rgba(56, 56, 56, 1)
            font-weight 500
            font-size 16px
            line-height 24px
</style>
