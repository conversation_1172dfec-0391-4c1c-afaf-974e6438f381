<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import selfEntry, { ISelfEntry, IEntryMeta } from '@/models/assess/selfEntry';
import { IActivity } from '@/models/assess/activity';
import activityStore from '@/store/modules/assess/activity.store';
import questionSet, { IQuestionSet } from '../../../../models/assess/questionSet';
import question, { IQuestion } from '../../../../models/assess/question';
import { baseStore } from '../../../../store/modules/base.store';
import AssessRate from '../../../../components/assess/AssessRate.vue';

const TIMESTAMP = 'ASSESS_SELF_ENTRY_TIMESTAMP';

@Component({
  components: {
    AssessRate,
  },
})
export default class AssessSelfAssess extends Vue {
  activity: IActivity = { meta: {} };
  questionSets: IQuestionSet[] = [];
  formData: IQuestion = {};
  formSetIndex: number = -1;
  formIndex: number = -1;
  formVisible: boolean = false;
  // 必填项
  entryMeta: IEntryMeta = { total: '', work: '', duty: '', question_count: 0, attachments: { documents: [] } };
  timestamp: string = '';
  timer: any = null;

  get activityId() {
    return this.$route.params.id;
  }
  get isDisabled() {
    return this.activity.stage !== 0;
  }
  get loading() {
    return baseStore.loading;
  }

  created() {
    this.timestamp = window.localStorage.getItem(TIMESTAMP) || '';
    this.fetchData();
  }
  async fetchData() {
    const { data } = await selfEntry.findByActivity(this.activityId);
    this.questionSets = (data.exam || []).map(o => ({ ...o, meta: o.meta || {} }));
    this.entryMeta = data.entry_meta || { total: '', work: '', duty: '', question_count: 0 };
    this.entryMeta.attachments = this.entryMeta.attachments || { documents: [] };
    this.fetchActivity(data.access_activity_id!);
  }
  async fetchActivity(activityId: number) {
    await activityStore.find(activityId);
    this.activity = activityStore.stageRecord;
  }
  onNew(set: IQuestionSet, setIndex: number) {
    this.formData = { title: '' };
    this.formSetIndex = setIndex;
    this.formVisible = true;
  }
  async onEdit(q: IQuestion, i: number, setIndex: number) {
    this.formData = { ...q, title: q.title || '' };
    this.formSetIndex = setIndex;
    this.formIndex = i;
    this.formVisible = true;
  }
  resetForm() {
    this.formIndex = -1;
    this.formSetIndex = -1;
    this.formData = {};
    this.formVisible = false;
  }
  async save(set: IQuestionSet, questionIndex: number) {
    if (!this.formData.title) return;
    if (this.formData.id) {
      await question.update(this.formData);
      this.$set(set.questions, questionIndex, this.formData);
    } else {
      const { data } = await question.createByParent(set.id!, this.formData);
      this.$set(set.questions, questionIndex, data);
    }
    this.resetForm();
    this.updateEntryMeta();
  }
  onDelete(q: IQuestion, i: number, setIndex: number) {
    this.$dialog.confirm({
      title: '删除自评项',
      message: '您确定要删除此自评项吗？',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          try {
            await question.delete(q.id!);
            this.$delete(this.questionSets[setIndex].questions, i);
            this.updateEntryMeta();
            done();
          } catch (error) {
            done();
          }
        } else {
          done();
        }
      },
    });
  }
  updateEntryMeta() {
    clearTimeout(this.timer);
    this.timer = setTimeout(async () => {
      // 已填写工作内容数量
      this.entryMeta.question_count = this.questionSets.reduce(
        (ary: IQuestion[], o) => [...ary, ...(o.questions || [])],
        [],
      ).length;
      // 更新 entry_meta
      selfEntry.updateSelf(this.activityId, this.entryMeta);
      this.updateTimestamp();
    }, 600);
  }
  updateTimestamp() {
    const time = this.$dayjs().format('YYYY/MM/DD HH:mm');
    window.localStorage.setItem(TIMESTAMP, time);
    this.timestamp = time;
  }
}
</script>

<template lang="pug">
NavContainer(title="自评")
  .container
    .datetime
      .tag-primary
        span 在
        span  {{ activity.meta.self_start_at }} - {{ activity.meta.self_end_at }}
        span 「自评阶段」内可对自评结果进行修改。
    .catalog
      .header.flex-between
        span 基础信息
        span 必填
      .catalog-content
        form.cell-wrapper(@input="updateEntryMeta" @submit.prevent="")
          .field-label 职务
          van-field.field(v-model="entryMeta.duty" placeholder="请输入你的职务" :disabled="isDisabled")
          .field-label 从事或分管工作
          van-field.field(v-model="entryMeta.work" placeholder="请输入你从事或分管的工作" :disabled="isDisabled")
    .catalog(v-for="(set, setIndex) in questionSets" :key="set.id")
      .header.flex-between
        span {{ set.title }}
        span(v-if="set.meta.max_question_count") {{ set.questions.length }}/{{ set.meta.max_question_count }}
      .catalog-content
        .cell-wrapper(v-for="(question, index) in set.questions" :key="question.id")
          .question(v-loading="loading && formIndex === index")
            template(v-if="formVisible && formData.id === question.id && formSetIndex === setIndex")
              .head.flex-between
                strong {{ index + 1 }}.
                .actions
                  .action.text-primary(@click="save(set, index)")
                    van-icon(name="passed")
                    span 保存
                  .action(@click="resetForm")
                    van-icon(name="close")
                    span 取消
              van-field.field(
                v-model="formData.title"
                rows="3"
                autosize
                autofocus
                type="textarea"
                :maxlength="set.meta.max_letter_count"
                show-word-limit
                placeholder="自评内容")
            template(v-else)
              .head.flex-between
                strong {{ index + 1 }}.
                .actions(v-if="!isDisabled")
                  .action.text-primary(@click="onEdit(question, index, setIndex)")
                    van-icon(name="edit")
                    span 编辑
                  .action(@click="onDelete(question, index, setIndex)")
                    van-icon(name="delete")
                    span 删除
              .title
                | {{ question.title }}
        //- new
        .cell-wrapper.form-new(v-if="formVisible && !formData.id && setIndex === formSetIndex")
          .question
            .head.flex-between
              span {{ set.questions.length + 1 }}.
              .actions
                .action.text-primary(@click="save(set, set.questions.length)")
                  van-icon(name="passed")
                  span 保存
                .action(@click="formVisible = false")
                  van-icon(name="close")
                  span 取消
            van-field.field(
              v-model="formData.title"
              rows="3"
              autosize
              type="textarea"
              autofocus
              :maxlength="set.meta.max_letter_count"
              show-word-limit
              placeholder="自评内容")
        .cell-wrapper(v-if="!isDisabled")
          van-button.question-new(v-if="set.questions.length === set.meta.max_question_count")
            | 最多添加 {{ set.meta.max_question_count }} 条工作记录
          van-button.question-new(@click="onNew(set, setIndex)" icon="plus" :disabled="formVisible" v-else)
            | 添加工作内容

    .self-assess
      .form
        .label.required 总体评价
        AssessRate(v-model="entryMeta.total" @change="updateEntryMeta" v-loading="loading" :disabled="isDisabled")
    .files
      .form
        FileUploader(
          v-model='entryMeta.attachments.documents',
          label='自评附件',
          accept='application/pdf',
          :showActions='true',
          :disabled="formVisible",
          @change='updateEntryMeta'
        )
    .time-footer
      van-icon(name="checked")
      span(v-if="timestamp")
        | 最后保存时间 {{ timestamp }}
      span(v-else)
        | 填写的信息将会自动保存
</template>

<style lang="stylus" scoped>
.container
  overflow auto
  padding-bottom 32px
  height 100%
  background #fff
  -webkit-overflow-scrolling touch
  .datetime
    padding 12px 20px
  .required
    position relative
    &:before
      position absolute
      top 50%
      left -12px
      color #FF4F3E
      content '*'
      font-size 15px
      transform translateY(-50%)
  .self-assess, .files
    padding 0 20px
    .form
      padding 20px 0
      border-top 1px solid #E9E9E9
      .label
        margin-bottom 10px
        color #262626
        font-size 14px
        line-height 20px
  .catalog
    .header
      padding 12px 20px
      background rgba(237, 247, 255, 1)
      color #3DA8F5
      font-size 14px
      line-height 20px
      span
        &:first-child
          padding-right 16px
    .catalog-content
      padding 0 20px
      .cell-wrapper
        padding 24px 0 0
        border-bottom 1px solid #E9E9E9
        &:last-child
          border none
        .field
          margin-bottom 24px
          border 1px solid #CCCCCC
          border-radius 3px
        .field-label
          margin-bottom 8px
          color rgba(128, 128, 128, 1)
          font-size 14px
          line-height 20px
        .question
          .head
            margin-bottom 10px
            .actions
              .action
                display inline-block
                margin-left 20px
                color #808080
                span
                  margin-left 6px
                  vertical-align middle
          .title
            margin-bottom 24px
            color rgba(38, 38, 38, 0.85)
            font-weight 500
            font-size 15px
            line-height 24px
      .question-new
        margin-bottom 24px
        width 100%
        height 36px
        border none
        border-radius 4px
        background rgba(245, 245, 245, 1)
        color #3DA8F5
        text-align center
        font-size 14px
        line-height 36px
  .time-footer
    position fixed
    bottom 0
    left 0
    z-index 1000
    width 100%
    height 32px
    background #F5F5F5
    color #6DC37D
    text-align center
    font-size 14px
    line-height 32px
</style>
