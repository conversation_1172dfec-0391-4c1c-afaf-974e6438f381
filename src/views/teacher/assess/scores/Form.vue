<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IEntryMeta } from '@/models/assess/selfEntry';
import { baseStore } from '../../../../store/modules/base.store';
import questionSet, { IQuestionSet } from '../../../../models/assess/questionSet';
import question, { IQuestion } from '../../../../models/assess/question';
import scoreEntryStore from '../../../../store/modules/assess/scoreEntry.store';
import { IEntry } from '../../../../models/assess/scoreEntry';
import { IActivity } from '../../../../models/assess/activity';
import scoreModel, { IScore } from '../../../../models/assess/score';
import activityStore from '../../../../store/modules/assess/activity.store';

@Component({
  components: {},
})
export default class AssessScoresForm extends Vue {
  activity: IActivity = { meta: {} };
  questions: IQuestion[] = []; // 所有题目的以为数组
  // form
  formVisible: boolean = false;
  metaFormData: IObject = {};
  totalScore: number | string = '';
  submitLoading: boolean = false;
  TOTAL_FIELD_ID: string = 'total_field';
  // 自定义键盘
  keyboardVisible: boolean = false;
  keyboardValue: string = '';
  inputKey: string = ''; // question.id
  inputTitle: string = ''; // keyboard title
  isMoving: boolean = false;

  get scoreEntryId() {
    return +this.$route.params.id;
  }
  get scoreEntry() {
    return scoreEntryStore.record;
  }
  get selfEntryMeta(): IEntryMeta {
    return scoreEntryStore.record.entry_meta || {};
  }
  // 本次活动是否存在自评阶段
  get hasSelfStage() {
    return !!(this.activity.meta || {}).self_start_at;
  }
  // 是否已打分
  get isScored() {
    return this.scoreEntry.access_score && this.scoreEntry.access_score.state === 'done';
  }
  get teacher() {
    return scoreEntryStore.record.teacher || {};
  }
  get questionSets(): IQuestionSet[] {
    return scoreEntryStore.record.exam || [];
  }
  get isDisabled() {
    return this.activity.stage !== 1;
  }
  get loading() {
    return scoreEntryStore.loading;
  }
  // 1. leader: 小项不必填，总分必填
  get isLeaderMode() {
    return this.scoreEntry.score_config === 'leader';
  }
  // 2. manual: 所有项都必填，总分和小项分数独立填写
  get isManualMode() {
    return this.scoreEntry.score_config === 'manual';
  }
  // 3. normal: 所有项都必填，总分禁用，由小项目计算平均分得到
  get isNormalMode() {
    return this.scoreEntry.score_config === 'normal';
  }
  get totalDisabled() {
    return this.activity.stage !== 1 || this.isNormalMode;
  }

  @Watch('metaFormData', { deep: true })
  onMetaFormDataChange() {
    // normal: 总分根据评分项计算总体评价
    if (this.isNormalMode) {
      const items = Object.values(this.metaFormData).filter(Boolean);
      const itemsSum = items.reduce((sum: number, n) => sum + Number(n), 0);
      this.totalScore = Math.round((itemsSum / items.length) * 10) / 10;
    }
  }

  async created() {
    await this.fetchScoreEntry();
    await this.fetchActivity();
  }
  // 考核详情
  async fetchScoreEntry() {
    await scoreEntryStore.find(this.scoreEntryId);
    this.initFormData();
  }
  // 本地活动详情
  async fetchActivity() {
    await activityStore.find(this.scoreEntry.access_activity_id);
    this.activity = activityStore.stageRecord;
  }
  // 初始化表单和键盘
  initFormData() {
    this.questions = [];
    this.questionSets.forEach(o => this.questions.push(...o.questions));
    if (this.isScored) {
      // 初始化上次提交的数据
      this.metaFormData = this.scoreEntry.access_score.score_meta;
      this.totalScore = Number(this.scoreEntry.access_score.score);
      this.formVisible = false;
      this.keyboardVisible = false;
    } else {
      // 第一次打分，初始化 MetaFormData, { id => score }
      this.metaFormData = this.questions.reduce((obj, o) => ({ ...obj, [o.id!]: null }), {});
      this.editForm();
    }
  }
  editForm() {
    this.formVisible = true;
    this.keyboardVisible = true;
    if (this.isLeaderMode) {
      // 领导直接跳到总评部分
      this.initFieldKeyboard(this.TOTAL_FIELD_ID, '总体评价', { duration: 400 });
    } else {
      // 其他跳到第一项开始打分
      const firstQuestion = this.questions[0] || {};
      this.initFieldKeyboard(String(firstQuestion.id), firstQuestion.title!);
    }
  }
  onCancelForm() {
    this.keyboardVisible = false;
    this.formVisible = false;
  }
  submit() {
    if (this.submitLoading) return;

    const scores = Object.values(this.metaFormData);
    const itemsValid = scores.length === this.questions.length && scores.every(o => !!o);
    const totalValid = !!this.totalScore;

    if (this.isLeaderMode) {
      // 1. 领导打分，打总分即可
      if (totalValid) {
        this.updateScore();
      } else {
        this.$toast('请填写总体评价');
      }
    } else if (this.isManualMode) {
      // 2. 手动设置类型，所有项都必填
      if (!itemsValid) {
        this.$toast('请完善打分项');
        return;
      }
      if (!totalValid) {
        this.$toast('请填写总体评价');
        return;
      }
      this.updateScore();
    } else if (this.isNormalMode) {
      // 3. 正常模式，总分为所有项的平均分，都必填，总分禁用
      if (itemsValid) {
        this.updateScore();
      } else {
        this.$toast('请完善打分项');
      }
    }
  }
  async updateScore() {
    try {
      this.submitLoading = true;
      await scoreModel.submitScore(this.scoreEntryId, {
        score: Number(this.totalScore),
        score_meta: this.metaFormData,
      });
      await this.fetchScoreEntry();
      this.submitLoading = false;
      this.formVisible = false;
      if (this.isScored) {
        this.$message.success('修改成功');
      } else {
        this.$message.success('打分成功');
      }
      this.$router.back();
    } catch (error) {
      this.submitLoading = false;
      this.$message.success('打分失败，请稍后重试');
    }
  }
  // 初始化键盘状态
  initFieldKeyboard(key: string, title: string, config: { duration?: number; disabled?: boolean } = { duration: 40 }) {
    if (this.isMoving) return;
    // 如果是 normal 模式，总分禁用
    if (config.disabled) {
      this.keyboardVisible = false;
      return;
    }

    this.keyboardVisible = true;
    if (key === this.TOTAL_FIELD_ID) {
      // 总体评价
      this.keyboardValue = String(this.totalScore || '');
    } else {
      // 考核项
      this.keyboardValue = String(this.metaFormData[key] || '');
    }
    this.inputKey = String(key);
    this.inputTitle = title;
    this.scrollToField(key, config.duration);
  }
  // 键盘点击事件
  onKeyboardInput(newChar: string) {
    if (newChar === '下') {
      const list = this.questions.map(o => `${o.id}`).concat(this.TOTAL_FIELD_ID);
      let nextIndex = list.indexOf(this.inputKey) + 1;
      nextIndex = nextIndex === list.length ? 0 : nextIndex;
      const objs = this.questions
        .map(o => ({ key: `${o.id}`, title: o.title }))
        .concat({ key: this.TOTAL_FIELD_ID, title: '总体评价' });
      const activeOption = objs[nextIndex] || {};
      this.initFieldKeyboard(activeOption.key, activeOption.title!, {
        disabled: this.isNormalMode && activeOption.key === this.TOTAL_FIELD_ID,
      });
      return;
    }

    // 获取输入分数
    const newValue = `${this.keyboardValue}${newChar}`;
    // 越界处理：== 0
    if (newValue === '0') {
      this.keyboardValue = '';
      return;
    }
    // 越界处理：> 100
    if (Number(newValue) > 100) {
      this.keyboardValue = '100';
      if (this.inputKey === this.TOTAL_FIELD_ID) {
        this.totalScore = 100;
      } else {
        this.metaFormData[this.inputKey] = 100;
      }
      return;
    }
    // <= 100, 合法，计算分值
    this.keyboardValue = newValue;
    if (this.inputKey === this.TOTAL_FIELD_ID) {
      this.totalScore = +this.keyboardValue;
    } else {
      this.metaFormData[this.inputKey] = +this.keyboardValue;
    }
  }
  // 键盘删除字符事件
  onKeyboardDelete() {
    this.keyboardValue = String(this.keyboardValue).slice(0, String(this.keyboardValue).length - 1);
    if (this.inputKey === this.TOTAL_FIELD_ID) {
      this.totalScore = this.keyboardValue ? +this.keyboardValue : '';
    } else {
      this.metaFormData[this.inputKey] = this.keyboardValue ? +this.keyboardValue : '';
    }
  }
  // 键盘完成时间
  onKeyboardComplete() {
    this.keyboardVisible = false;
    this.$nextTick(() => {
      this.scrollToField('footer');
    });
  }
  resetKeyboard() {
    this.inputKey = '';
    this.inputTitle = '';
  }
  scrollToField(id: string, duration: number = 20) {
    setTimeout(() => {
      const dom: HTMLElement | null = document.getElementById(id);
      if (dom) {
        dom.scrollIntoView(false);
      }
    }, duration);
  }
}
</script>

<template lang="pug">
NavContainer(:title="teacher.name")
  .container(:class="{ 'keyboard-container': keyboardVisible }")
    .list-view.scroll-y(v-loading="loading")
      .base-info
        .teacher
          .name {{ teacher.name }}
          .item(v-if="hasSelfStage")
            label 总体自评：
            span {{ selfEntryMeta.total || '未自评' }}
          .item
            label 工号：
            span {{ teacher.code }}
          .item
            label 部门：
            span {{ teacher.department_name }}
          .item(v-if="hasSelfStage")
            label 职务：
            span {{ selfEntryMeta.duty || '未填写' }}
          .item(v-if="hasSelfStage")
            label 从事或分管工作：
            span {{ selfEntryMeta.work || '未填写' }}
        .files
          Attachments(:attachments='selfEntryMeta.attachments ? selfEntryMeta.attachments.documents : []')
        .datetime
          .tag-primary
            span 在
            span  {{ activity.meta.assessment_start_at }} - {{ activity.meta.assessment_end_at }}
            span 「考核阶段」内可对考核结果进行修改。
      .question-set(v-for="set in questionSets" :key="set.id")
        .header.flex-between
          span {{ set.title }}
        .questions
          .empty(v-if="(set.questions || []).length === 0")
            | 无评分项
          .cell-wrapper(v-for="(question, index) in (set.questions || [])" :key="question.id" :id="question.id")
            .question(:class="{ 'question-actived': inputKey === `${question.id}` }")
              .head(:class="{ required: !isLeaderMode }") {{ index + 1 }}. {{ question.title }}
              van-field.field(
                v-if="formVisible"
                :value="metaFormData[question.id]"
                placeholder="分值，满分 100"
                :disabled="isDisabled"
                type="number"
                readonly
                :min="0"
                :max="100"
                :step="1"
                @touchstart.native.stop="isMoving = false"
                @touchmove.native.stop="isMoving = true"
                @touchend.native.stop="initFieldKeyboard(question.id, question.title, { disabled: isDisabled })")
              .score(v-else)
                | 分值：{{ metaFormData[question.id] || '-' }} 分
      .question-set
        .header.flex-between
          span 总体评价
          strong.text-primary 必填
        .questions
          .cell-wrapper(:id="TOTAL_FIELD_ID")
            .question(:class="{ 'question-actived': inputKey === TOTAL_FIELD_ID && !isNormalMode }")
              van-field.field(
                v-if="formVisible"
                :value="totalScore"
                placeholder="分值，满分 100"
                :disabled="totalDisabled"
                type="number"
                readonly
                :min="0"
                :max="100"
                :step="1"
                @touchstart.native.stop="isMoving = false"
                @touchmove.native.stop="isMoving = true"
                @touchend.native.stop="initFieldKeyboard(TOTAL_FIELD_ID, '总体评价', { disabled: totalDisabled })")
              .score(v-else)
                | 分值：{{ totalScore }} 分
      //- 编辑
      .footer.flex-between(v-if="isScored" id="footer")
        template(v-if="formVisible")
          van-button.submit-button(
            size="large"
            :disabled="isDisabled"
            @click="onCancelForm")
            | 取消修改
          van-button.submit-button(
            type="info"
            size="large"
            loading-text="正在修改..."
            :loading="submitLoading"
            :disabled="isDisabled || submitLoading"
            @click="submit")
            | 确认修改
        van-button.submit-button(
          v-else
          type="info"
          size="large"
          :disabled="isDisabled"
          @click="editForm")
          | 修改考核
      //- 新建
      .footer.flex-between(v-else)
        van-button.submit-button(
          type="info"
          size="large"
          :loading="submitLoading"
          :disabled="isDisabled || submitLoading"
          loading-text="正在提交..."
          @click="submit")
          | 提交

    van-number-keyboard(
      :value="keyboardValue"
      :show="keyboardVisible"
      :class="{ keyboard: keyboardVisible }"
      theme="custom"
      :z-index="9999"
      close-button-text="完成"
      :extra-key="inputKey === TOTAL_FIELD_ID ? '' : '下一项'"
      @input="onKeyboardInput"
      @delete="onKeyboardDelete"
      @hide="resetKeyboard"
      @close="onKeyboardComplete")
</template>

<style lang="stylus" scoped>
.container
  display flex
  flex-direction column
  height 100%
  .list-view
    flex-grow 1
    background #fff
    transition height 0.3s ease-out
    scroll-behavior smooth
    .base-info
      padding 12px 12px 16px
      .teacher
        margin-bottom 16px
        padding 16px
        border-radius 4px
        background rgba(100, 185, 245, 1)
        color rgba(255, 255, 255, 1)
        line-height 20px
        .name
          margin-bottom 12px
          font-weight 500
          font-size 17px
        .item
          margin-top 6px
          font-size 15px
          label
            white-space nowrap
      .files
        margin-bottom 20px
    .question-set
      .header
        padding 12px 20px
        background rgba(237, 247, 255, 1)
        color #3DA8F5
        font-size 14px
        line-height 20px
      .questions
        padding 0 20px
        .empty
          padding 24px 0
          text-align center
        .cell-wrapper
          padding 24px 0
          border-bottom 1px solid #E9E9E9
          &:last-child
            border-bottom none
        .question
          .head
            margin-bottom 12px
            color rgba(38, 38, 38, 0.85)
            font-weight 500
            font-size 15px
            line-height 24px
          .required
            position relative
            &:before
              position absolute
              top 13px
              left -12px
              color #FF4F3E
              content '*'
              font-size 15px
              transform translateY(-50%)
          .field
            border 1px solid #CCCCCC
            border-radius 3px
          .score
            color rgba(61, 168, 245, 1)
            font-weight 500
            font-size 15px
            line-height 22px
        .question-actived
          .field
            border 1px solid #3DA8F5
    .footer
      margin-top 20px
      width 100%
      height 48px
      .submit-button
        width 100%
        height 48px
        border-radius 0px
        font-size 16px
        line-height 44px
  .keyboard
    position relative
    flex-shrink 0
    box-shadow 0px 0px 8px 0 rgba(0, 0, 0, 0.2)
</style>
