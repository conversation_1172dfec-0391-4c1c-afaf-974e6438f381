<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import assessActivityStore from '@/store/modules/assess/activity.store';
import { IActivity } from '../../../../models/assess/activity';

@Component({
  components: {},
})
export default class AssessActivities extends Vue {
  get store() {
    return assessActivityStore;
  }

  created() {
    this.store.SET_RECORDS({ current_page: 0 });
    this.fetchData(1);
  }

  async fetchData(page: number = 1) {
    await this.store.fetch({ page, q: { state_eq: 'published', s: ['created desc'] } });
  }
  getActivityRole(activity: IActivity) {
    const roles = [];
    if (activity.score) roles.push('考核人');
    if (activity.entry) roles.push('被考核人');
    if (!roles.length) roles.push('无操作权限');
    return roles.join('、');
  }
}
</script>

<template lang="pug">
NavContainer.container(title="人事考核")
  ListView(
    :data="store.stageRecords"
    :loading="store.loading"
    :pullup="!store.finish"
    :pulldown="true"
    emptyText="暂无考核"
    @loadMore="fetchData(store.currentPage + 1)"
    @refresh="fetchData(1)")
    .activities
      .activity(v-for="item in store.stageRecords" :key="item.id")
        .title {{ item.name }}
        KvCell.cell(name="身份" :value="getActivityRole(item)")
        KvCell.cell(
          v-if="item.stage === 1 && item.score"
          name="被考核人"
          :value="`${item.total_entry_count} 人`")
        KvCell.cell(name="考核内容" :value="item.body")
        van-steps.steps(direction="vertical" :active="item.stage" active-color="#3DA8F5" active-icon="clock")
          van-step
            .step-name 自评阶段
            .step-time(v-if="item.meta.self_start_at") {{ item.meta.self_start_at }} - {{ item.meta.self_end_at }}
            .step-time(v-else) 无
          van-step
            .step-name 考核阶段
            .step-time {{ item.meta.assessment_start_at }} - {{ item.meta.assessment_end_at }}
          van-step
            .step-name 统计阶段
            .step-time {{ item.meta.statistics_start_at }} - {{ item.meta.statistics_end_at }}
        .footer.flex-between
          .actions
            template(v-if="item.isSelf && item.entry")
              van-button(
                v-if="item.entry_meta.total && item.entry_meta.question_count"
                :to="`/assess/activities/${item.id}/self_assess`"
                type="info")
                | 查看自评
              van-button(
                v-else
                :to="`/assess/activities/${item.id}/self_assess`"
                type="info")
                | 自评
            template(v-if="item.isAssessment && item.score")
              van-button(
                v-if="item.total_entry_count === item.score_count"
                :to="`/assess/activities/${item.id}/score_entries`"
                type="info")
                | 查看考核
              van-button(
                v-else
                :to="`/assess/activities/${item.id}/score_entries`"
                type="info")
                | 考核
          .tags
            van-tag.tag(size="large" :class="`tag-${item.tag.type}`")
              van-icon(name="info")
              span {{ item.tag.text }}
</template>

<style lang="stylus" scoped>
.container
  .activities
    position relative
    padding 12px
    .activity
      margin-bottom 12px
      padding 16px 16px 0
      border-radius 4px
      background rgba(255, 255, 255, 1)
      &:last-child
        margin-bottom 0
      .title
        margin-bottom 10px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 16px
        line-height 24px
      .cell
        margin-bottom 8px
      .steps
        padding-left 24px
        .step-name
          color #333
          font-size 12px
        .step-time
          color #A6A6A6
          font-size 12px
      .footer
        padding 12px 0
        border-top 1px solid #E8E8E8
        .actions
          button
            height 40px
            line-height 40px
        .tags
          .tag
            i
              vertical-align middle
            span
              margin-left 4px
              vertical-align middle
</style>
