<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import <PERSON><PERSON><PERSON> from '@/components/teaching/CheckChart.vue';
import lessonModel, { ILesson } from '@/models/teaching/lesson';

@Component({
  components: {
    Check<PERSON><PERSON>,
  },
})
export default class TeacherLessonsEvaluation extends Vue {
  lesson: ILesson = {};
  loading: boolean = false;

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get evaluationStat() {
    return this.lesson.evaluation_stat || { count: 0, todo: 0, done: 0 };
  }
  get chartData() {
    const { count = 1, done = 0, todo = 0 } = this.evaluationStat;
    return [
      { name: '已评价', count: done, percent: Math.round((done / count) * 100) },
      { name: '未评价', count: todo, percent: Math.round((todo / count) * 100) },
    ];
  }
  get questionInfos() {
    return (this.lesson.evaluation_answer_statistic || { question_infos: [] }).question_infos;
  }

  created() {
    this.fetchLesson();
  }
  async fetchLesson() {
    try {
      this.loading = true;
      const { data } = await lessonModel.find(this.lessonId);
      this.lesson = data;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  getQuestionInfoData(statInfo: any) {
    const total = statInfo.reduce((sum: number, o: any) => sum + o.count, 0);
    return statInfo.map((o: any) => ({
      name: o.value,
      count: o.count,
      percent: Math.round((o.count / total) * 100),
    }));
  }
}
</script>

<template lang="pug">
NavContainer.container(title="评价详情")
  .content-box
    .counts
      .count-item
        .name 已评价
        .count
          span {{ evaluationStat.done }}
          sub 人
      .count-item
        .name 未评价
        .count
          span {{ evaluationStat.todo }}
          sub 人
    .panel
      .title 评价情况
      .content
        CheckChart(:data="chartData" unit="人")
    .panel(v-for="(questionInfo, index) in questionInfos" :key="index")
      .title {{ questionInfo.title }}
      .content
        CheckChart(:data="getQuestionInfoData(questionInfo.stat_info)" unit="票")
</template>

<style lang="stylus" scoped>
.container
  height 100%
  background #fff
  .content-box
    padding 12px
    .panel
      background rgba(255,255,255,1)
      border-radius 3px
      border 1px solid rgba(232,232,232,1)
      margin-bottom 12px
      .title
        font-size 14px
        color rgba(56,56,56,1)
        line-height 20px
        padding 12px
        border-bottom 1px solid #E8E8E8
      .content
        paddding 10px
    .counts
      display flex
      justify-content space-between
      align-items center
      width 160px
      margin 30px auto
      .count-item
        .name
          margin 0 0 8px 0
          color rgba(166, 166, 166, 1)
          font-weight 500
          font-size 12px
          line-height 14px
        .count
          span
            display inline-block
            height 30px
            color rgba(56, 56, 56, 1)
            font-weight 500
            font-size 30px
            font-family 'DINCond'
            line-height 30px
          sub
            display inline-block
            margin-left 4px
            height 18px
            color rgba(128, 128, 128, 1)
            vertical-align 4px
            font-weight 400
            font-size 12px
            font-family PingFang SC
            line-height 18px
</style>
