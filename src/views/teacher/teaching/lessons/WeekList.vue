<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ILesson } from '@/models/teaching/lesson';
import WeekScheduleContainer from './WeekScheduleContainer.vue';

@Component({
  components: {
    WeekScheduleContainer,
  },
})
export default class TeacherLessonsWeekList extends Vue {
  onShow(lesson: ILesson) {
    this.$router.push(`/teaching/teacher/lessons/${lesson.id}`);
  }
}
</script>

<template lang="pug">
WeekScheduleContainer
  template(#default="{ lessons }")
    Empty(v-if="lessons.length === 0" desc="无课程")
    .lessons
      .lesson(
        v-for="lesson in lessons"
        :key="lesson.id"
        @click="onShow(lesson)")
        .name {{ lesson.course_set_name }}
        .item
          label 时间
          .gap-box
            span {{ lesson.date }}
            span {{ lesson.start_time }} - {{ lesson.end_time }}
            span {{ $utils.weekDay(lesson.weekday) }}
        .item
          label 节数
          span {{ lesson.start_unit }} - {{ lesson.end_unit }}（共 {{ lesson.unit_count }} 节）
        .item
          label 教室
          span {{ lesson.classroom_name }}
        .item
          label 班级
          span {{ lesson.course_name }}
        .item
          label 人数
          span {{ lesson.course_std_count }} 人
        .statistic
          .count-info
            div 签到情况
            .count
              span.text-success {{ lesson.register_count ? lesson.register_count.done : '-' }}/
              span.text-warning {{ lesson.register_count ? lesson.register_count.late : '-' }}/
              span.text-gray {{ lesson.register_count ? lesson.register_count.undo : '-' }}
          .count-info
            div 已评价
            .count.text-primary
              | {{ lesson.evaluation_count }} / {{ lesson.course_std_count }}
          .count-info
            div 作业
            .count(v-if="lesson.report_count && lesson.report_count.submited >= 0")
              span.text-success {{ lesson.report_count.submited }}/
              span.text-gray {{ lesson.course_std_count }}
            .count.text-gray(style="font-size: 14px" v-else)
              | 未布置作业
</template>

<style lang="stylus" scoped>
.lessons
  padding 12px 12px 1px
  .lesson
    margin-bottom 12px
    padding 16px
    border-radius 4px
    background #fff
    cursor pointer
    .name
      margin-bottom 10px
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 16px
      line-height 20px
    .item
      display flex
      margin-bottom 8px
      height 20px
      color rgba(128, 128, 128, 1)
      font-size 14px
      line-height 20px
      label
        width 58px
      .gap-box
        display inline-block
        span
          margin-right 6px
    .statistic
      display flex
      margin-top 8px
      padding-top 12px
      border-top 1px solid #E8E8E8
      .count-info
        flex-grow 1
        color rgba(56, 56, 56, 1)
        font-weight 400
        font-size 14px
        line-height 20px
        .count
          margin-top 4px
          height 25px
          font-weight 500
          font-size 18px
          line-height 25px
</style>
