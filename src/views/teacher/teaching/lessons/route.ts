import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/teaching/teacher/lessons/:lessonId/registers',
    name: 'teaching_teacher_lessons_registers',
    component: () => import(/* webpackChunkName: "teaching_teacher_lessons_registers" */ './Registers.vue'),
    meta: {
      title: '签到情况',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/teaching/teacher/lessons/:lessonId/lesson_plans',
    name: 'teaching_teacher_lesson_plans',
    component: () => import(/* webpackChunkName: "teaching_teacher_lessons_show" */ './LessonPlans.vue'),
    meta: {
      title: '课程教学平台',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/teaching/teacher/lessons/:lessonId/lesson_items/:lessonItemId/recorders',
    name: 'teaching_teacher_lesson_item_recorders',
    component: () =>
      import(/* webpackChunkName: "teaching_teacher_lesson_item_recorders" */ './LessonItemRecorders.vue'),
    meta: {
      title: '课时学习情况',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/teaching/teacher/lessons/:lessonId/evaluation',
    name: 'teaching_teacher_lessons_evaluation',
    component: () => import(/* webpackChunkName: "teaching_teacher_lessons_evaluation" */ './Evaluation.vue'),
    meta: {
      title: '评价详情',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/teaching/teacher/lessons/:lessonId/recorders',
    name: 'teaching_teacher_lessons_recorders',
    component: () => import(/* webpackChunkName: "teaching_teacher_lessons_recorders" */ './Recorders.vue'),
    meta: {
      title: '课程学习情况',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/teaching/teacher/lessons/:lessonId',
    name: 'teaching_teacher_lessons_show',
    component: () => import(/* webpackChunkName: "teaching_teacher_lessons_show" */ './Show.vue'),
    meta: {
      title: '课程详情',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/teaching/teacher',
    name: 'teaching_teacher_lessons_entry',
    component: () => import(/* webpackChunkName: "teaching_teacher_lessons_index" */ './Entry.vue'),
    meta: {
      title: '课程教学平台',
      requireAuth: true,
      roles: ['Teacher'],
    },
    children: [
      {
        path: 'activities',
        name: 'teaching_teacher_activities_index',
        component: () => import(/* webpackChunkName: "teaching_teacher_lessons_index" */ './Activities.vue'),
        meta: {
          title: '学期课表',
          requireAuth: true,
          roles: ['Teacher'],
        },
      },
      {
        path: 'lessons',
        name: 'teaching_teacher_lessons_index',
        component: () => import(/* webpackChunkName: "teaching_teacher_lessons_index" */ './Lessons.vue'),
        meta: {
          title: '学期课表',
          requireAuth: true,
          roles: ['Teacher'],
        },
      },
      {
        path: 'week_list',
        name: 'teaching_teacher_week_list',
        component: () => import(/* webpackChunkName: "teaching_teacher_week_list" */ './WeekList.vue'),
        meta: {
          title: '列表视图',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
      {
        path: 'week_schedule',
        name: 'teaching_teacher_week_schedule',
        component: () => import(/* webpackChunkName: "teaching_teacher_week_schedule" */ './WeekSchedule.vue'),
        meta: {
          title: '日历视图',
          requireAuth: true,
          roles: ['Teacher'],
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
