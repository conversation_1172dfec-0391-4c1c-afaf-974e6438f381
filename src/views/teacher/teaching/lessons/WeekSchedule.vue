<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ILesson } from '@/models/teaching/lesson';
import WeekScheduleContainer from './WeekScheduleContainer.vue';

@Component({
  components: {
    WeekScheduleContainer,
  },
})
export default class TeacherLessonsWeekSchedule extends Vue {
  onShow(lesson: ILesson) {
    this.$router.push(`/teaching/teacher/lessons/${lesson.id}`);
  }
}
</script>

<template lang="pug">
WeekScheduleContainer(@show="onShow")
</template>

<style lang="stylus" scoped></style>
