<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import session from '@/models/session';

@Component
export default class TeachingTeacherLessonsEntry extends Vue {
  get activeIndex() {
    return ['/teaching/teacher/activities', '/teaching/teacher/week_schedule', '/teaching/teacher/week_list'].indexOf(
      this.$route.path,
    );
  }
}
</script>

<template lang="pug">
.lessons-container
  router-view
  van-tabbar.tabbar(:value="activeIndex" fixed safe-area-inset-bottom :zIndex="1000")
    van-tabbar-item(icon="notes-o" to="/teaching/teacher/activities" :replace="true") 学期课表
    van-tabbar-item(icon="calender-o" to="/teaching/teacher/week_schedule" :replace="true") 日历视图
    van-tabbar-item(icon="orders-o" to="/teaching/teacher/week_list" :replace="true") 列表视图
</template>

<style lang="stylus" scoped>
.lessons-container
  padding 0px 0px 50px
  height 100vh
  position relative
  overflow auto
  -webkit-overflow-scrolling touch
</style>
