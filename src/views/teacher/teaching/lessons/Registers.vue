<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import <PERSON><PERSON><PERSON> from '@/components/teaching/CheckChart.vue';
import lessonModel, { ILesson } from '../../../../models/teaching/lesson';
import teacherRegister, { IRegister } from '../../../../models/teaching/teacherRegister';

@Component({
  components: {
    CheckChart,
  },
})
export default class TeacherRegisterList extends Vue {
  registers: IRegister[] = [];
  lesson: ILesson = {};
  loading: boolean = false;
  timer: any = null;

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get registerCount() {
    return this.lesson.register_count || { done: 0, late: 0, undo: 0 };
  }
  get chartData() {
    const { course_std_count = 0 } = this.lesson;
    const { done = 0, late = 0, undo = 0 } = this.registerCount;
    return [
      { name: '已签到', count: done, percent: Math.round((done / course_std_count) * 100) },
      { name: '迟到', count: late, percent: Math.round((late / course_std_count) * 100) },
      { name: '未签到', count: undo, percent: Math.round((undo / course_std_count) * 100) },
    ];
  }

  created() {
    this.fetchData();
    this.timer = setInterval(this.fetchData, 6000);
  }
  beforeDestroy() {
    clearInterval(this.timer);
  }
  fetchData() {
    this.fetchLesson();
    this.fetchRegisters();
  }
  async fetchLesson() {
    try {
      this.loading = true;
      const { data } = await lessonModel.find(this.lessonId);
      this.lesson = data;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  async fetchRegisters() {
    try {
      this.loading = true;
      const { data } = await teacherRegister.indexByParent(this.lessonId, {
        page: 1,
        per_page: 1000,
      });
      this.registers = data.registers;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  getTagState(register: IRegister) {
    return {
      'tag-success': register.state === 'done',
      'tag-warning': register.state === 'late',
      tag: register.state === 'undo',
    };
  }
}
</script>

<template lang="pug">
NavContainer.check-statistic(title="签到情况" )
  .statistic
    .chart
      CheckChart(:data="chartData" unit="人" theme="green")
    .counts
      .count-item
        .name 总人数
        .count
          span {{ lesson.course_std_count }}
          sub 人
      .count-item
        .name 已签到
        .count
          span {{ registerCount.done }}
          sub 人
      .count-item
        .name 迟到
        .count
          span {{ registerCount.late }}
          sub 人
      .count-item
        .name 未签到
        .count
          span {{ registerCount.undo }}
          sub 人
    .check-list
      .empty(v-if="!loading && registers.length === 0" desc="本课程还未运行过签到功能")
      .item(v-for="register in registers" :key="register.id")
        .tag(:class="getTagState(register)")
          | {{ register.student.name }}
</template>

<style lang="stylus" scoped>
.check-statistic
  height 100%
  background #fff
  .statistic
    overflow hidden
    padding 20px
    .counts
      display flex
      justify-content space-between
      align-items center
      padding 0 18px 30px
      .count-item
        .name
          margin 0 0 8px 0
          color rgba(166, 166, 166, 1)
          font-weight 500
          font-size 12px
          line-height 14px
        .count
          span
            display inline-block
            height 30px
            color rgba(56, 56, 56, 1)
            font-weight 500
            font-size 30px
            font-family 'DINCond'
            line-height 30px
          sub
            display inline-block
            margin-left 4px
            height 18px
            color rgba(128, 128, 128, 1)
            vertical-align 4px
            font-weight 400
            font-size 12px
            font-family PingFang SC
            line-height 18px
    .check-list
      display flex
      flex-wrap wrap
      overflow hidden
      margin -2px
      padding-bottom 30px
      width 100%
      .item
        flex-shrink 0
        padding 2px
        width 25%
        .tag
          padding 10px
          width 100%
          text-align center
          font-weight bold
</style>
