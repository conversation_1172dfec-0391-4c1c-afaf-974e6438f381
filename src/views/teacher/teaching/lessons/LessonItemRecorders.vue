<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { RecorderService, IRecorder, SourceType } from '@/service/recorder';
import lessonModel, { ILesson } from '@/models/teaching/lesson';

@Component({
  components: {},
})
export default class TeacherLessonStudentRecorders extends Vue {
  lesson: ILesson = {};
  page: number = 1;
  pageSize: number = 15;
  totalPages: number = 1;
  totalCount: number = 0;
  recorders: IRecorder[] = [];
  loading: boolean = false;

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get courseId() {
    return +this.lesson.course_id!;
  }
  get lessonItemId() {
    return +this.$route.params.lessonItemId;
  }

  mounted() {
    this.fetchLesson();
  }

  async fetchLesson() {
    try {
      this.loading = true;
      const { data } = await lessonModel.setRole('teacher/inspect').find(this.lessonId!);
      this.lesson = data;
      this.loading = false;
      this.fetchRecords(1);
    } catch (error) {
      this.loading = false;
    }
  }

  async fetchRecords(page: number = this.page) {
    this.loading = true;
    const { data } = await RecorderService.teacher
      .fetchStudentRecorders({
        sourceType: SourceType.LessonItem,
        sourceId: this.lessonItemId,
        courseId: this.courseId,
        lessonId: this.lessonId,
        params: {
          page,
          per_page: 15,
          q: {},
        },
      })
      .finally(() => {
        this.loading = false;
      });
    this.recorders = data.recorders;
    this.page = page;
    this.totalPages = data.total_pages;
    this.totalCount = data.total_count;
  }
  getTimeLength(seconds?: number) {
    if (seconds) {
      return this.$utils.parseSeconds(seconds);
    }
    return '-';
  }
}
</script>

<template lang="pug">
NavContainer.container(title="课时学习情况")
  ListView(
    :data="recorders"
    :loading="loading"
    :pullup="page >= totalPages"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchRecords(page + 1)"
    @refresh="fetchRecords(1)")
    .recorder(v-for="recorder in recorders" :key="recorder.id")
      .content
        KvCell(name="姓名") {{ recorder.user.name }}
        KvCell(name="学号") {{ recorder.user.code }}
        KvCell(name="课前") {{ getTimeLength(recorder.prepare_in_sec) }}
        KvCell(name="课中") {{ getTimeLength(recorder.study_in_sec) }}
        KvCell(name="课后") {{ getTimeLength(recorder.review_in_sec) }}
        KvCell(name="总计") {{ getTimeLength(recorder.total_in_sec) }}
</template>

<style lang="stylus" scoped>
.container
  height 100%
  .recorder
    padding 12px 12px 0
    .content
      background #fff
      padding 12px
</style>
