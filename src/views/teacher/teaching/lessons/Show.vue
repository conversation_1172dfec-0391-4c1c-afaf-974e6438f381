<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import lessonModel, { ILesson } from '../../../../models/teaching/lesson';
import CheckChart from '../../../../components/teaching/CheckChart.vue';

@Component({
  components: {
    CheckChart,
  },
})
export default class TeacherLessonsShow extends Vue {
  lesson: ILesson = {
    evaluation_stat: {
      todo: 0,
      done: 0,
      count: 0,
      doing: 0,
    },
  };
  loading: boolean = false;

  get registerCount() {
    return this.lesson.register_stat || { done: 0, late: 0, undo: 0 };
  }
  get chartData() {
    const { course_std_count = 0 } = this.lesson;
    const { done = 0, late = 0, undo = 0 } = this.registerCount;
    return [
      { name: '已签到', count: done, percent: Math.round((done / course_std_count) * 100) },
      { name: '迟到', count: late, percent: Math.round((late / course_std_count) * 100) },
      { name: '未签到', count: undo, percent: Math.round((undo / course_std_count) * 100) },
    ];
  }

  created() {
    this.fetchLesson();
  }
  async fetchLesson() {
    try {
      this.loading = true;
      const { data } = await lessonModel.setRole('teacher/inspect').find(this.$route.params.lessonId!);
      this.lesson = data;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
}
</script>

<template lang="pug">
.container.scoll-y(v-loading="loading")
  .content
    .name {{ lesson.course_set_name }}
    van-cell(title="时间" icon="clock-o" :value="`${lesson.date} ${lesson.start_time}-${lesson.end_time}`")
    van-cell(title="节数" icon="location-o" :value="`${lesson.unit_count} 节`")
    van-cell(title="教室" icon="wap-home-o" :value="lesson.classroom_name")
    van-cell(title="班级" icon="home-o" :value="lesson.course_name")
    van-cell(title="人数" icon="friends-o" :value="`${lesson.course_std_count}人`")
    van-cell(title="教案" icon="bookmark-o" value="查看" is-link
      :to="`/teaching/teacher/lessons/${lesson.id}/lesson_plans`")
    van-cell(title="签到情况" icon="sign" is-link :to="`/teaching/teacher/lessons/${lesson.id}/registers`")
    CheckChart(:data="chartData" unit="人" theme="green")
    van-cell(
      title="评价"
      icon="comment-o"
      :to="`/teaching/teacher/lessons/${lesson.id}/evaluation`"
      :value="`${lesson.evaluation_stat.done || '-'}/${lesson.course_std_count}人`" is-link)
    van-cell(
      title="学习情况"
      icon="bar-chart-o"
      :to="`/teaching/teacher/lessons/${lesson.id}/recorders`"
      is-link)
</template>

<style lang="stylus" scoped>
.container
  height 100vh
  background #fff
  .content
    padding 10px 0 30px
    .name
      font-size 19px
      color rgba(56,56,56,1)
      line-height 28px
      padding 8px 16px
</style>
