<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import LessonPlansDetail from '@/components/teaching/LessonPlansDetail.vue';
import { LessonPlan, ILessonPlan } from '@/models/teaching/lesson_plan';

@Component({
  components: {
    LessonPlansDetail,
  },
})
export default class TeacherLessonPlans extends Vue {
  lessonPlans: ILessonPlan[] = [];
  loading: boolean = false;

  get lessonId() {
    return +this.$route.params.lessonId;
  }

  mounted() {
    this.fetchLessonPlans();
  }
  async fetchLessonPlans() {
    try {
      this.loading = true;
      const lessonPlanModel = new LessonPlan('teacher/inspect');
      lessonPlanModel.setConfig({ parentResource: 'lessons' });
      const { data } = await lessonPlanModel.indexByParent(this.lessonId, {
        page: 1,
        per_page: 1000,
      });
      this.lessonPlans = data.lesson_plans;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  onShowItem(lessonItem: any) {
    this.$router.push(`/teaching/teacher/lessons/${this.lessonId}/lesson_items/${lessonItem.id}/recorders`);
  }
}
</script>

<template lang="pug">
NavContainer.container(title="课程资源")
  LessonPlansDetail(
    :lessonPlans="lessonPlans"
    :showRecorderEntry="true"
    @showItem="onShowItem"
    v-loading="loading")
</template>

<style lang="stylus" scoped>
.lesson-plans
  height 100%
</style>
