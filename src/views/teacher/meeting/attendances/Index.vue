<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import attendanceStore from '@/store/modules/meeting/attendance.store';
import AttendanceCell from '../../../../components/meeting/AttendanceCell.vue';
import { IAttendance, AttendanceStates } from '../../../../models/meeting/attendance';

@Component({
  components: {
    AttendanceCell,
  },
})
export default class MeetingAttendances extends Vue {
  activeState: string = 'success';
  tabs: object[] = [
    { text: '报名成功', key: 'success' },
    { text: '待审批', key: '待审批' },
    { text: '未通过', key: '已取消' },
    { text: '历史报名', key: 'history' },
  ];

  get attendanceStore() {
    return attendanceStore;
  }
  get AttendanceStates() {
    return AttendanceStates;
  }

  mounted() {
    this.attendanceStore.SET_RECORDS({ current_page: 0 });
    this.fetchData(1);
  }

  async fetchData(page: number = 1) {
    const qs: IObject = {};
    if (this.activeState === 'success') {
      qs.meeting_activity_end_time_gt = new Date();
      qs.state_in = [AttendanceStates.signed, AttendanceStates.checked];
    } else if (this.activeState === 'history') {
      qs.meeting_activity_end_time_lt = new Date();
    } else {
      qs.state_eq = this.activeState;
    }
    await this.attendanceStore.fetch({
      page,
      q: {
        ...qs,
      },
    });
  }

  async onCancel(record: IAttendance) {
    try {
      await attendanceStore.cancel(record.id!);
      this.$message.success('取消成功');
    } catch (error) {
      this.$message.error('取消失败');
    }
  }

  onStateChange() {
    this.fetchData(1);
  }
}
</script>

<template lang="pug">
NavContainer(title="我报名的")
  //- van-icon(slot="right" name="search")
  .container
    Tabs.tabs(v-model="activeState" :tabs="tabs" @change="onStateChange")
    ListView(
      :data="attendanceStore.records"
      :loading="attendanceStore.loading"
      :pullup="!attendanceStore.finish"
      :pulldown="true"
      emptyText="暂无报名记录"
      @loadMore="fetchData(attendanceStore.currentPage + 1)"
      @refresh="fetchData(1)")
      .attendances
        AttendanceCell(
          v-for="item in attendanceStore.records"
          :key="item.id"
          :attendance="item")
    van-tabbar.tabbar(:value="1" fixed safe-area-inset-bottom)
      van-tabbar-item(icon="flag-o" to="/meeting/activities" :replace="true") 全部活动
      van-tabbar-item(icon="orders-o" to="/meeting/attendances" :replace="true") 我报名的
      van-tabbar-item(icon="qr" to="/meeting/qrcode" :replace="true") 二维码
</template>

<style lang="stylus" scoped>
.container
  position relative
  padding-top 46px
  padding-bottom 50px
  height 100%
  .tabs
    position absolute
    top 0
    left 0
    width 100%
    z-index 10
  .attendances
    padding 12px 12px 1px
</style>
