<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import session from '@/models/session';
import IDQrCode from '@/components/session/IDQrCode.vue';

@Component({
  components: {
    IDQrCode,
  },
})
export default class MeetingMyQrCode extends Vue {}
</script>

<template lang="pug">
NavContainer(title="我的二维码")
  .container
    .card
      IDQrCode.qrcode
      .tips
        van-icon(name="scan")
        span 对准扫码枪进行签到
    van-tabbar.tabbar(:value="2" fixed safe-area-inset-bottom)
      van-tabbar-item(icon="flag-o" to="/meeting/activities" :replace="true") 全部活动
      van-tabbar-item(icon="orders-o" to="/meeting/attendances" :replace="true") 我报名的
      van-tabbar-item(icon="qr" to="/meeting/qrcode" :replace="true") 二维码
</template>

<style lang="stylus" scoped>
.container
  padding 12px 12px 62px
  height 100%
  .card
    position relative
    height 100%
    border-radius 4px
    background rgba(255, 255, 255, 1)
    background #fff
    .qrcode
      position absolute
      top 50%
      left 50%
      width 220px
      height 220px
      transform translate(-50%, -50%)
    .tips
      position absolute
      bottom 0
      left 0
      display flex
      justify-content center
      align-items center
      padding 15px 0
      width 100%
      height 52px
      background rgba(61, 168, 245, 0.05)
      color rgba(61, 168, 245, 1)
      text-align center
      font-weight 500
      font-size 14px
      line-height 1
      span
        margin-left 12px
</style>
