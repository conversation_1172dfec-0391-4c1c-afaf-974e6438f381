<template lang="pug">
NavContainer(title="活动详情" :loading="store.loading" homePath="/meeting/activities")
  Empty(desc="活动不存在" v-if="!activity.id")
  .container(v-else)
    .content
      section
        .title {{ activity.title }}
        .item
          label 时间
          span {{ activity.begin_time | format('MM/DD HH:mm') }}-{{ activity.end_time | format('HH:mm') }}
        .item
          label 地点
          span {{ activity.location }}
        .item
          label 类型
          span {{ activity.typeText }}
      section
        .sub-title 活动描述
        .desc {{ activity.desc }}
      section
        .cell
          .name 已报名
          .value {{ activity.supposed_count }}
        .cell
          .name 还可报名
          .value
            template(v-if="activity.limit_count")
              | {{ activity.balance }}
            template(v-else)
              | 不限
      router-link.qrcode-trigger(to="/meeting/qrcode")
        van-icon(name="qr")
        span 我的二维码
        van-icon(name="arrow")
    .footer
      van-button(size="large" type="info" v-if="can.apply" @click="apply" :loading="loading")
        | 报名
      van-button(
        size="large"
        type="info"
        plain
        v-else-if="can.cancel" @click="cancelAttendance"
        :loading="loading")
        | 取消报名
      van-button(color="#ccc" v-else-if="attendance.state" size="large" disabled)
        | {{ attendance.state }}
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import activityStore from '@/store/modules/meeting/activity.store';
import activity from '@/models/meeting/activity';
import attendance, { AttendanceStates, Attendance } from '@/models/meeting/attendance';

@Component({
  components: {},
})
export default class ActivitiesShow extends Vue {
  loading: boolean = false;

  get store() {
    return activityStore;
  }
  get activity() {
    return activityStore.currentActivity;
  }
  get attendance() {
    return this.activity.meeting_attendance || {};
  }
  get can() {
    return Attendance.can(this.attendance, this.activity);
  }

  mounted() {
    activityStore.find(+this.$route.params.id);
  }
  // 报名确认
  apply() {
    if (new Date() > new Date(this.activity.end_time!)) {
      this.$message.warning('会议活动已结束');
      return;
    }
    this.$dialog.confirm({
      title: '报名',
      message: '您确定要报名此次会议活动吗？',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          try {
            this.loading = true;
            const { data } = await attendance.apply(this.activity.id!);
            await activityStore.find(this.activity.id!);
            done();
            this.loading = false;
          } catch (error) {
            done();
            this.loading = false;
          }
        } else {
          done();
        }
      },
    });
  }
  cancelAttendance() {
    this.$dialog.confirm({
      title: '取消报名',
      message: '您确定要取消此次会议活动的报名吗？',
      beforeClose: async (action: string, done: any) => {
        if (action === 'confirm') {
          try {
            this.loading = true;
            await attendance.cancel(this.attendance.id!);
            this.attendance.state = AttendanceStates.canceled;
            done();
            this.loading = false;
          } catch (error) {
            done();
            this.loading = false;
          }
        } else {
          done();
        }
      },
    });
  }
}
</script>

<style lang="stylus" scoped>
.container
  display flex
  flex-direction column
  height 100%
  background #fff
  .content
    overflow auto
    padding 20px
    height 100%
    .qrcode-trigger
      position fixed
      right 0
      bottom 80px
      padding 8px 12px
      border-top-left-radius 18px
      border-bottom-left-radius 18px
      background #DCEEFC
      color #3DA8F5
      text-align center
      text-decoration none
      font-size 12px
      line-height 20px
      i
        vertical-align middle
        font-size 13px
        line-height 20px
      span
        margin 0 4px
        vertical-align middle
        line-height 20px
    section
      overflow hidden
      margin-bottom 20px
      padding-bottom 20px
      border-bottom 1px solid #E8E8E8
      color rgba(56, 56, 56, 1)
      font-size 14px
      &:last-child
        border-bottom none
      .title
        margin-bottom 16px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 18px
        line-height 25px
      .sub-title
        margin-bottom 12px
        color rgba(56, 56, 56, 1)
        font-weight 500
        font-size 15px
        line-height 20px
      .item
        display flex
        justify-content space-between
        align-items center
        padding 6px 0
        font-size 14px
        line-height 20px
        label
          color rgba(166, 166, 166, 1)
        span
          color rgba(56, 56, 56, 1)
      .desc
        line-height 22px
      .cell
        float left
        width 70px
        .name
          color rgba(166, 166, 166, 1)
          font-weight 500
          font-size 14px
          line-height 20px
        .value
          margin-top 4px
          height 25px
          font-size 18px
          line-height 25px
  .footer
    flex-shrink 0
    border-top 1px solid #E8E8E8
    button
      height 48px
      border none
      border-radius 0
</style>
