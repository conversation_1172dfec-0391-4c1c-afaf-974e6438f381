import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/meeting/activities',
    name: 'meeting_activities',
    component: () => import(/* webpackChunkName: "meeting_activities_index" */ './Index.vue'),
    meta: {
      title: '会议管理系统',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/meeting/activities/:id',
    name: 'meeting_activities_show',
    component: () => import(/* webpackChunkName: "meeting_activities_show" */ './Show.vue'),
    meta: {
      title: '会议管理系统',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
];

export default routes;
