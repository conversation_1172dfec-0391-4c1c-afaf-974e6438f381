<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ActivityCell from '@/components/meeting/ActivityCell.vue';
import activityStore from '@/store/modules/meeting/activity.store';

@Component({
  components: {
    ActivityCell,
  },
})
export default class MeetingActivities extends Vue {
  get store() {
    return activityStore;
  }

  created() {
    this.store.SET_RECORDS({ current_page: 0 });
    this.fetchData(1);
  }

  async fetchData(page: number = 1) {
    await this.store.fetch({
      page,
      q: {
        published_eq: true,
        end_time_gt: new Date(),
      },
    });
  }
}
</script>

<template lang="pug">
NavContainer(title="全部活动")
  //- van-icon(slot="right" name="search")
  .container
    ListView(
      :data="store.activities"
      :loading="store.loading"
      :pullup="!store.finish"
      :pulldown="true"
      emptyText="暂无活动"
      @loadMore="fetchData(store.currentPage + 1)"
      @refresh="fetchData(1)")
      .activities
        ActivityCell(
          v-for="item in store.activities"
          :key="item.id"
          :activity="item"
          @click="$router.push(`/meeting/activities/${item.id}`)")
    van-tabbar.tabbar(:value="0" fixed safe-area-inset-bottom)
      van-tabbar-item(icon="flag-o" to="/meeting/activities" :replace="true") 全部活动
      van-tabbar-item(icon="orders-o" to="/meeting/attendances" :replace="true") 我报名的
      van-tabbar-item(icon="qr" to="/meeting/qrcode" :replace="true") 二维码
</template>

<style lang="stylus" scoped>
.container
  position relative
  height 100%
  padding-bottom 50px
  .activities
    padding 12px
</style>
