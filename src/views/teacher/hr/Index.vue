<template lang="pug">
  .container
    router-view
    van-tabbar.tabbar(:value="activeIndex" fixed safe-area-inset-bottom :zIndex="1000")
      van-tabbar-item(icon="notes-o" to="/hr/teacher/contacts" :replace="true") 通讯录
      van-tabbar-item(icon="friends-o" to="/hr/teacher/star" :replace="true")  星标联系人
      van-tabbar-item(icon="bar-chart-o" to="/hr/teacher/organizations" :replace="true") 组织结构
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class hrIndex extends Vue {
  private state: string = '';

  @Prop() private property!: string;

  get activeIndex() {
    return ['/hr/teacher/contacts', '/hr/teacher/star', '/hr/teacher/organizations'].indexOf(this.$route.path);
  }

  public mounted() {
    this.fetchData();
  }

  public fetchData() {}
}
</script>

<style lang="stylus" scoped>
.container
  width 100%
  height 100%
</style>
