<template lang="pug">
NavContainer(title="星标联系人")
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class star extends Vue {
  private state: string = '';

  @Prop() private property!: string;

  public mounted() {
    this.fetchData();
  }

  public fetchData() {}
}
</script>

<style lang="stylus" scoped></style>
