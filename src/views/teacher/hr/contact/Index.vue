<template lang="pug">
NavContainer(
  title="通讯录"
  :loading="teacherStore.loading"
  search
  v-model="queryObject"
  placeholder="输入姓名、工号、所属部门"
  :variables="['name', 'code']")
  .teachers(
    v-infinite-scroll="loadMore"
    infinite-scroll-disabled="disabled"
    infinite-scroll-distance="300")
    van-index-bar(:index-list="indexList" highlight-color="#3DA8F5")
      .group(v-for="(group, index) in groups" :key="index")
        van-index-anchor(:index="group.label")
        van-cell(
          v-for="(item, index) in group.teachers"
          :key="index"
          @click="onShow(item)")
          Avatar(
            :name="item.name || item.code"
            size="32px"
            :bgColor="item.sex === '女' ? '#F28E36' : '#58A8EF'"
            slot="icon")
          .info-box(slot="title")
            strong {{ item.name || item.code }}
            .department {{ item.department_name }}
      template(v-if="teacherStore.finish")
        van-divider 没有更多了
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import hrTeacherStore from '@/store/modules/hr/teacher.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  private queryObject: IObject = {};
  private groups: any[] = [];
  get teacherStore() {
    return hrTeacherStore || {};
  }
  get indexList() {
    return this.groups.map((e: any) => e.label);
  }
  get disabled() {
    return this.teacherStore.loading || this.teacherStore.finish;
  }

  @Watch('queryObject')
  public watchQuery() {
    this.fetchData();
  }

  public mounted() {
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 30,
      shouldAppend: true,
      q: {
        s: ['name_pinyin asc'],
        ...this.queryObject,
      },
    };
    const { data } = await hrTeacherStore.fetch(params);
    const groups: any[] = [];
    (this.teacherStore.records || []).forEach((item: any) => {
      const index = groups.findIndex((e: any) => e.label === item.first_letter);
      if (index >= 0) {
        groups[index].teachers.push(item);
      } else {
        groups.push({
          label: item.first_letter,
          teachers: [item],
        });
      }
    });
    this.groups = groups;
  }

  public onShow(val: any) {
    this.$router.push(`/hr/teacher/contacts/${val.id}`);
  }

  public loadMore() {
    if (this.teacherStore.currentPage < this.teacherStore.totalPages) {
      this.fetchData(this.teacherStore.currentPage + 1);
    }
  }
}
</script>

<style lang="stylus" scoped>
.teachers
  overflow auto
  width 100%
  height 100%
  background #F4F5F6
  &::-webkit-scrollbar
    display none
  .group
    width 100%
    .van-cell
      display flex
      align-items center
      .info-box
        margin-left 14px
        width 100%
        strong
          color #383838
          font-size 16px
          line-height 20px
        .department
          margin-top 4px
          color #808080
          font-size 14px
          line-height 20px
</style>
