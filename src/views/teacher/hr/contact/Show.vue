<template lang="pug">
NavContainer.container(title="人员信息" :loading="teacherStore.loading")
  van-cell-group
    van-cell
      Avatar(
        :name="teacher.name || teacher.code"
        size="40px"
        :bgColor="teacher.sex === '女' ? '#F28E36' : '#58A8EF'"
        slot="icon")
      .name(slot="title") {{ teacher.name || teacher.code }}
  van-cell-group
    van-cell(v-for="(item, index) in dataKeys" :key="index")
      template(slot="icon")
        .cell-label {{ item.label }}
      template(slot="title")
        .cell-value
          span(v-if="['identity_type'].includes(item.key)")
            | {{ teacher[item.key] === '1' ? '身份证' : teacher[item.key] }}
          span(v-else) {{ teacher[item.key] }}
      template(v-if="['phone'].includes(item.key) && teacher.phone" slot="right-icon")
        a(:href="'tel:' + teacher.phone")
          van-icon(name="phone-o")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import hrTeacherStore from '@/store/modules/hr/teacher.store';

@Component({
  components: {},
})
export default class Show extends Vue {
  private dataKeys: any[] = [
    {
      label: '姓名拼音',
      key: 'name_pinyin',
    },
    {
      label: '性别',
      key: 'sex',
    },
    {
      label: '工号',
      key: 'code',
    },
    // {
    //   label: '手机号',
    //   key: 'phone',
    // },
    {
      label: '邮箱',
      key: 'email',
    },
    {
      label: '所属部门',
      key: 'department_name',
    },
    {
      label: '所属学院',
      key: 'college_name',
    },
    // {
    //   label: '出生日期',
    //   key: 'birthday',
    // },
    // {
    //   label: '证件类型',
    //   key: 'identity_type',
    // },
    // {
    //   label: '证件号',
    //   key: 'identity_id',
    // },
  ];
  get teacherStore() {
    return hrTeacherStore || {};
  }
  get teacher() {
    return this.teacherStore.record || {};
  }

  public mounted() {
    this.fetchData();
  }

  public fetchData() {
    hrTeacherStore.find(this.$route.params.id);
  }
}
</script>

<style lang="stylus" scoped>
.container
  width 100%
  background #f5f5f5
  .van-cell-group
    margin-bottom 16px
    .van-cell
      display flex
      align-items center
      padding 16px 20px
      .name
        margin-left 16px
        font-weight 500
        font-size 18px
        line-height 20px
      .cell-label
        width 74px
        color #808080
        font-size 14px
        line-height 20px
      .cell-value
        color #383838
        font-size 14px
        line-height 20px
</style>
