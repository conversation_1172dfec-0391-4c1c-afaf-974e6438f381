<template lang="pug">
NavContainer(title="组织结构" :loading="departmentStore.loading")
  .container(
    v-infinite-scroll="loadMore"
    infinite-scroll-disabled="disabled"
    infinite-scroll-distance="300")
    .card-list(v-for="(item, index) in date" :key="index  ")
      ComOrganization(:duty="item" :departmentType='getType(item)')

</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ComOrganization from '@/components/teacher/ComOrganization.vue';
import DepartmentStore from '@/store/modules/hr/department.store';
import { IDepartment } from '@/models/hr/department';

@Component({
  components: { ComOrganization },
})
export default class organization extends Vue {
  @Prop() private property!: string;
  private state: string = '';
  date: any = [];
  private queryObject: IObject = {};
  get departmentStore() {
    return DepartmentStore;
  }

  @Watch('queryObject')
  public watchQuery() {
    this.fetchData();
  }

  public mounted() {
    this.fetchData();
  }

  // eslint-disable-next-line consistent-return
  getType(item: IDepartment) {
    if (item.type === 'Department::Major') {
      return '部门';
    }
    if (item.type === 'Department::College') {
      return '学院';
    }
    return '科室';
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 30,
      shouldAppend: true,
      q: {
        s: ['name_pinyin asc'],
        ...this.queryObject,
      },
    };
    await this.departmentStore.fetch(params).then(res => {
      this.date.push(...res.data.departments);
    });
  }

  public loadMore() {
    if (this.departmentStore.currentPage < this.departmentStore.totalPages) {
      this.fetchData(this.departmentStore.currentPage + 1);
    }
  }
}
</script>

<style lang="stylus" scoped>
.container
  overflow auto
  padding 0px 10px
  width 100%
  height 100%
  background #F4F5F6
  &::-webkit-scrollbar
    display none
  padding-bottom 70px
</style>
