import { RouteConfig } from '@/interfaces/IRoute';

const Index = () => import(/* webpackChunkName: "hr_teacher_modification_index" */ './Index.vue');
const Show = () => import(/* webpackChunkName: "hr_teacher_modification_show" */ './Show.vue');
const Form = () => import(/* webpackChunkName: "hr_teacher_modification_form" */ './Form.vue');

export default [
  {
    path: '/hr/teacher/modifications',
    name: 'hr_teacher_modification_index',
    component: Index,
    meta: {
      title: '通讯录',
      roles: ['Teacher'],
    },
  },
  {
    path: '/hr/teacher/modifications/new',
    name: 'hr_teacher_modification_new',
    component: Form,
    meta: {
      title: '修改信息',
      roles: ['Teacher'],
    },
  },
  {
    path: '/hr/teacher/modifications/:modificationId/edit',
    name: 'hr_teacher_modification_edit',
    component: Form,
    meta: {
      title: '编辑修改信息',
      roles: ['Teacher'],
    },
  },
  {
    path: '/hr/teacher/modifications/:modificationId',
    name: 'hr_teacher_modification_show',
    component: Show,
    meta: {
      title: '详情',
      roles: ['Teacher'],
    },
  },
] as RouteConfig[];
