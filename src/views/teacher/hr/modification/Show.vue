<template lang="pug">
.container
  .header
  .main
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Show extends Vue {
  public mounted() {
    this.fetchData();
  }

  public fetchData() {}
}
</script>

<style lang="stylus" scoped>
.container
  overflow hidden
  padding-top 48px
  width 100%
  height 100%
  background #fff
  .header
    float left
    margin-top -48px
    width 100%
  .main
    overflow auto
    width 100%
    height 100%
</style>
