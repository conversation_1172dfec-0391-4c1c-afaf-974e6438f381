import { RouteConfig } from '@/interfaces/IRoute';

const route: RouteConfig[] = [
  {
    // 人事管事
    path: '/hr/teacher',
    name: 'hr_Index',
    component: () => import(/* webpackChunkName: "hr_index" */ '@/views/teacher/hr/Index.vue'),
    meta: {
      title: '人事管理首页',
      roles: ['Teacher'],
    },
    children: [
      {
        path: '/hr/teacher/contacts',
        name: 'hr_teacher_contact_index',
        component: () => import(/* webpackChunkName: "hr_teacher_contact_index" */ './contact/Index.vue'),
        meta: {
          title: '通讯录',
          roles: ['Teacher'],
        },
      },
      {
        path: '/hr/teacher/organizations',
        name: 'hr_teacher_organization_index',
        component: () => import(/* webpackChunkName: "hr_teacher_organization_index" */ './organization/Index.vue'),
        meta: {
          title: '组织结构',
          roles: ['Teacher'],
        },
      },
      {
        path: '/hr/teacher/star',
        name: 'hr_teacher_star_index',
        component: () => import(/* webpackChunkName: "hr_teacher_star_index" */ './star/Index.vue'),
        meta: {
          title: '星标联系人',
          roles: ['Teacher'],
        },
      },
    ],
  },
];

export default route;
