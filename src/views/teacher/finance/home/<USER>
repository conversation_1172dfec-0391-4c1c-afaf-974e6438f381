<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import session from '@/models/session';
import IDQrCode from '@/components/session/IDQrCode.vue';
import instanceModel, { InstanceType } from '@/models/bpm/instance';

@Component({
  components: {
    IDQrCode,
  },
})
export default class FinanceTeacherHome extends Vue {
  statistic: IObject = {};
  timer: any = null;

  mounted() {
    this.loadFinanceStatistic();
  }
  beforeDestroy() {
    clearTimeout(this.timer);
  }
  async loadFinanceStatistic() {
    const { data } = await instanceModel.statistic(InstanceType.Voucher);
    this.statistic = data.statistic;
    this.timer = setTimeout(this.loadFinanceStatistic, 10000);
  }
}
</script>

<template lang="pug">
NavContainer(title="资金卡管理")
  AppGroup(title="付款凭证")
    AppGroupItem(
      to="/finance/teacher/instances?state=todo"
      :icon="require('@/assets/images/meeting/icon_activity.png')"
      name="待我提交"
      :count="statistic.todo")
    AppGroupItem(
      to="/finance/teacher/instances?state=created"
      :icon="require('@/assets/images/meeting/icon_activity.png')"
      name="我发起的")
    AppGroupItem(
      to="/finance/teacher/instances?state=approving"
      :icon="require('@/assets/images/meeting/icon_activity.png')"
      name="待我审批"
      :count="statistic.approving")
    AppGroupItem(
      to="/finance/teacher/instances?state=approved"
      :icon="require('@/assets/images/meeting/icon_activity.png')"
      name="我已审批")
    AppGroupItem(
      to="/finance/teacher/instances?state=notified"
      :icon="require('@/assets/images/meeting/icon_activity.png')"
      name="抄送我的")
  AppGroup(title="操作")
    AppGroupItem(
      :icon="require('@/assets/images/meeting/icon_activity.png')"
      name="扫一扫")
</template>

<style lang="stylus" scoped>
.container
  padding 12px 12px 62px
  height 100%
  .card
    position relative
    height 100%
    border-radius 4px
    background rgba(255, 255, 255, 1)
    background #fff
    .qrcode
      position absolute
      top 50%
      left 50%
      width 220px
      height 220px
      transform translate(-50%, -50%)
    .tips
      position absolute
      bottom 0
      left 0
      display flex
      justify-content center
      align-items center
      padding 15px 0
      width 100%
      height 52px
      background rgba(61, 168, 245, 0.05)
      color rgba(61, 168, 245, 1)
      text-align center
      font-weight 500
      font-size 14px
      line-height 1
      span
        margin-left 12px
</style>
