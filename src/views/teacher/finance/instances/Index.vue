<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import InstanceCell from '@/components/finance/InstanceCell.vue';
import instanceStore from '@/store/modules/bpm/instance.store';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    InstanceCell,
  },
})
export default class FinanceTeacherInstances extends Vue {
  get store() {
    return instanceStore;
  }
  get currentState(): string {
    return (this.$route.query.state || 'processing') as string;
  }
  get searchVariables() {
    return [
      'flowable_of_Finance::Voucher_type_seq',
      'flowable_of_Finance::Voucher_type_projects_uid',
      'flowable_of_Finance::Voucher_type_projects_name',
      'flowable_of_Finance::Voucher_type_projects_department_name',
      'flowable_of_Finance::Voucher_type_teacher_name',
    ];
  }
  get title() {
    return ({
      todo: '待我提交',
      approving: '待我审批',
      created: '我发起的',
      approved: '我已审批',
      notified: '抄送我的',
    } as any)[this.currentState];
  }

  created() {
    this.store.SET_RECORDS({ current_page: 0, instances: [] });
    this.fetchData(1);
  }

  async fetchData(page: number = 1, query?: object) {
    const { id } = sessionStore.currentUser;
    const { type } = sessionStore.account;
    await this.store.fetch({
      page,
      per_page: 15,
      q: {
        [`${this.currentState}`]: [id, type],
        type_eq: 'Finance::VoucherInstance',
        ...query,
      },
    });
  }
}
</script>

<template lang="pug">
NavContainer(:title="title")
  van-icon(slot="right" name="search")
  ListView(
    :data="store.records"
    :loading="store.loading"
    :pullup="!store.finish"
    @loadMore="fetchData(store.currentPage + 1)"
    @refresh="fetchData(1)")
    .activities
      InstanceCell(
        v-for="item in store.records"
        :key="item.id"
        :instance="item"
        @click="$router.push(`/bpm/instances/${item.id}`)")
</template>

<style lang="stylus" scoped>
.activities
  padding 12px
</style>
