<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import workflowModel, { IWorkflow, WorkflowTypes } from '@/models/bpm/workflow';
import instanceModel, { InstanceType } from '@/models/bpm/instance';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {},
})
export default class BpmWorkflows extends Vue {
  statistic: IObject = {
    todo_count: 0,
    total: 0,
  };
  groupApps: IObject = {};
  apps: IWorkflow[] = [];
  loading: boolean = false;
  otherCategoryName: string = '其他';
  keyword: string = '';

  get wotkflowType() {
    return this.$route.query.type || WorkflowTypes.Bpm;
  }
  get instanceType() {
    const type: any = String(this.wotkflowType).split('::')[0];
    return (InstanceType as any)[type] as InstanceType;
  }

  mounted() {
    this.fetchWorkflows();
    this.fetchInstanceCount();
  }

  async fetchWorkflows() {
    this.loading = true;
    // const roleFilter = (sessionStore.currentUser as IObject).type === 'Teacher' ? 'for_teacher' : 'for_student';
    const { data } = await workflowModel
      .index({
        page: 1,
        per_page: 1000,
        q: {
          // [roleFilter]: true,
          type_eq: this.wotkflowType,
        },
      })
      .finally(() => {
        this.loading = false;
      });
    this.apps = data.workflows.map(w => ({
      ...w,
      category_name: w.category_name || this.otherCategoryName,
    }));
    this.setGroupWorkflows(this.apps);
  }
  async fetchInstanceCount() {
    const { data } = await instanceModel.statistic(this.instanceType);
    this.statistic = {
      total: data.total,
      ...data.statistic,
    };
  }
  showAppInstances(id: number) {
    this.$router.push(`/bpm/workflows/${id}/instances?state=approving&type=${this.instanceType}`);
  }
  showInstances(state: string) {
    this.$router.push(`/bpm/instances?state=${state}&type=${this.instanceType}`);
  }
  setGroupWorkflows(workflows: IWorkflow[]) {
    this.groupApps = this.$utils.groupBy(workflows, 'category_name');
  }
  onSearch() {
    const workflows = this.apps.filter((w: IWorkflow) => w.name!.includes(this.keyword));
    this.setGroupWorkflows(workflows);
  }
  onClear() {
    this.keyword = '';
    this.onSearch();
  }
}
</script>

<template lang="pug">
.container
  SearchBar(
    v-model="keyword"
    @search="onSearch"
    @clear="onClear")
  .header
    .app(@click="showInstances('todo')")
      van-icon.icon(name="user-o" :info="statistic.todo || null")
      .name 待我提交
    .app(@click="showInstances('approving')")
      van-icon.icon(name="clock-o" :info="statistic.approving || null")
      .name 待我审批
    .app(@click="showInstances('approved')")
      van-icon.icon(name="passed")
      .name 我已审批
    .app(@click="showInstances('notified')")
      van-icon.icon(name="bullhorn-o")
      .name 抄送我的

  van-panel.panel(
    v-if="categoryName !== otherCategoryName"
    v-for="(apps, categoryName) in groupApps"
    :key="categoryName"
    :title="categoryName")
    .applications
      .app(
        v-for="app in apps"
        :key="app.id"
        @click="showAppInstances(app.id)")
        img.logo(src="@/assets/images/icon_app.png")
        .name {{ app.name }}
  van-panel.panel(
    v-if="groupApps[otherCategoryName]"
    :title="otherCategoryName")
    .applications
      .app(
        v-for="app in groupApps[otherCategoryName]"
        :key="app.id"
        @click="showAppInstances(app.id)")
        img.logo(src="@/assets/images/icon_app.png")
        .name {{ app.name }}
</template>

<style lang="stylus" scoped>
.container
  overflow auto
  width 100%
  height 100%
  -webkit-overflow-scrolling touch
  .header
    display flex
    justify-content space-around
    align-items center
    margin-top 1px
    margin-bottom 16px
    width 100%
    background #fff
    .icon
      color #007BFF
      font-weight bold
      font-size 30px
    .name
      color #666
      font-size 14px
  .panel
    margin-bottom 16px
    .applications
      display flex
      flex-wrap wrap
      align-items stretch
      width 100%
  .app
    flex 0 0 25%
    padding 16px 0
    text-align center
    &:active
      background #f8f8f8
    .logo
      width 48px
      height 48px
    .name
      margin-top 6px
      padding 0 6px
      color #666
      text-align center
      font-size 14px
      line-height 1.4
</style>
