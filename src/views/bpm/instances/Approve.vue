<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import tokenModel, { IToken } from '@/models/bpm/token';
import instanceStore from '@/store/modules/bpm/instance.store';
import { PlaceActionType, getDefaultPlaceActionConfig } from '@/models/bpm/workflow';
import TemplateForm from '@/components/form/TemplateForm.vue';

@Component({
  components: {
    TemplateForm,
  },
})
export default class FinanceInstanceApprove extends Vue {
  historyPlaceOptions: IObject[] = [];
  nextPlaceId: number | null = 0;
  currentToken: IToken = {
    place: {},
    options: {
      users: [],
    },
  };
  tokenComment: string = '';
  loading: boolean = false;
  // token form
  tokenLoading: boolean = false;

  get action(): PlaceActionType {
    return this.$route.query.action as PlaceActionType;
  }
  get tokenFormTemplate() {
    const { place_form } = this.currentToken;
    return place_form && place_form.fields ? place_form.fields || [] : [];
  }
  get tokenPayload() {
    const { token_payload } = this.currentToken;
    return token_payload || {};
  }
  get placeActionConfig() {
    return {
      ...getDefaultPlaceActionConfig(),
      ...this.currentToken.action_alias,
    };
  }

  mounted() {
    this.fetchInstance();
  }

  async fetchInstance() {
    try {
      this.loading = true;
      const { data } = await instanceStore.find(+this.$route.params.id);
      this.historyPlaceOptions = data.historyPlaceOptions;
      this.nextPlaceId = data.defaultNextPlaceId;
      this.currentToken = data.current_token!;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  submitAcceptForm() {
    (this.$refs.approveForm as any).submit({
      success: async (payload: IObject) => {
        delete payload.id;
        this.tokenLoading = true;
        await tokenModel
          .update({
            id: this.currentToken.id,
            token_payload: payload,
          })
          .finally(() => {
            this.tokenLoading = false;
          });
        this.fire();
      },
    });
  }

  async fire() {
    try {
      this.loading = true;
      this.nextPlaceId = this.nextPlaceId ? this.nextPlaceId : null;
      await tokenModel.fire(this.action, this.currentToken.id, this.tokenComment, this.nextPlaceId);
      this.loading = false;
      this.$message.success('操作成功');
      this.back();
    } catch (error) {
      this.loading = false;
    }
  }
  back() {
    this.$router.back();
  }
}
</script>

<template lang="pug">
.container
  van-nav-bar(
    title="审批"
    left-text="返回"
    left-arrow
    fixed
    @click-left="$router.back()")
  .form-item(v-if="action === 'reject'")
    SelectField(
      v-model.number="nextPlaceId"
      :options="historyPlaceOptions"
      label="打回节点 (可选)")
  .form-item(v-if="action === 'fail'")
    SelectField(
      v-model.number="nextPlaceId"
      :options="historyPlaceOptions"
      label="退回节点 (可选)")
  .form-item(v-if="tokenFormTemplate.length > 0")
    TemplateForm(
      ref="approveForm"
      :formData="tokenPayload"
      :template="tokenFormTemplate"
      :showActions="false")
    .actions(v-if="action === 'accept'")
      van-button(
        type="info"
        @click="submitAcceptForm"
        :disabled="tokenLoading || loading"
        :loading="tokenLoading || loading")
        | {{ placeActionConfig.accept.name }}
      van-button(@click="back")
        | 取消
  .form-item(v-else)
    van-field(
      v-model="tokenComment"
      rows="4"
      autosize
      label="备注（可选）"
      type="textarea"
      maxlength="200"
      placeholder="输入审批备注"
      show-word-limit)
    .actions(v-if="action === 'accept'")
      van-button(type="info" @click="fire" :disabled="loading" :loading="loading")
        | {{ placeActionConfig.accept.name }}
      van-button(@click="back")
        | 取消

  .actions(v-if="action !== 'accept'")
    van-button(type="danger" @click="fire" :disabled="loading" :loading="loading" v-if="action === 'reject'")
      | {{ placeActionConfig.reject.name }}
    van-button(type="danger" @click="fire" :disabled="loading" :loading="loading" v-if="action === 'fail'")
      | {{ placeActionConfig.fail.name }}
    van-button(type="danger" @click="fire" :disabled="loading" :loading="loading" v-if="action === 'terminated'")
      | {{ placeActionConfig.terminated.name }}
    van-button(@click="back")
      | 取消
</template>

<style lang="stylus" scoped>
.container
  padding-top 46px
  .form-item
    background #ffffff
  .actions
    margin-top 15px
    padding 15px
    button
      margin-bottom 10px
      width 100%
</style>
