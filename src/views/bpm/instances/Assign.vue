<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import tokenModel, { IToken, TokenTypes } from '@/models/bpm/token';
import instanceStore from '@/store/modules/bpm/instance.store';
import sessionStore from '@/store/modules/session.store';
import { PlaceActionType } from '@/models/bpm/workflow';
import { ITeacher } from '@/models/teaching/teacher';

@Component({
  components: {},
})
export default class InstanceAssign extends Vue {
  workflowId: number = 0;
  currentToken: IToken = {
    place: {},
    options: {
      users: [],
    },
  };
  // 选择审批人
  assignUsers: ITeacher[] = [];
  selectorVisible: boolean = false;
  // 已设置审批人
  tokenUsers: IObject[] = [];
  loading: boolean = false;

  get action(): PlaceActionType {
    return this.$route.query.action as PlaceActionType;
  }
  get currentUser() {
    return sessionStore.currentUser;
  }
  get isSingleAssign() {
    return this.currentToken.type === TokenTypes.Approval;
  }
  get isMultiAssign() {
    return this.currentToken.type === TokenTypes.ApprovalSelect;
  }

  mounted() {
    this.fetchInstance();
  }

  async fetchInstance() {
    try {
      this.loading = true;
      const { data } = await instanceStore.find(+this.$route.params.id);
      this.currentToken = data.current_token!;
      this.workflowId = data.workflow_id;
      this.tokenUsers = (this.currentToken.options.users || []).map((o: any) => ({
        ...o,
        label: o.name,
        value: o.id,
      }));
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  submit() {
    if (this.isSingleAssign) {
      this.assign();
    } else {
      this.assignManyUsers();
    }
  }
  async assign() {
    try {
      const user = this.assignUsers[0];
      if (!user) return;
      this.loading = true;
      await tokenModel.assign(this.currentToken.id, {
        operator_id: user.id,
        operator_type: 'Teacher',
      });
      this.selectorVisible = false;
      this.loading = false;
      this.$message.success('指派成功');
      this.back();
    } catch (error) {
      this.loading = false;
    }
  }
  async assignManyUsers() {
    try {
      this.loading = true;
      await tokenModel.assign(this.currentToken.id, {
        operator_id: this.currentUser.id,
        operator_type: 'Teacher',
        options: {
          users: this.assignUsers.map(user => ({
            ...this.$utils.only(user, ['id', 'code', 'name', 'type']),
          })),
        },
      });
      this.selectorVisible = false;
      this.loading = false;
      this.$message.success('指派成功');
      this.back();
    } catch (error) {
      this.loading = false;
    }
  }
  onRadioChange(id: number) {
    this.assignUsers = this.tokenUsers.filter(o => o.id === id);
  }
  back() {
    this.$router.back();
  }
}
</script>

<template lang="pug">
.container
  van-notice-bar(wrapable :scrollable="false")
    | 当前进度：{{ currentToken.place_name }}

  van-cell-group.group(v-if="tokenUsers.length")
    radio-field(
      label="选择人员"
      v-model="selectedContactId"
      :options="tokenUsers"
      :required="true"
      @change="onRadioChange")
  van-cell-group.group(v-else-if="isMultiAssign")
    ContactField(
      v-model="assignUsers"
      label="选择人员"
      placeholder="点击设置"
      :multiple="true")
  van-cell-group.group(v-else-if="isSingleAssign")
    ContactField(
      v-model="assignUsers"
      label="选择人员"
      placeholder="点击设置"
      :multiple="false")
  .actions
    van-button(
      type="info"
      @click="submit"
      :loading="loading"
      :disabled="!assignUsers.length")
      | 提交
    van-button(@click="back")
      | 取消
</template>

<style lang="stylus" scoped>
.container
  .group
    margin-top 30px
  .avatar
    margin-right 8px
  .actions
    margin-top 15px
    padding 15px
    button
      margin-bottom 10px
      width 100%
</style>
