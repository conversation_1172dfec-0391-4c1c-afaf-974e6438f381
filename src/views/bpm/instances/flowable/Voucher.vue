<template lang="pug">
.flowable-finance-voucher(v-loading="voucherStore.loading")
  section.group
    van-cell(:border="false" title="资金卡号" :value="project.uid")
    van-cell(:border="false" title="资金卡名" :value="project.name")
    van-cell(:border="false" title="一级项目" :value="project.project_category_name")
    van-cell(
      :border="false"
      v-for="role in project.project_roles"
      :key="role.id"
      :title="role.approval_role_name"
      :value="role.teacher_name")
  section.group
    .title 单据内容
    .payments
      table.native-table
        tr
          th 二级
          th 三级
          th 来源
          th 科目
          th 金额
        tr(v-for="payment in (voucher.payments || [])" :key="payment.id")
          td {{ payment.catalog_name }}
          td {{ payment.budget_name }}
          td {{ payment.origin_name }}
          td {{ payment.subject_name }}
          td {{ payment.amount }}
  section.group(v-if="voucher.type === 'Finance::RoutineVoucher'")
    .title 付款信息
    van-cell(:border="false" title="付款方式" :value="payeeMeta.payment_way")
    van-cell(:border="false" title="收款人（单位）" :value="payeeMeta.name")
    van-cell(:border="false" title="事由备注" :value="voucher.remark || '-'")
  section.group(v-else)
    .title 出差人信息
    van-cell(:border="false" title="姓名" :value="payeeMeta.name")
    van-cell(:border="false" title="部门" :value="payeeMeta.department")
    van-cell(:border="false" title="付款方式" :value="payeeMeta.payment_way")
    van-cell(:border="false" title="事由备注" :value="voucher.remark || '-'")
  section.group
    .title.flex-between
      span 报销费用合计
      span ￥{{ $utils.toUsCurrency(voucher.final_amount || 0) }}
    van-cell(:border="false" title="劳务费个税" :value="`￥${$utils.toUsCurrency(voucher.tax || 0)}`")

  van-collapse.collapse(v-model="activePanel")
    van-collapse-item.collapse-item(title="报销费用详情" name="1" v-if="voucher.type === 'Finance::OutsideVoucher'")
      OutsideVoucherInfo(:data="meta")
    van-collapse-item.collapse-item(:title="`单据附件 (${attachments.length})`" name="2")
      Attachments(:attachments="attachments")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { VoucherStore } from '@/store/modules/finance/voucher.store';
import instanceModel, { IInstance } from '@/models/bpm/instance';
import Attachments from '@/components/global/Attachments.vue';
import OutsideVoucherInfo from '@/components/finance/OutsideVoucherInfo.vue';

@Component({
  components: {
    Attachments,
    OutsideVoucherInfo,
  },
})
export default class InstanceFlowableVoucher extends Vue {
  @Prop() private instance!: IInstance;

  activePanel: string[] = [];

  get voucherStore() {
    return VoucherStore.withActivityTeacher;
  }
  get voucher() {
    return this.voucherStore.record;
  }
  get project() {
    return this.voucher.project || { project_roles: [] };
  }
  get payeeMeta() {
    return this.voucher.payee_meta || {};
  }
  get meta() {
    return this.voucher.meta || { route: {} };
  }
  get attachments() {
    return (this.voucher.receipts || []).map((o: any) => o.attachment);
  }
  get stateMap() {
    return instanceModel.stateMap;
  }

  @Watch('instance', { immediate: true })
  onInstanceChange() {
    this.voucherStore.find(this.instance.flowable_id!);
  }
}
</script>

<style lang="stylus" scoped>
.flowable-finance-voucher
  margin 16px 0
  .group
    overflow hidden
    padding-bottom 20px
    background #fff
    color rgba(56, 56, 56, 1)
    font-size 14px
    .title
      margin-bottom 12px
      padding 0px 16px
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 15px
      line-height 20px
    .payments
      padding 0 16px 10px

.no-margin
  margin-bottom 0 !important
</style>
