<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import TemplateForm from '@/components/form/TemplateForm.vue';
import workflowModel, { IWorkflow, IWorkflowCorePlace } from '@/models/bpm/workflow';
import instanceModel, { IInstance } from '@/models/bpm/instance';
import tokenModel, { IToken } from '@/models/bpm/token';
import { IFormItem } from '@/interfaces/IFormItem';
import TemplateFormatter from '../../../components/form/TemplateFormatter.vue';

type ActionType = 'submit' | 'save';

@Component({
  components: {
    TemplateForm,
    TemplateFormatter,
  },
})
export default class InstanceForm extends Vue {
  workflow: IWorkflow = { id: null };
  instance: IInstance = {
    payload: {},
  };
  // token and place
  currentToken: IToken = {};
  currentPlace: IWorkflowCorePlace = {};
  template: IObject = [];

  get workflowId() {
    return +this.$route.params.workflowId;
  }
  get instanceId() {
    return +this.$route.params.id;
  }
  get title() {
    return this.instanceId ? '修改审批内容' : '发起审批';
  }

  mounted() {
    if (this.instanceId) {
      this.fetchInstance();
    } else {
      this.fetchWorkflowAndForm(this.workflowId);
    }
  }

  // edit
  async fetchInstance() {
    const { data } = await instanceModel.find(this.instanceId);
    this.instance = data;
    this.currentToken = data.current_token!;
    this.fetchWorkflowAndForm(data.workflow_id!, +this.currentToken.id!);
  }
  async fetchWorkflowAndForm(workflowId: number, tokenId?: number) {
    const { token, workflow, formTemplate } = await workflowModel.findTokenForm(workflowId, tokenId);
    this.workflow = workflow;
    this.currentToken = token;
    this.currentPlace = token.transition!;
    this.template = formTemplate;
  }
  submit(type: ActionType = 'save') {
    if (type === 'save') {
      // 暂存不验证表单
      this.instance = {
        payload: (this.$refs.form as any).getFormData(),
      };
      this.create(type);
    } else {
      (this.$refs.form as any).submit({
        success: (formData: IObject) => {
          this.instance = {
            payload: { ...this.instance.payload, ...formData },
          };
          if (this.instanceId) {
            this.update();
          } else {
            this.create(type);
          }
        },
      });
    }
  }
  async create(type: ActionType) {
    const { data } = await instanceModel.createByParent(this.workflowId, this.instance);
    this.instance = data;
    if (type === 'submit') {
      await this.accept(data);
    } else {
      this.$message.success('暂存成功, 你可以点击【开始流程】按钮正式运行流程。');
      this.$router.replace(`/bpm/instances/${this.instance.id}`);
    }
  }
  async update() {
    await instanceModel.update({
      id: this.instanceId,
      ...this.instance,
    });
    this.$message.success('更新成功');
    this.$router.replace(`/bpm/instances/${this.instanceId}`);
  }
  async accept(instance: IInstance) {
    await tokenModel.accept(instance.current_token!.id, '');
    this.$message.success('提交成功');
    this.$router.replace(`/bpm/instances/${this.instance.id}`);
  }
  cancel() {
    this.$router.back();
  }

  formattedTemplate: IFormItem[] = [];
}
</script>

<template lang="pug">
.container
  //- TemplateFormatter(
  //-   ref='templateFormatter',
  //-   v-model='formattedTemplate'
  //-   :payload='instance.payload'
  //-   :template='template'
  //- )
  Loading(v-if="$store.state.loading")
  van-nav-bar(
    :title="title"
    left-text="返回"
    left-arrow
    fixed
    @click-left="$router.back()")
  TemplateForm(
    v-if="template.length > 0"
    ref="form"
    :template="template"
    :formData="instance.payload")
    template(slot="actions")
      van-button(v-if="!instanceId" type="info" :loading="$store.state.loading" @click.prevent.stop="submit('save')")
        | 暂存
      van-button(type="primary" :loading="$store.state.loading" @click.stop.self="submit('submit')")
        | 提交
      van-button(@click="cancel")
        | 取消
</template>

<style lang="stylus" scoped>
.container
  padding-top 50px
  .form-item
    margin-top 14px
</style>
