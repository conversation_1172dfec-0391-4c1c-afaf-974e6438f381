<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import tokenModel, { IToken, TokenTypes } from '@/models/bpm/token';
import instanceStore from '@/store/modules/bpm/instance.store';
import sessionStore from '@/store/modules/session.store';
import { PlaceActionType } from '@/models/bpm/workflow';
import TeacherField from '@/components/form/TeacherField.vue';

@Component({
  components: {
    TeacherField,
  },
})
export default class InstanceAssign extends Vue {
  workflowId: number = 0;
  currentToken: IToken = {
    place: {},
    options: {
      users: [],
    },
  };
  // 选择审批人
  selectedTeacherId: number | number[] = [];
  selectorVisible: boolean = false;
  // 已设置审批人
  tokenUsers: IObject[] = [];
  loading: boolean = false;

  get action(): PlaceActionType {
    return this.$route.query.action as PlaceActionType;
  }
  get currentUser() {
    return sessionStore.currentUser;
  }

  mounted() {
    this.fetchInstance();
  }

  async fetchInstance() {
    try {
      this.loading = true;
      const { data } = await instanceStore.find(+this.$route.params.id);
      this.currentToken = data.current_token!;
      this.workflowId = data.workflow_id;
      this.tokenUsers = (this.currentToken.options.users || []).map((o: any) => ({
        ...o,
        label: o.name,
        value: o.id,
      }));
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  submit() {
    this.forward();
  }
  async forward() {
    try {
      if (!this.selectedTeacherId) {
        this.$message.error('请选择转办人员');
        return;
      }
      this.loading = true;
      await tokenModel.assign(this.currentToken.id, {
        operator_id: Array.isArray(this.selectedTeacherId) ? this.selectedTeacherId[0] : this.selectedTeacherId,
        operator_type: 'Teacher',
      });
      this.selectorVisible = false;
      this.loading = false;
      this.$message.success('转办成功');
      this.back();
    } catch (error) {
      this.loading = false;
    }
  }

  back() {
    this.$router.back();
  }
}
</script>

<template lang="pug">
.container
  van-notice-bar(wrapable :scrollable="false")
    | 当前进度：{{ currentToken.place_name }}

  van-cell-group.group
    TeacherField(
      v-model="selectedTeacherId"
      label="选择转办人员"
      placeholder="请选择转办人员"
      :required="true"
      :disabled="loading"
    )
  .actions
    van-button(
      type="info"
      @click="submit"
      :loading="loading"
      :disabled="!selectedTeacherId"
    )
      | 提交
    van-button(@click="back")
      | 取消
</template>

<style lang="stylus" scoped>
.container
  .group
    margin-top 30px
  .avatar
    margin-right 8px
  .actions
    margin-top 15px
    padding 15px
    button
      margin-bottom 10px
      width 100%
</style>
