<template lang="pug">
NavContainer(:title="instanceTitle" :loading="instanceStore.loading" homePath="/bpm/instances")
  templateFormatter(
    ref='templateFormatter',
    v-model='formattedTemplate'
    :payload='payload'
    :template='template'
  )
  Empty(desc="记录不存在" v-if="!instance.id")
  .finance-instance-show(v-else)
    .content
      //- base info
      van-cell(title="流程状态" :border="false" center)
        .tag(:class="stateMap[instance.state].class")
          | {{ stateMap[instance.state].label }}
      van-cell(title="当前进度" :value="currentToken.place_name" @click="showTimeline" :border="false" is-link)
      van-cell(:border="false" title="单号" :value="instance.seq")
      van-cell(:border="false" title="创建日期" :value="instance.created_at | format")
      van-cell(:border="false" title="发起人" :value="instance.creator_name")
      van-cell(:border="false" title="部门" :value="instance.creator_department_name")
      template(v-if="instance.creator_type === 'Student'")
        van-cell(:border="false" title="学号" :value="instance.creator_code")
      template(v-else)
        van-cell(:border="false" title="工号" :value="instance.creator_code")
      //- template form
      TemplateFormViewer(
        :formData="payload"
        :template="template"
        :editable="formEditable"
        :instance="instance"
      )
      //- flowable
      FlowableVoucher(:instance="instance" v-if="instance.flowable_type === 'Finance::Voucher'")
      ActivityMeetingCard(:record="instance.flowable_info" v-if="instance.flowable_type === 'Meeting::Activity'")
      //- tokens
      van-collapse.tokens(v-model="tokenCollapse" ref="timeline")
        van-collapse-item(title="审批流程" name="tokens")
          InstanceTimeline(:tokens="instance.tokens")
      //- token payload
      van-collapse.tokens(v-model="tokenPayloadCollapse" ref="tokenPayload")
        van-collapse-item(title="审批数据" name="tokenPayload")
          TemplateFormViewer(
            v-for="token in payloadTokens"
            :key="token.id"
            :title="`${token.name} - ${token.operator_name}`"
            :template="token.place_form.fields || []"
            :formData="token.token_payload || {}")
    .footer
      van-button(
        v-if="enableActionsMap.edit && formEditable"
        size="small"
        type="warning"
        :to="`/bpm/workflows/${workflowId}/instances/${instance.id}/edit`")
        | 编辑表单
      //- assign
      template(v-if="enableActionsMap.assign")
        van-button(
          v-if="currentToken.type.includes('Approval')"
          size="small"
          type="info"
          :to="`/bpm/instances/${instance.id}/assign`")
          | {{ placeActionConfig.assign.name }}
        van-button(
          v-else
          size="small"
          type="info"
          :to="`/bpm/instances/${instance.id}/assign_receivers`")
          | {{ placeActionConfig.assign.name }}
      //- 发起人首次提交
      van-button(
        v-if="enableActionsMap.submit"
        size="small"
        type="info"
        @click="confirmFire(placeActionConfig.submit.name, placeActionConfig.submit.desc, 'submit')"
        :loading="loading")
        | {{ placeActionConfig.submit.name }}
      //- 他人审批
      van-button(
        v-if="enableActionsMap.accept && isValidate"
        size="small"
        type="info"
        :to="`/bpm/instances/${instance.id}/approve?action=accept`")
        | {{ placeActionConfig.accept.name }}
      van-button(
        v-if="enableActionsMap.forward"
        size="small"
        type="info"
        :to="`/bpm/instances/${instance.id}/forward?action=forward`")
        | {{ placeActionConfig.forward.name }}
      van-button(
        v-if="enableActionsMap.reject"
        size="small"
        type="danger"
        :to="`/bpm/instances/${instance.id}/approve?action=reject`")
        | {{ placeActionConfig.reject.name }}
      van-button(
        v-if="enableActionsMap.fail"
        size="small"
        type="danger"
        :to="`/bpm/instances/${instance.id}/approve?action=fail`")
        | {{ placeActionConfig.fail.name }}
      van-button(
        v-if="enableActionsMap.recall"
        size="small"
        type="danger"
        @click="confirmFire(placeActionConfig.recall.name, placeActionConfig.recall.desc, 'recall')")
        | {{ placeActionConfig.recall.name }}
      van-button(
        v-if="enableActionsMap.terminate"
        size="small"
        type="danger"
        @click="confirmFire(placeActionConfig.terminate.name, placeActionConfig.terminate.desc, 'terminate')")
        | {{ placeActionConfig.terminate.name }}
</template>

<script lang="ts">
import { Dialog } from 'vant';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import instanceStore from '@/store/modules/bpm/instance.store';
import instance from '@/models/bpm/instance';
import tokenModel, { IToken, TokenTypes } from '@/models/bpm/token';
import workflowModel, { getDefaultPlaceActionConfig, PlaceActionType } from '@/models/bpm/workflow';
import sessionStore from '@/store/modules/session.store';
import InstanceTimeline from '@/components/bpm/Timeline.vue';
// form
import TemplateFormViewer from '@/components/form/TemplateFormViewer.vue';
// flowable record
import { IFormItem } from '@/interfaces/IFormItem';
import TemplateFormatter from '@/components/form/TemplateFormatter.vue';
import FlowableVoucher from './flowable/Voucher.vue';
import ActivityMeetingCard from '../../../components/conference/ActivityMeetingCard.vue';

@Component({
  components: {
    InstanceTimeline,
    FlowableVoucher,
    ActivityMeetingCard,
    TemplateFormViewer,
    TemplateFormatter,
  },
})
export default class FinanceInstancesShow extends Vue {
  loading: boolean = false;
  tokenCollapse: any = ['tokens'];
  tokenPayloadCollapse: any = ['tokenPayload'];
  workflowName: string = '审批详情';
  instanceTitle: string = '审批详情';
  // token
  enableActionsMap: IObject = {};
  currentToken: IToken = {
    place: {},
    options: {
      users: [],
    },
  };
  // workflow
  workflowId: number | null = null;
  payload: IObject = {};
  template: IFormItem[] = [];
  // 表单
  formEditable: boolean = false;
  isValidate: boolean = false;

  formattedTemplate: IFormItem[] = [];

  get instanceStore() {
    return instanceStore;
  }
  get instance() {
    return this.instanceStore.record;
  }
  get stateMap() {
    return instance.stateMap;
  }
  get TokenTypes() {
    return TokenTypes;
  }
  get placeActionConfig() {
    return {
      ...getDefaultPlaceActionConfig(),
      ...this.currentToken.action_alias,
    };
  }
  get payloadTokens() {
    return (this.instance.tokens || []).filter(
      (t: IToken) => t.place_form && t.place_form.fields && t.place_form.fields.length,
    );
  }
  get formViewerEditable() {
    const { edit, accept, reject, submit, forward } = this.enableActionsMap;
    return edit || accept || reject || submit || forward;
  }

  mounted() {
    this.fetchInstance();
  }

  async fetchInstance() {
    const { data } = await this.instanceStore.find(+this.$route.params.id);
    this.currentToken = data.current_token!;
    this.enableActionsMap = data.enableActionsMap;
    this.workflowId = data.workflow_id;
    this.payload = data.payload;
    this.workflowName = data.workflow_name;
    this.instanceTitle = `${data.creator_name}提交的${data.workflow_name}`;
    this.fetchTokenForm(data.workflow_id, data.current_token.id);
  }

  async fetchTokenForm(workflowId: number, tokenId: number) {
    try {
      const { token, formTemplate, formEditable } = await workflowModel.findTokenForm(workflowId, tokenId);
      this.template = formTemplate;
      this.currentToken = token;
      this.formEditable = (this.enableActionsMap.submit || this.enableActionsMap.accept) && formEditable;
      this.$nextTick(() => {
        this.validateForm();
      });
    } catch (error) {
      this.$message.error('表单验证出现异常');
    }
  }

  validateForm() {
    try {
      workflowModel.validateForm(this.payload, this.formattedTemplate, {
        success: () => {
          this.isValidate = true;
        },
        fail: () => {
          this.isValidate = false;
        },
      });
    } catch (error) {
      this.$message.error('表单验证出现异常');
    }
  }

  confirmFire(title: string, desc: string, method: PlaceActionType) {
    Dialog.confirm({
      title,
      message: desc,
    })
      .then(() => {
        this.validateForm();
        if (this.isValidate || method !== 'submit') {
          this.fire(method);
        } else {
          this.$message.error('请完善表单后再进行提交');
        }
      })
      .catch(() => {});
  }

  async fire(action: PlaceActionType) {
    try {
      this.loading = true;
      await tokenModel.fire(action, this.currentToken.id, '');
      this.loading = false;
      this.$message.success('操作成功');
      this.fetchInstance();
    } catch (error) {
      this.loading = false;
    }
  }

  showTimeline() {
    if ((this.$refs.timeline as any).$el) {
      (this.$refs.timeline as any).$el.scrollIntoView();
    }
  }
}
</script>

<style lang="stylus" scoped>
.finance-instance-show
  display flex
  flex-direction column
  height 100%
  .content
    overflow auto
    height 100%
    .title
      padding 0px 16px
    .tokens
      margin-top 16px
  .footer
    flex-shrink 0
    padding 10px 16px 2px
    border-top 1px solid #E8E8E8
    background #fff
    text-align right
    button
      margin-left 8px
      margin-bottom 8px
      &:first-child
        margin-left 0
    &:empty
      padding 0
</style>
