<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import tokenModel, { IToken } from '@/models/bpm/token';
import instanceStore from '@/store/modules/bpm/instance.store';
import { ITeacher } from '@/models/teaching/teacher';
import { PlaceActionType } from '@/models/bpm/workflow';

@Component({
  components: {},
})
export default class InstanceAssignReceivers extends Vue {
  currentToken: IToken = {
    place: {},
    options: {
      users: [],
    },
  };
  loading: boolean = false;
  defaultOperators: ITeacher[] = [];
  selectedContacts: ITeacher[] = [];

  mounted() {
    this.fetchInstance();
  }

  async fetchInstance() {
    try {
      this.loading = true;
      const { data } = await instanceStore.find(+this.$route.params.id);
      this.currentToken = data.current_token!;
      this.defaultOperators = (this.currentToken.options.users || []).map((o: any) => ({
        ...o,
        label: o.name,
        value: o.id,
      }));
      this.selectedContacts = this.defaultOperators.concat();
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  async assign() {
    try {
      this.loading = true;
      const members = this.selectedContacts.map(({ id, name, code }) => ({
        id,
        name,
        code,
        type: 'Teacher',
      }));
      await tokenModel.assign(this.currentToken.id, {
        options: {
          users: members,
        },
      });
      this.loading = false;
      this.$message.success('指派成功');
      this.back();
    } catch (error) {
      this.loading = false;
    }
  }
  back() {
    this.$router.back();
  }
}
</script>

<template lang="pug">
.container
  Loading(v-if="loading")
  van-nav-bar(
    title="选择抄送人"
    left-text="返回"
    left-arrow
    fixed
    @click-left="$router.back()")
  van-notice-bar(wrapable :scrollable="false")
    | 当前进度：{{ currentToken.place_name }}
  van-cell-group.group
    ContactField(
      label="选择抄送人"
      v-model="selectedContacts"
      :multiple="true")

  .actions
    van-button(
      type="info"
      @click="assign"
      :loading="loading"
      :disabled="selectedContacts.length === 0")
      | 提交
    van-button(@click="back")
      | 取消
</template>

<style lang="stylus" scoped>
.container
  padding-top 46px
  .group
    margin-top 30px
  .avatar
    margin-right 8px
  .actions
    margin-top 15px
    padding 15px
    button
      margin-bottom 10px
      width 100%
</style>
