import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/bpm/workflows/:workflowId/instances',
    name: 'workflow_instances',
    component: () => import(/* webpackChunkName: "bpm_instances_index" */ './Index.vue'),
    meta: {
      title: '业务审批记录',
      requireAuth: true,
      roles: ['Teacher', 'Student'],
    },
  },
  {
    path: '/bpm/instances',
    name: 'instances_index',
    component: () => import(/* webpackChunkName: "bpm_instances_index" */ './Index.vue'),
    meta: {
      title: '所有审批记录',
      requireAuth: true,
      roles: ['Teacher', 'Student'],
    },
  },
  {
    path: '/bpm/workflows/:workflowId/instances/:id/edit',
    name: 'instances_edit',
    component: () => import(/* webpackChunkName: "bpm_instances_form" */ './Form.vue'),
    meta: {
      title: '编辑表单',
      requireAuth: true,
      roles: ['Teacher', 'Student'],
    },
  },
  {
    path: '/bpm/workflows/:workflowId/instances/new',
    name: 'workflow_instances_new',
    component: () => import(/* webpackChunkName: "bpm_instances_form" */ './Form.vue'),
    meta: {
      title: '发起审批',
      requireAuth: true,
      roles: ['Teacher', 'Student'],
    },
  },
  {
    path: '/bpm/instances/:id',
    name: 'bpm_instances_show',
    component: () => import(/* webpackChunkName: "bpm_instances_show" */ './Show.vue'),
    meta: {
      title: '审批详情',
      requireAuth: true,
      roles: ['Teacher', 'Student'],
    },
  },
  {
    path: '/bpm/teacher/instances/:id', // 兼容过去的微信推送链接，2020-01-18 添加注释
    name: 'bpm_teacher_instances_show',
    component: () => import(/* webpackChunkName: "bpm_instances_show" */ './Show.vue'),
    meta: {
      title: '审批详情',
      requireAuth: true,
      roles: ['Teacher', 'Student'],
    },
  },
  {
    path: '/bpm/instances/:id/approve',
    name: 'bpm_instances_approve',
    component: () => import(/* webpackChunkName: "bpm_instances_show" */ './Approve.vue'),
    meta: {
      title: '审批',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/bpm/instances/:id/assign',
    name: 'bpm_instances_assign',
    component: () => import(/* webpackChunkName: "bpm_instances_show" */ './Assign.vue'),
    meta: {
      title: '选择审批人',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/bpm/instances/:id/forward',
    name: 'bpm_instances_forward',
    component: () => import(/* webpackChunkName: "bpm_instances_show" */ './Forward.vue'),
    meta: {
      title: '选择转办人',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
  {
    path: '/bpm/instances/:id/assign_receivers',
    name: 'bpm_instances_assign_receivers',
    component: () => import(/* webpackChunkName: "bpm_instances_show" */ './AssignReceivers.vue'),
    meta: {
      title: '选择抄送人',
      requireAuth: true,
      roles: ['Teacher'],
    },
  },
];

export default routes;
