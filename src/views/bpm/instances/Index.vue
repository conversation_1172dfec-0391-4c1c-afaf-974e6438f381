<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import instanceModel, { IInstance, InstanceType } from '@/models/bpm/instance';
import instanceStore from '@/store/modules/bpm/instance.store';
import sessionStore from '@/store/modules/session.store';
import InstanceCell from '@/components/bpm/InstanceCell.vue';

@Component({
  components: {
    InstanceCell,
  },
})
export default class BpmInstancesIndex extends Vue {
  query: IObject = {};
  activeState: string = 'todo';
  tabs: IObject[] = [
    { text: '待我提交', key: 'todo' },
    { text: '待我审批', key: 'approving' },
    { text: '我发起的', key: 'created' },
    { text: '我已审批', key: 'approved' },
    { text: '抄送我的', key: 'notified' },
  ];

  get instanceStore() {
    return instanceStore;
  }
  get workflowId() {
    return this.$route.params.workflowId;
  }
  get state(): string {
    return (this.$route.query.state || 'todo') as string;
  }
  get instanceType() {
    return this.$route.query.type || InstanceType.Bpm;
  }
  get stateMap() {
    return instanceModel.stateMap;
  }

  created() {
    instanceStore.reset();
    this.activeState = this.state;
    this.fetchInstances(1);
  }
  onStateChange() {
    if (this.activeState === this.state) return;
    this.$router.replace({ query: { state: this.activeState, type: this.instanceType } });
    instanceStore.reset();
    this.fetchInstances(1);
  }
  async fetchInstances(page = 1) {
    const { id } = sessionStore.currentUser;
    const { type } = sessionStore.account;
    const params = {
      page,
      q: {
        [this.state]: [id, type],
        type_eq: this.instanceType,
      },
    };
    if (this.workflowId) {
      await instanceStore.fetchByParent({
        parentId: this.workflowId,
        ...params,
      });
    } else {
      await instanceStore.fetch(params);
    }
  }
  async onListFetch(page: number, done: any) {
    try {
      await this.fetchInstances(page);
      done();
    } catch (error) {
      done();
    }
  }
  showInstance(instance: IInstance) {
    if (instance.workflow_id) {
      this.$router.push(`/bpm/instances/${instance.id}`);
    }
  }
  onNew() {
    this.$router.push(`/bpm/workflows/${this.workflowId}/instances/new?type=${this.instanceType}`);
  }
}
</script>

<template lang="pug">
NavContainer(title="业务审批记录")
  .container
    Tabs.tabs(v-model="activeState" :tabs="tabs" @change="onStateChange")
    ListView(
      :data="instanceStore.records"
      :loading="instanceStore.loading"
      :pullup="!instanceStore.finish"
      :pulldown="true"
      emptyText="暂无记录"
      @loadMore="fetchInstances(instanceStore.currentPage + 1)"
      @refresh="fetchInstances(1)")
      .attendances
        InstanceCell.cell(
          v-for="instance in instanceStore.records"
          :key="instance.id"
          :instance="instance"
          @click="showInstance(instance)")

    .float-button(v-if="workflowId" @click="onNew")
      van-icon(name="plus")
</template>

<style lang="stylus" scoped>
.container
  position relative
  padding-top 46px
  height 100%
  .tabs
    position absolute
    top 0
    left 0
    z-index 10
    width 100%
  .attendances
    padding 12px 12px 1px
    .cell
      margin-bottom 8px
  .float-button
    position fixed
    right 20px
    bottom 20px
    z-index 1000
    display flex
    justify-content center
    align-items center
    width 48px
    height 48px
    border-radius 50%
    background #ED5128
    box-shadow -1px 1px 8px 2px rgba(0, 0, 0, 0.2)
    color #ffffff
    font-size 24px
</style>
