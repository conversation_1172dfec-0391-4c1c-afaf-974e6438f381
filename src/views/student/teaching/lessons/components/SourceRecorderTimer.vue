<script lang="ts">
/**
 * 课程时间记录器
 * 每分钟更新 recorder
 */
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { RecorderService, IRecorder, SourceType } from '@/service/recorder';
import lessonModel, { ILesson } from '@/models/teaching/lesson';

@Component({
  components: {},
})
export default class SourceRecorderTimer extends Vue {
  @Prop({ type: String, validator: val => [SourceType.Lesson, SourceType.LessonItem].includes(val) })
  private sourceType!: SourceType;
  @Prop({ type: Number }) sourceId!: number;
  @Prop({ type: Number }) lessonId!: number;
  @Prop({ type: Boolean, default: true }) visible!: boolean;
  @Prop({ type: Function }) afterRecordCallback!: (recorder: IRecorder) => any;

  lesson: ILesson = {};
  recorder: IRecorder = {};
  latestTotalSeconds: number = 0;
  recorderTimer: any = null;
  duration: number = 60000 * 2;

  // @Watch('sourceId', { immediate: true })
  // onSourceChange() {
  //   this.start();
  // }

  // beforeDestroy() {
  //   clearInterval(this.recorderTimer);
  //   this.findRecorder();
  // }

  start() {
    if (this.sourceId && this.sourceType && this.lessonId) {
      this.fetchLesson();
    } else {
      clearInterval(this.recorderTimer);
    }
  }

  async fetchLesson() {
    try {
      lessonModel.setRole('student');
      const { data } = await lessonModel.find(this.lessonId);
      this.lesson = data;
      this.findRecorder();
      this.recorderTimer = setInterval(this.findRecorder, this.duration);
    } catch (error) {
      this.$message.warning('获取课程信息异常');
    }
  }

  async findRecorder() {
    const { data } = await RecorderService.student.find({
      sourceType: this.sourceType,
      sourceId: this.sourceId,
      courseId: this.lesson.course_id!,
      lessonId: this.lesson.id!,
    });
    this.recorder = data;
    // 更新更新秒表
    if (!this.latestTotalSeconds) {
      (this.$refs.timer as any).start();
      this.latestTotalSeconds = data.total_in_sec || 0;
    }
    this.$emit('afterRecord', data);
    if (this.afterRecordCallback) {
      this.afterRecordCallback(data);
    }
  }
}
</script>

<template lang="pug">
//- TaTimer(
//-   ref="timer"
//-   v-show="visible"
//-   v-model="latestTotalSeconds"
//-   :auto="false")
</template>

<style lang="stylus" scoped></style>
