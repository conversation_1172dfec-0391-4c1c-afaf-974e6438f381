<template lang="pug">
NavContainer(title="作业提交情况" :loading="homeworkStore.loading")
  //- Tabs.tabs(v-model="tabIndex" :tabs="tabs")
  ListView(
    :data="homeworkStore.records"
    :loading="homeworkStore.loading"
    :pullup="!homeworkStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(homeworkStore.currentPage + 1)"
    @refresh="fetchData()")
    .reports
      van-panel.report(v-for="(item, index) in homeworkStore.records" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.title }}
        template(slot="default")
          .cell(v-if="item.state !== 'pending'")
            .key 提交时间
            .value(v-if="item.answer && item.answer.published_at")
              | {{ $dayjs(item.answer.published_at).format('YYYY/MM/DD HH:mm') }}
          .cell
            .key 提交截止时间
            .value
              | {{ item.end_at ? $dayjs(item.end_at).format('YYYY/MM/DD HH:mm') : '-' }}
          .cell(v-if="item.answer && item.answer.comment_count ")
            .key 评论数
            .value {{ item.answer.comment_count }}
        template(slot="footer")
          .state
            van-tag(color="#F0F9F2" text-color="#6DC37D" v-if="item.answer_state === 'scored'") 已评分
            van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else-if="item.answer_state === 'published'") 已上传
            van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else) 未提交
          template(v-if="item.answer_state !== 'pending'")
            van-tag(color="#F0F9F2" text-color="#6DC37D" v-if="item.answer_state === 'scored'")
              | 评分：{{ item.answer && +item.answer.score }}分
            van-tag(color="#f5f5f5" text-color="#A6A6A6" v-else) 等待打分

  //- lesson 时长统计埋点
  SourceRecorderTimer(
    sourceType="Teaching::Lesson"
    :sourceId="lessonId"
    :lessonId="lessonId"
    :visible="false")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import homeworkStore from '@/store/modules/teaching/homework.store';
import SourceRecorderTimer from '../components/SourceRecorderTimer.vue';

@Component({
  components: {
    SourceRecorderTimer,
  },
})
export default class Reports extends Vue {
  private queryObject: object = {};
  private tabIndex: string = 'published';
  private reportStore: any = {
    info: {},
    records: [],
  };

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get variables() {
    return [
      'user_of_Student_type_name',
      'user_of_Student_type_code',
      'user_of_Student_type_major_name',
      'user_of_Student_type_adminclass_name',
    ];
  }
  get role() {
    return this.$tools.getRole();
  }
  get tabs() {
    return [
      {
        text: '待评分',
        key: 'published',
      },
      {
        text: '已评分',
        key: 'scored',
      },
      {
        text: '未提交',
        key: 'pending',
      },
    ];
  }
  get homeworkStore() {
    return homeworkStore || {};
  }

  @Watch('queryObject')
  public watchQuery() {
    this.fetchData();
  }

  @Watch('tabIndex')
  public watchChange() {
    this.fetchData();
  }

  public mounted() {
    homeworkStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.lessonId,
      shouldAppend: true,
      q: {
        s: ['created_at asc'],
        state_eq: this.tabIndex,
      },
    };
    homeworkStore.fetchByParent(params);
  }

  public onShow(val: any) {
    this.$router.push(`/teaching/student/lessons/${this.lessonId}/reports/${val.id}`);
  }
}
</script>

<style lang="stylus" scoped>
.reports
  padding 10px 16px
  width 100%
  background #f5f5f5
  .report
    margin-bottom 12px
    padding 16px 16px 12px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 90px
    .state
      display inline
      margin-right 10px

.van-panel__footer
  padding 12px 0px 0px
</style>
