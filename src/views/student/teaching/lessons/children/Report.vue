<template lang="pug">
NavContainer.pannel(:title="answer.id ? '' : '作业提交情况'" :loading="homeworkStore.loading")
  template(v-if="!answer.id")
    Empty(desc="暂不可提交作业")
  template(v-else)
    Tabs(v-model="tabIndex" :tabs="tabs" slot="title")
    template(v-if="tabIndex === 'Homework'")
      .steps
        van-steps(direction="vertical" :active="currentIndex")
          van-step(v-for="(item, index) in steps" :key="index" active-color="#3DA8F5")
            template(slot="active-icon")
              .point.active {{ index + 1 }}
            template(slot="inactive-icon")
              .point.inactive(v-if="currentIndex === 0") {{ index + 1 }}
              .point.success(v-else)
                van-icon(name="success" color="#3DA8F5")
            .module
              van-cell(style="padding: 0px 10px")
                .title {{ item.title }}
                template(v-if="answer.state !== 'scored' && item.value === 'Homework'")
                  van-icon(name="edit" slot="right-icon" @click="onForm")
              template(v-if="item.value === 'Task'")
                Attachments(
                  :attachments="files"
                  :display="true"
                  :showActions="false")
                RichText(:value="report.body")
              template(v-else-if="item.value === 'Homework'")
                template(v-if="answerFiles.length > 0 || answer.body")
                  Attachments(
                    :attachments="answerFiles"
                    :display="true"
                    :showActions="false")
                  RichText(:value="answer.body")
              template(v-else-if="item.value === 'Score'")
                .score-cell
                  span 评分
                  strong(:class="+answer.score > 0 ? 'text-primary' : 'text-gray'")
                    | {{ +answer.score ? +answer.score + '分' : '等待评分' }}
    template(v-else)
      CommentPanel(
        :commentableId="Number(this.answer.id)"
        commentType="Tree"
        commentableType="Report"
        indexPath="/comm/user/reports"
        deletePath="/comm/user"
        :useCdn="true"
        :showHeader="false"
        @refresh="fetchData")

  //- lesson 时长统计埋点
  SourceRecorderTimer(
    sourceType="Teaching::Lesson"
    :sourceId="lessonId"
    :lessonId="lessonId"
    :visible="false")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import homeworkStore from '@/store/modules/teaching/homework.store';
import CommentPanel from '@/components/comment/CommentPanel.vue';
import SourceRecorderTimer from '../components/SourceRecorderTimer.vue';

@Component({
  components: {
    CommentPanel,
    SourceRecorderTimer,
  },
})
export default class Report extends Vue {
  private tabIndex: string = 'Homework';
  private answer: any = {
    state: 'pending',
    attachments: {},
    body: '',
    files: [],
  };

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get role() {
    return this.$tools.getRole();
  }
  get tabs() {
    return [
      {
        text: '作业',
        key: 'Homework',
      },
      {
        text: this.answer.id ? `评论(${this.answer.comment_count})` : '评论',
        key: 'Comment',
      },
    ];
  }
  get steps() {
    return [
      {
        title: '课堂作业',
        value: 'Task',
      },
      {
        title: '上传作业',
        value: 'Homework',
      },
      {
        title: '教师评分',
        value: 'Score',
      },
    ];
  }
  get homeworkStore() {
    return homeworkStore || {};
  }
  get report() {
    return homeworkStore.record || {};
  }
  get files() {
    return this.report.files || [];
  }
  get answerFiles() {
    return this.answer.files || [];
  }
  get disabled() {
    return this.answerFiles.length === 0 && !this.answer.body;
  }
  get currentIndex() {
    return this.answer.state === 'scored' ? 2 : 1;
  }

  public created() {
    homeworkStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData() {
    const { data } = await homeworkStore.find(this.$route.params.reportId);
    this.answer = data.answer;
  }

  public onForm() {
    this.$router.push(`/teaching/student/lessons/${this.lessonId}/reports/${this.$route.params.reportId}/report_form`);
  }
}
</script>

<style lang="stylus" scoped>
.pannel
  width 100%
  height 100%
  background #fff
  .steps
    padding 14px 10px
    width 100%
    .title
      color #383838
      font-size 15px
      line-height 24px
    .ck-content
      margin-top 10px
      color #383838
    .score-cell
      display flex
      justify-content space-between
      align-items center
      margin-top 10px
      width 100%
      color #383838
    .point
      display flex
      justify-content center
      align-items center
      margin-top 8px
      width 28px
      height 28px
      border-radius 50%
      font-weight 600
      font-size 16px
    .active
      background #3DA8F5
      color #fff
    .inactive
      border 1px #CCCCCC solid
      background #fff
      color #CCCCCC
    .success
      border 1px #3DA8F5 solid
      background #fff
    .module
      padding 0px 0px 40px 4px
      width 100%
      .title
        color #383838
        font-size 15px
        line-height 24px
      .van-cell
        display flex
        align-items center

.van-icon
  color #A6A6A6
  font-size 16px
</style>
