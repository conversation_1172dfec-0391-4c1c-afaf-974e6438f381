<template lang="pug">
NavContainer(title="作业提交" :loading="homeworkStore.loading")
  .form
    .form-item
      .form-item-label 附件
      FileUploader(
        :value="reportFiles"
        label="上传附件"
        :useCdn="true"
        :multiple="true"
        style="padding: 0px"
        @change="onUploader")
    .form-item
      .form-item-label 内容
      van-field(
        v-model="report.body"
        rows="1"
        autosize
        type="textarea"
        placeholder="输入内容")
    .footer
      van-button(@click="$router.go(-1)") 取消
      van-button(
        type="info"
        :loading="reportStore.loading"
        :disabled="disabled || reportStore.loading"
        @click="onSubmit") 发布

  //- lesson 时长统计埋点
  SourceRecorderTimer(
    sourceType="Teaching::Lesson"
    :sourceId="lessonId"
    :lessonId="lessonId"
    :visible="false")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import homeworkStore from '@/store/modules/teaching/homework.store';
import reportStore from '@/store/modules/comm/report.store';
import SourceRecorderTimer from '../components/SourceRecorderTimer.vue';

@Component({
  components: {
    SourceRecorderTimer,
  },
})
export default class ReportForm extends Vue {
  private uploadState: boolean = true;
  private report: any = {
    attachments: {},
    body: '',
    files: [],
  };

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get role() {
    return this.$tools.getRole();
  }
  get homeworkStore() {
    return homeworkStore || {};
  }
  get reportStore() {
    return reportStore || {};
  }
  get reportFiles() {
    return this.report.files || [];
  }
  get disabled() {
    return this.reportFiles.length === 0 && !this.report.body;
  }

  public created() {
    homeworkStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData() {
    const { data } = await homeworkStore.find(this.$route.params.reportId);
    this.report = data.answer;
  }

  public onSubmit() {
    if (!this.uploadState) {
      this.$message.warning(this.$tools.fileVerifyMessage());
      return;
    }
    const val = {
      ...this.report,
      state: 'published',
      noCompare: true,
    };
    this.update(val);
  }

  public async update(val: any) {
    try {
      await reportStore.update(val);
      this.$message.success('操作成功！');
      this.$router.go(-1);
    } catch (error) {
      this.$message.error('操作失败');
    }
  }

  public onUploader(fileItems: any[], statusFiles: any, allSettled: boolean) {
    this.report.attachments = {
      files: statusFiles.done || [],
    };
    this.report.files = fileItems;
    this.uploadState = allSettled;
  }
}
</script>

<style lang="stylus" scoped>
.form
  padding 20px 14px 70px
  width 100%
  background #fff
  .form-item, .form-item-label
    padding-bottom 14px
  .van-field
    border 1px #e8e8e8 solid
  .footer
    position fixed
    bottom 0px
    left 0px
    display flex
    justify-content flex-end
    padding 10px 20px
    width 100%
    border-top 1px #e8e8e8 solid
    background #fff
    .van-button
      margin-left 10px
      width 100px
      border 1px #e8e8e8 solid
</style>
