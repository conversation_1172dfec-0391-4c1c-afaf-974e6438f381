<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { AnswerSetService, IAnswerSet, MetaCatalog, AnswerSetState } from '@/service/answer_set';
import { QuestionTypes, IQuestion, QuestionTypeMap } from '@/service/question';
import { IAnswer, AnswerService, UserOwnAnswer } from '@/service/answer';
import { baseStore } from '@/store/modules/base.store';
import { ExamActivity, IActivity } from '@/service/exam_activity';

@Component({
  components: {},
})
export default class TopicQuestionSetExam extends Vue {
  examActivity: IActivity = {
    time_infos: { to_end: 0, to_start: 0, to_start_off: 0 },
  };
  answerSet: IAnswerSet = {};
  answers: IAnswer[] = [];
  questionAnswersMap: Map<number, IAnswer> = new Map();
  catalogs: MetaCatalog[] = [];
  nowCatalog: MetaCatalog | null = null;
  nowCatalogIndex: number = 0;
  nowAnswers: IAnswer[] = [];
  nowIndex: number = 0;
  isFileUploaded: boolean = true;
  loading: boolean = false;
  // 定位
  logError: boolean = false;
  // 元素控制
  submitDisabled: boolean = false; // 用于手动控制页面元素的禁用状态
  nextLoading: boolean = false; // 切换题目 loading, 延迟加载新题，确保旧题更新事件触发
  preLoading: boolean = false; // 切换题目 loading, 延迟加载新题，确保旧题更新事件触发
  updateAnswerLoading: boolean = false;

  get activityId() {
    return +this.$route.params.activityId;
  }
  get answerSetId() {
    return +this.$route.params.answerSetId;
  }
  get QuestionTypes() {
    return QuestionTypes;
  }
  get QuestionTypeMap() {
    return QuestionTypeMap;
  }
  // 未开始 || 已结束 || 已提交 || 已阅卷 || 定位异常
  get disableExam() {
    return false;
    // const { to_start, to_start_off, to_end } = this.examActivity.time_infos || {
    //   to_start: 0,
    //   to_start_off: 0,
    //   to_end: 0,
    // };
    // const { submitDisabled, answerSet, logError } = this;
    // return (
    //   submitDisabled ||
    //   logError ||
    //   to_start > 0 ||
    //   to_end === 0 ||
    //   answerSet.state === 'done' ||
    //   answerSet.state === 'checked'
    // );
  }
  get nowAnswer() {
    return this.nowAnswers[this.nowIndex];
  }
  get undoCount() {
    return this.answers.filter(a => !this.isDoneAnswer(a)).length;
  }

  mounted() {
    window.addEventListener('beforeunload', this.unload);
    this.fetchActivity();
    if (this.answerSetId) {
      this.fetchAnswerSet().then(() => {
        this.updateLocationLog();
      });
    }
  }

  beforeDestroy() {
    this.unload();
    window.removeEventListener('beforeunload', this.unload);
  }

  unload() {
    this.updateNowAnswer();
    return '';
  }
  // 记录考试位置 log
  async updateLocationLog() {
    if (this.answerSet.state === 'done' || this.answerSet.state === 'checked') return;
    try {
      ExamActivity.setLog(this.activityId);
      this.logError = false;
    } catch (error) {
      this.logError = true;
      this.$message.error('检查你的定位出现异常，无法进行考试');
    }
  }
  async fetchActivity() {
    this.loading = true;
    const { data } = await ExamActivity.find(this.activityId).finally(() => {
      this.loading = false;
    });
    this.examActivity = data;
  }

  async fetchAnswerSet() {
    try {
      this.loading = true;
      const { data } = await AnswerSetService.UserOwn.find(this.answerSetId);
      this.answerSet = data;
      this.answers = (this.answerSet.answers || []).map(AnswerService.UserOwn.getProcessedAnswer);
      this.questionAnswersMap.clear();
      this.answers.forEach(a => {
        this.questionAnswersMap.set(
          a.question_id!,
          Object.assign(a, {
            answer_meta: a.answer_meta || { value: '' },
            attachments: a.attachments || { files: [] },
            question: Object.assign(a.question, { choices: a.question!.choices || { options: [] } }),
          }),
        );
      });
      this.catalogs = data.meta ? data.meta.catalogs : [];
      this.catalogs.forEach((catalog: MetaCatalog, index: number) => {
        catalog._answers = catalog.questions.map(q =>
          Object.assign(this.questionAnswersMap.get(q.id!)!, { score: q.score }),
        );
        catalog._done = catalog._answers.filter(this.isDoneAnswer).length;
      });
      this.setCatalog(this.catalogs[0]);
      this.loading = false;
      this.$forceUpdate();
    } catch (error) {
      this.loading = false;
    }
  }
  getOptionKeyTitle(label: string, index: number) {
    const ikey = UserOwnAnswer.getOptionIndexKey(index);
    return `${ikey}. ${label}`;
  }
  // catalog====
  setCatalog(c: MetaCatalog) {
    if (!c) return;
    this.nowCatalog = c;
    this.nowCatalogIndex = this.catalogs.findIndex(catalog => catalog === c);
    this.nowAnswers = c._answers;
    this.nowIndex = 0;
  }
  onValueChange() {
    this.setNowDoneCount();
    this.updateNowAnswer();
  }
  setNowDoneCount() {
    if (this.nowCatalog) {
      this.$set(this.nowCatalog, '_done', this.nowAnswers.filter(this.isDoneAnswer).length);
      this.$forceUpdate();
    }
  }
  async updateNowAnswer() {
    if (this.nowAnswer) {
      this.updateAnswerLoading = true;
      const { id, value, attachments = { files: [] } } = this.nowAnswer;
      await AnswerService.UserOwn.update(this.answerSet.id!, {
        id,
        value: value ? String(value) : '',
        attachments,
      }).finally(() => {
        this.updateAnswerLoading = false;
      });
    }
  }
  onTimerChange(timeData: any) {
    const { days, hours, minutes, seconds } = timeData;
    const isDoneExceptMins = days === 0 && hours === 0 && seconds === 0;
    if (isDoneExceptMins && minutes === 15) {
      this.onFinish();
      this.$message.warning('距离考试结束还有 15 分钟');
    } else if (isDoneExceptMins && minutes === 5) {
      this.$message.warning('距离考试结束还有 5 分钟');
    } else if (isDoneExceptMins && minutes === 0) {
      this.onFinish();
    }
  }
  async onFinish() {
    const { start_at, duration_in_min } = this.examActivity;
    const endAt = this.$dayjs(start_at).add(+duration_in_min!, 'minute');
    if (this.answerSet.id && this.$dayjs().isAfter(endAt)) {
      this.submitDisabled = true;
      this.submit();
      this.$dialog
        .alert({
          title: '提示',
          message:
            this.undoCount > 0
              ? `答题时间已到，未做${this.undoCount}题，系统已自动为您提交试卷。`
              : '答题时间已到，系统已自动为您提交试卷。',
        })
        .then(() => {
          this.$router.back();
        });
    }
  }
  async manulSubmit() {
    this.submitDisabled = true;
    this.$dialog
      .confirm({
        title: '提示',
        message:
          this.undoCount > 0 ? `还有 ${this.undoCount} 题未做，确认提交试卷吗？` : '确认提交试卷，提交后无法修改.',
      })
      .then(async () => {
        await this.submit();
        this.$message.success('提交成功');
        this.$router.back();
      })
      .catch(() => {
        this.submitDisabled = false;
      });
  }
  async submit() {
    try {
      this.loading = true;
      this.submitDisabled = true;
      await this.updateNowAnswer();
      await AnswerSetService.UserOwn.update({ id: this.answerSet.id, state: 'done' });
      this.loading = false;
      return;
    } catch (error) {
      this.$message.error('提交失败');
      this.submitDisabled = false;
      this.loading = false;
    }
  }
  preQuestion() {
    if (!this.isFileUploaded) {
      this.$message.warning('有文件正在上传或上传失败，请先处理');
      return;
    }
    this.preLoading = true;
    setTimeout(() => {
      if (this.nowIndex > 0) {
        this.nowIndex -= 1;
      } else if (this.nowCatalogIndex >= 0) {
        this.setCatalog(this.catalogs[this.nowCatalogIndex - 1]);
        this.nowIndex = this.nowAnswers.length - 1;
      }
      this.preLoading = false;
    }, 300);
  }
  nextQuestion() {
    if (!this.isFileUploaded) {
      this.$message.warning('有文件正在上传或上传失败，请先处理');
      return;
    }
    this.nextLoading = true;
    setTimeout(() => {
      if (this.nowIndex < this.nowAnswers.length - 1) {
        this.nowIndex += 1;
      } else if (this.nowCatalogIndex < this.catalogs.length - 1) {
        this.setCatalog(this.catalogs[this.nowCatalogIndex + 1]);
      }
      this.nextLoading = false;
    }, 300);
  }
  setSingleTypeAnswerValue(answer: any, optionKey: string) {
    if (this.disableExam) return;
    this.$set(answer, 'value', optionKey);
  }
  setMultiTypeAnswerValue(answer: any, optionKey: string) {
    if (this.disableExam) return;
    const checkboxKey = `checkbox_${optionKey}`;
    const refs = this.$refs[checkboxKey] as any;
    if (refs && refs[0]) {
      refs[0].toggle();
    }
  }
  onRadioChange(key: string) {
    this.$set(this.nowAnswer, 'value', key);
    this.onValueChange();
  }
  onCheckboxChange(keys: string[]) {
    this.$set(this.nowAnswer, 'value', keys);
    this.onValueChange();
  }
  isDoneAnswer(a: IAnswer) {
    return (a.value && String(a.value)) || (a.attachments && a.attachments.files.length);
  }
  onTabChange(index: number) {
    this.setCatalog(this.catalogs[index]);
  }
  getQuestionTypeText(type: QuestionTypes) {
    return this.QuestionTypeMap[type] ? this.QuestionTypeMap[type].text : '未设置题型';
  }
}
</script>

<template lang="pug">
NavContainer.container(title="试卷答题")
  .count-down(slot="right" v-if="answerSet.id && answerSet.state === 'doing'")
    van-count-down.text-danger(
      :time="(examActivity.time_infos.to_end + 1) * 1000"
      @change="onTimerChange")
  .main-content(v-loading="loading")
    van-tabs.header(v-model="nowCatalogIndex" sticky scrollspy color="#3DA8F5" @change="onTabChange")
      van-tab(
        v-for="(c, i) in catalogs"
        :key="i"
        :title="`${c.name}${c._done}/${c.questions.length}`"
        :name="i")
    .questions(v-if="nowAnswer")
      .catalog-detail(v-if="nowCatalog")
        .ck-content(v-html="nowCatalog.body" v-if="nowCatalog.body")
        Attachments.files(
          v-if="nowCatalog.attachments && Array.isArray(nowCatalog.attachments.files)"
          :attachments="nowCatalog.attachments.files"
          :display="true")
      .question-wrapper(:class="{ 'question-error': nowAnswer.isError }")
        .title
          .marked {{ nowIndex + 1 }}.【{{ getQuestionTypeText(nowAnswer.question.type) }}】
          .ck-content(v-html="nowAnswer.question.title")
        Attachments.files(
          v-if="nowAnswer.question.attachments && Array.isArray(nowAnswer.question.attachments.files)"
          :attachments="nowAnswer.question.attachments.files")
        //- 单选题
        .question(v-if="nowAnswer.question.type === QuestionTypes.single")
          van-radio-group(v-model="nowAnswer.value" :disabled="disableExam" @change="onRadioChange")
            van-cell-group.options(:border="false")
              van-cell(
                v-for="(option, j) in nowAnswer.question.choices.options"
                :key="option.key"
                :title="getOptionKeyTitle(option.value, j)"
                clickable
                @click="setSingleTypeAnswerValue(nowAnswer, option.key, j)")
                van-radio(slot="right-icon" :name="option.key")
        //- 多选题
        .question(v-else-if="nowAnswer.question.type === QuestionTypes.multiple")
          van-checkbox-group(
            v-model="nowAnswer.value"
            :disabled="disableExam"
            @change="onCheckboxChange")
            van-cell-group.options(:border="false")
              van-cell(
                v-for="(option, j) in nowAnswer.question.choices.options"
                :key="option.key"
                :title="getOptionKeyTitle(option.value, j)"
                clickable
                @click="setMultiTypeAnswerValue(nowAnswer, option.key, j)")
                van-checkbox(slot="right-icon" :name="option.key" :ref="`checkbox_${option.key}`")
        //- 填空题
        .question(v-else-if="nowAnswer.question.type === QuestionTypes.fill")
          van-field.field(
            v-model="nowAnswer.value"
            placeholder="请输入答案"
            autosize
            rows="2"
            type="textarea"
            auto-focus
            :focus="true"
            :disabled="disableExam"
            @blur="onValueChange")
        //- 简答题
        .question(v-else-if="nowAnswer.question.type === QuestionTypes.essay")
          van-field.field(
            v-model="nowAnswer.value"
            placeholder="请输入答案"
            autosize
            rows="2"
            type="textarea"
            auto-focus
            :focus="true"
            :disabled="disableExam"
            @blur="onValueChange")
        .answer-files(v-if="nowAnswer.question.type === QuestionTypes.essay")
          FileUploader(
            v-model="nowAnswer.attachments.files"
            label="上传附件"
            :disabled="disableExam"
            :multiple="true"
            :isAllSettled.sync="isFileUploaded"
            @allSettle="onValueChange")

    .footer(v-if="disableExam")
      van-button(type="info" block :disabled="true" v-if="answerSet.state === 'done'")
        | 已经交卷
      van-button(type="primary" block :disabled="true" v-else-if="answerSet.state === 'checked'")
        | 已经评分
      van-button(type="danger" block :disabled="true" v-else-if="logError")
        | 定位异常
    .footer(v-else)
      .operators
        van-button(block
          :loading="preLoading"
          :disabled="nowIndex === 0 && nowCatalogIndex === 0 || !isFileUploaded"
          @click.prevent="preQuestion")
            | 上一题
        van-button(block
          :loading="nextLoading"
          :disabled="nowIndex === nowAnswers.length - 1 && nowCatalogIndex === catalogs.length - 1 || !isFileUploaded"
          @click.prevent="nextQuestion")
          | 下一题
      van-button(
        type="info"
        block
        :disabled="!isFileUploaded"
        :loading="loading"
        @click="manulSubmit")
        | 提交
</template>

<style lang="stylus" scoped>
.container
  position relative
  background #fff
  .count-down
    display flex
    justify-content flex-end
    align-items center
    height 46px

.main-content
  position relative
  display flex
  flex-direction column
  height 100%
  background #fff
  .header
    margin-bottom 16px
    background #fff
  .questions
    flex-grow 1
    overflow auto
    padding 0 20px 0
    .catalog-detail
      margin-bottom 16px
      padding-bottom 16px
      border-bottom 1px solid #E9E9E9
      &:empty
        margin-bottom 0px
        padding-bottom 0px
        border none
    .question-error
      .question
        .options
          border-color red
    .files
      margin-top 16px
      &:empty
        margin 0
    .title
      color rgba(38, 38, 38, 0.85)
      font-weight 500
      font-size 15px
      line-height 24px
    .question
      padding 14px 0
      .field
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
      .options
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
      .answer
        display flex
        align-items flex-start
        padding 8px 16px
        .label
          flex-shrink 0
          margin-right 14px
    .answer-files
      margin 0px -16px
  .footer
    flex-shrink 0
    padding 14px 20px
    background #fff
    .operators
      display flex
      margin 0 -10px 10px
      button
        margin 0 10px
</style>
