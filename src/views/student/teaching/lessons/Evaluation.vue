<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import studentEvaluation, { IEvaluation } from '../../../../models/teaching/studentEvaluation';
import { baseStore } from '../../../../store/modules/base.store';
import lessonModel, { ILesson } from '../../../../models/teaching/lesson';
import answerModel, { IAnswer } from '../../../../models/teaching/answer';
import SourceRecorderTimer from './components/SourceRecorderTimer.vue';

@Component({
  components: {
    SourceRecorderTimer,
  },
})
export default class TeachingStudentLessonEvaluation extends Vue {
  evaluation: IEvaluation = {
    answer_set: {
      answers: [],
    },
  };
  lesson: ILesson = {};

  get loading() {
    return baseStore.loading;
  }
  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get answers() {
    return this.evaluation.answer_set!.answers || [];
  }
  get isEvaluated() {
    return this.evaluation.state === 'done';
  }
  get disabled() {
    return this.answers.some(o => !o.value);
  }

  created() {
    this.fetchLesson();
    this.fetchEvaluation();
  }
  async fetchEvaluation() {
    const { data } = await studentEvaluation.findEvaluation(this.lessonId);
    this.evaluation = data;
  }
  async fetchLesson() {
    const { data } = await lessonModel.setRole('student').find(this.lessonId);
    this.lesson = data;
  }
  setAnswerValue(answer: any, value: string) {
    if (this.isEvaluated) return;
    this.$set(answer, 'value', value);
  }
  async submit() {
    if (this.disabled || this.loading) return;
    try {
      const answerAttibutes = this.answers.map(a => ({ id: Number(a.id), value: a.value! }));
      await studentEvaluation.updateEvaluation(this.lessonId, 'done', answerAttibutes);
      this.evaluation.state = 'done';
      this.$message.success('评价成功');
    } catch (error) {
      this.$message.warning('评价失败，请稍后重试');
    }
  }
}
</script>

<template lang="pug">
NavContainer.container(title="评价" v-loading="loading")
  .lesson
    .name {{ lesson.course_set_name }}
    van-cell(:border="false" title="时间" icon="clock-o"
      :value="`${lesson.date} ${lesson.start_time}-${lesson.end_time}`")
    van-cell(:border="false" title="节数" icon="location-o" :value="`${lesson.unit_count} 节`")
    van-cell(:border="false" title="教室" icon="wap-home-o" :value="lesson.classroom_name")
    van-cell(:border="false" title="教师" icon="contact" :value="lesson.teacher_name")
  .questions
    .question(v-for="(answer, index) in answers" :key="answer.id")
      .title {{ index + 1 }}. {{ answer.question.title }}
      van-radio-group(v-model="answer.value" :disabled="disabled")
        van-cell-group.options(:border="false")
          van-cell(
            v-for="option in answer.question.choices.options"
            :key="option.key"
            :title="option.value"
            clickable
            @click="setAnswerValue(answer, option.key)")
            van-radio(slot="right-icon" :name="option.key")
    van-button(type="primary" disabled block v-if="isEvaluated")
      | 已评价
    van-button(type="info" block :disabled="disabled" :loading="loading" @click="submit" v-else)
      | 提交

  //- lesson 时长统计埋点
  SourceRecorderTimer(
    sourceType="Teaching::Lesson"
    :sourceId="lessonId"
    :lessonId="lessonId"
    :visible="false")
</template>

<style lang="stylus" scoped>
.container
  background #fff
  .lesson
    .name
      padding 8px 16px
      background #fff
      color rgba(56, 56, 56, 1)
      font-size 19px
      line-height 28px
  .questions
    padding 0 20px
    .question
      padding 20px 0
      border-top 1px solid #E9E9E9
      .title
        margin-bottom 16px
        color rgba(38, 38, 38, 0.85)
        font-weight 500
        font-size 15px
        line-height 24px
      .options
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
</style>
