<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ActivitySchedule from '@/components/teaching/ActivitySchedule.vue';
import courseActivityModel, { ICourseActivity } from '@/models/teaching/course_activity';
import { baseStore } from '../../../../store/modules/base.store';

@Component({
  components: {
    ActivitySchedule,
  },
})
export default class StudentCourseActivitiesIndex extends Vue {
  activities: ICourseActivity[] = [];

  get loading() {
    return baseStore.loading;
  }

  created() {
    this.fetchCourseActivities();
  }

  async fetchCourseActivities() {
    const { data } = await courseActivityModel.setRole('student').index({
      page: 1,
      per_page: 1020,
    });
    this.activities = data.course_activities;
  }

  public onShow(val: any) {
    this.$router.push(`/teaching/student/courses/${val.course_id}`);
  }
}
</script>

<template lang="pug">
ActivitySchedule.activity-schedule(
  :activities="activities"
  :loading="loading"
  @show="onShow")
</template>

<style lang="stylus" scoped>
.activity-schedule
  height 100%
</style>
