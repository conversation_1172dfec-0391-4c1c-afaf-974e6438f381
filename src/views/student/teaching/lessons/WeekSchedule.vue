<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import WeekScheduleContainer from './WeekScheduleContainer.vue';
import { ILesson } from '../../../../models/teaching/lesson';

@Component({
  components: {
    WeekScheduleContainer,
  },
})
export default class StudentLessonsWeekSchedule extends Vue {
  onShow(lesson: ILesson) {
    this.$router.push(`/teaching/student/lessons/${lesson.id}`);
  }
}
</script>

<template lang="pug">
WeekScheduleContainer(@show="onShow")
</template>

<style lang="stylus" scoped></style>
