<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClassSchedule from '@/components/teaching/ClassSchedule.vue';
import lesson, { ILesson, ILessonRecord, Lesson } from '@/models/teaching/lesson';
import semesterModel, { ISemester } from '@/models/teaching/semester';

@Component({
  components: {
    ClassSchedule,
  },
})
export default class StudentWeekScheduleContainer extends Vue {
  query: IObject = {};
  semester: ISemester = {};
  week: number = 1; // 当前周
  weeksCount: number = 20;
  activeDate: string = new Date().toString();
  lessons: ILesson[] = []; // 所有课程
  weekLessons: ILesson[] = [];
  loading: boolean = false;

  created() {
    this.fetchSemesters();
  }

  async fetchSemesters() {
    const { data } = await semesterModel.setRole('student').current();
    this.semester = data;
    this.week = data.current_week || 1;
    this.weeksCount = data.weeks_count || 20;
    this.fetchLessons();
  }
  async fetchLessons() {
    try {
      this.loading = true;
      const { data } = await lesson.setRole('student').index({
        page: 1,
        per_page: 100,
        q: {
          ...this.query,
          week_eq: this.week,
        },
      });
      this.lessons = data.lessons.map(o => ({
        ...o,
        ...Lesson.getLessonTimeState(o),
      }));
      this.weekLessons = Lesson.getWeekLessons(this.lessons);
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  changeWeek(val: number) {
    this.week = this.week + val;
    if (this.week < 1) {
      this.week = 1;
      return;
    }
    if (this.week > this.weeksCount) {
      this.week = this.weeksCount;
      return;
    }
    this.fetchLessons();
  }
  onShow(data: ILesson) {
    this.$emit('show', data);
  }
}
</script>

<template lang="pug">
.container
  .header
    van-icon(name="arrow-left" @click="changeWeek(-1)")
    .week 第 {{ week }} 周
    van-icon(name="arrow" @click="changeWeek(+1)")
  .schedule-content(v-loading="loading")
    slot(:weekLessons="weekLessons" :lessons="lessons" :loading="loading")
      ClassSchedule(
        :lessons="weekLessons"
        :columns="semester._scheduleColumns"
        :isEvaluation="true"
        :loading="loading"
        @show="onShow")
</template>

<style lang="stylus" scoped>
.container
  position relative
  padding-top 48px
  height 100%
  .header
    position absolute
    top 0
    left 0
    display flex
    justify-content center
    align-items center
    width 100%
    height 48px
    color #A6A6A6
    font-weight 500
    font-size 17px
    background rgba(255,255,255,1)
    box-shadow 0px 1px 2px 0px rgba(0,0,0,0.08)
    .week
      margin 0 16px
      color rgba(56, 56, 56, 1)
      white-space nowrap
      flex-shrink 0
      word-break keep-all
  .schedule-content
    height 100%
    overflow auto
    -webkit-overflow-scrolling touch
    position relative
</style>
