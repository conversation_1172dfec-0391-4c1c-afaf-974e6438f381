<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import wechatSdk from '@/utils/wechatSdk';
import lessonModel, { ILesson, Lesson } from '@/models/teaching/lesson';
import studentEvaluation from '@/models/teaching/studentEvaluation';
import { Register } from '@/models/teaching/register';

@Component({
  components: {},
})
export default class TeacherLessonsShow extends Vue {
  lesson: ILesson = {};
  loading: boolean = true;
  registerLoading: boolean = true;
  // 签到
  checkStartSeconds: number = 0;
  checkEndSeconds: number = 0;
  // 评价
  evaluateStartSeconds: number = 0;
  evaluateEndSeconds: number = 0;
  evaluateLoading: boolean = false;

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get canRegister() {
    return this.lesson.register_state === 'undo' && this.checkEndSeconds > 0 && this.checkStartSeconds === 0;
  }
  get canEvaluate() {
    const { lesson, evaluateStartSeconds, evaluateEndSeconds } = this;
    const { student_evaluation_state } = lesson;
    return (
      student_evaluation_state === 'done' ||
      student_evaluation_state === 'doing' ||
      (student_evaluation_state === 'todo' && evaluateEndSeconds > 0 && evaluateStartSeconds === 0)
    );
  }
  get registerModel() {
    return new Register('student');
  }

  async mounted() {
    if (this.$route.query.code) {
      // 1. 学生直接扫码进入
      this.signUpForLesson(this.$route.query.code as string);
    } else {
      // 2. 正常进入
      await this.fetchLesson();
      this.fetchRegister();
    }
  }
  async fetchLesson() {
    try {
      this.loading = true;
      const { data } = await lessonModel.setRole('student').find(this.lessonId);
      this.lesson = data;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  async fetchRegister() {
    try {
      this.registerLoading = true;
      const { data } = await this.registerModel.checkInfo(this.lessonId);
      this.checkStartSeconds = data.register_start_time * 1000;
      this.checkEndSeconds = data.register_end_time * 1000 - this.checkStartSeconds;
      this.evaluateStartSeconds = data.evaluate_start_time * 1000;
      this.evaluateEndSeconds = data.evaluate_end_time * 1000 - this.evaluateStartSeconds;
      this.registerLoading = false;
    } catch (error) {
      this.registerLoading = false;
    }
  }
  // 扫码签到
  scanCode() {
    if (!this.canRegister) return;
    wechatSdk.scanQRCode(this.signUpForLesson);
  }
  // 直接签到
  async directlyCheck() {
    if (!this.canRegister || this.loading) return;
    this.signUpForLesson();
  }
  async signUpForLesson(code?: string) {
    try {
      this.loading = true;
      const { data } = await this.registerModel.checkInLesson(this.lessonId);
      this.lesson.register_state = data.state;
      this.checkStartSeconds = 0;
      this.checkEndSeconds = 0;
      this.loading = false;
      this.$notify({ type: 'success', message: '签到成功' });
    } catch (error) {
      this.$notify({ type: 'danger', message: '签到失败，请稍后重试' });
      this.loading = false;
    }
  }
  // 评价
  async onEvaluation() {
    if (!this.canEvaluate) return;
    this.evaluateStartSeconds = 0;
    this.evaluateEndSeconds = 0;
    this.$router.push(`/teaching/student/lessons/${this.lesson.id}/evaluation`);
  }
}
</script>

<template lang="pug">
NavContainer.container(:title="lesson.course_set_name" :loading="loading")
  van-cell-group(title="签到")
    van-cell(title="时间" icon="clock-o" :value="`${lesson.date} ${lesson.start_time}-${lesson.end_time}`")
    van-cell(title="节数" icon="location-o" :value="`${lesson.unit_count} 节`")
    van-cell(title="教室" icon="wap-home-o" :value="lesson.classroom_name")
    van-cell(title="教师" icon="contact" :value="lesson.teacher_name")
    van-cell(title="签到" icon="sign" :is-link="canRegister" @click="directlyCheck")
      template
        .state.text-gray(size="large" v-if="!lesson.register_state")
          | 等待签到开启
        .state.text-primary(size="large" v-else-if="lesson.register_state === 'undo'")
          span(v-if="checkStartSeconds")
            span 倒计时：
            van-count-down.countdown(
              :time="checkStartSeconds"
              @finish="checkStartSeconds = 0")
          van-count-down.countdown.text-success(
            v-else-if="checkEndSeconds"
            :time="checkEndSeconds"
            @finish="checkEndSeconds = 0")
          span(v-else) 待签到
        .state.text-success(size="large" type="success" v-else-if="lesson.register_state === 'done'")
          | 已签到
        .state.text-warning(size="large" type="warning" v-else-if="lesson.register_state === 'late'")
          | 已签到，迟到
        .state.text-danger(size="large" type="warning" v-else-if="lesson.register_state === 'absent'")
          | 缺席
    van-cell(
      title="评价"
      icon="comment-o"
      @click="onEvaluation"
      :is-link="canEvaluate")
      template
        .state.text-gray(size="large" v-if="lesson.student_evaluation_state === 'todo'")
          span(v-if="evaluateStartSeconds")
            span 倒计时：
            van-count-down.countdown(
              :time="evaluateStartSeconds"
              @finish="evaluateStartSeconds = 0")
          van-count-down.countdown.text-success(
            v-else-if="evaluateEndSeconds"
            :time="evaluateEndSeconds"
            @finish="evaluateEndSeconds = 0")
          span(v-else) 待填写评价
        .state.text-success(size="large" type="success" v-else-if="lesson.student_evaluation_state === 'done'")
          | 已填写评价
        .state.text-warning(size="large" type="warning" v-else-if="lesson.student_evaluation_state === 'doing'")
          | 待提交评价
  van-cell-group(title="资源")
    van-cell(title="课程教案" icon="tv-o" is-link :to="`${lessonId}/lesson_plans`")
  van-cell-group(title="作业")
    van-cell(title="课堂作业" icon="todo-list-o" is-link :to="`${lessonId}/reports`")
  van-cell-group(title="论坛")
    van-cell(title="课堂论坛" icon="chat-o" is-link :to="`/teaching/student/courses/${lesson.course_id}/topics`")
</template>

<style lang="stylus" scoped>
.countdown
  color #3DA8F5
  letter-spacing 1px
  font-weight 500
  line-height 24px
  display inline-block
</style>
