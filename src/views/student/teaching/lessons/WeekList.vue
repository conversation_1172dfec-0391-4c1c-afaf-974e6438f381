<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import WeekScheduleContainer from './WeekScheduleContainer.vue';
import { ILesson } from '../../../../models/teaching/lesson';
import sessionStore from '../../../../store/modules/session.store';

@Component({
  components: {
    WeekScheduleContainer,
  },
})
export default class StudentLessonsWeekList extends Vue {
  get role() {
    return sessionStore.role;
  }

  onShow(lesson: ILesson) {
    this.$router.push(`/teaching/student/lessons/${lesson.id}`);
  }
}
</script>

<template lang="pug">
WeekScheduleContainer
  template(#default="{ lessons }")
    Empty(v-if="lessons.length === 0" desc="无课程")
    .lessons
      .lesson(
        v-for="lesson in lessons"
        :key="lesson.id"
        @click="onShow(lesson)")
        .name {{ lesson.course_set_name }}
        .item
          label 时间
          .gap-box
            span {{ lesson.date }}
            span {{ lesson.start_time }} - {{ lesson.end_time }}
            span {{ $utils.weekDay(lesson.weekday) }}
        .item
          label 节数
          span {{ lesson.start_unit }} - {{ lesson.end_unit }}（共 {{ lesson.unit_count }} 节）
        .item
          label 教室
          span {{ lesson.classroom_name }} {{ lesson.course_name }}
        .item
          label 教师
          span {{ lesson.teacher_name }}
        .statistic(v-if="role === 'student'")
          span.tag-cell
            TaTag(v-if="!lesson.register_state")
              | 等待签到开启
            TaTag(type="primary" v-else-if="lesson.register_state === 'undo'")
              | 待签到
            TaTag(type="success" v-else-if="lesson.register_state === 'done'")
              | 已签到
            TaTag(type="warning" v-else-if="lesson.register_state === 'late'")
              | 迟到
            TaTag(type="danger" v-else-if="lesson.register_state === 'absent'")
              | 缺席
          span.tag-cell
            TaTag(type="primary" v-if="lesson.student_evaluation_state === 'todo'")
              | 待填写评价
            TaTag(type="success"  v-else-if="lesson.student_evaluation_state === 'done'")
              | 已填写评价
            TaTag(type="warning"  v-else-if="lesson.student_evaluation_state === 'doing'")
              | 待提交评价
          span.tag-cell
            TaTag(type="default" v-if="lesson.report_state === 'none'")
              | 无作业
            TaTag(type="warning" v-if="lesson.report_state === 'pending'")
              | 未提交
            TaTag(type="primary" v-if="lesson.report_state === 'published'")
              | 已提交
            TaTag(type="success" v-if="lesson.report_state === 'scored'")
              | 已评分
</template>

<style lang="stylus" scoped>
.lessons
  padding 12px 12px 1px
  .lesson
    margin-bottom 12px
    padding 16px
    border-radius 4px
    background #fff
    cursor pointer
    .name
      margin-bottom 10px
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 16px
      line-height 20px
    .item
      display flex
      margin-bottom 8px
      height 20px
      color rgba(128, 128, 128, 1)
      font-size 14px
      line-height 20px
      label
        width 58px
      .gap-box
        display inline-block
        span
          margin-right 6px
    .statistic
      display flex
      margin-top 8px
      padding-top 12px
      border-top 1px solid #E8E8E8
      .tag-cell
        margin-right 6px
</style>
