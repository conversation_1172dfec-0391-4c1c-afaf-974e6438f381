<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import LessonPlansDetail from '@/components/teaching/LessonPlansDetail.vue';
import { LessonPlan, ILessonPlan } from '@/models/teaching/lesson_plan';
import { ILessonItem } from '@/models/teaching/lesson_item';
import lessonModel, { ILesson } from '@/models/teaching/lesson';
import SourceRecorderTimer from './components/SourceRecorderTimer.vue';

@Component({
  components: {
    LessonPlansDetail,
    SourceRecorderTimer,
  },
})
export default class StudentLessonPlans extends Vue {
  lessonPlans: ILessonPlan[] = [];
  loading: boolean = false;
  // study
  previewingLessonPlan: ILessonPlan = {};
  previewingLessonItem: ILessonItem = {};
  previewerVisible: boolean = false;

  get lessonId() {
    return +this.$route.params.lessonId;
  }
  get previewingFileItem() {
    const { attachments } = this.previewingLessonItem;
    return attachments ? attachments.attachments[0] : {};
  }

  mounted() {
    this.fetchLessonPlans();
  }
  async fetchLessonPlans() {
    try {
      this.loading = true;
      const lessonPlanModel = new LessonPlan('student');
      lessonPlanModel.setConfig({ parentResource: 'lessons' });
      const { data } = await lessonPlanModel.indexByParent(this.lessonId, {
        page: 1,
        per_page: 1000,
      });
      this.lessonPlans = data.lesson_plans;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  async onItemPreview(params: { lessonPlan: ILessonPlan; lessonItem: ILessonItem }) {
    const { lessonPlan, lessonItem } = params;
    this.previewingLessonPlan = lessonPlan;
    this.previewingLessonItem = lessonItem;
    this.previewerVisible = true;
  }
  handlePerMinuteLogic(recorder: IObject) {
    if (this.previewingLessonItem.student_recorder) {
      Object.entries(recorder).forEach((entry: any) => {
        this.$set(this.previewingLessonItem.student_recorder!, entry[0], entry[1]);
      });
    } else {
      this.fetchLessonPlans();
    }
  }
}
</script>

<template lang="pug">
NavContainer.container(title="课程资源")
  LessonPlansDetail(
    :lessonPlans="lessonPlans"
    @itemPreview="onItemPreview"
    v-loading="loading")

  FilePreviewer(
    v-model="previewerVisible"
    :attachment="previewingFileItem")
    .timer-part(slot="top")
      SourceRecorderTimer.timer(
        sourceType="Teaching::LessonItem"
        :sourceId="previewingLessonItem.id"
        :lessonId="lessonId"
        :afterRecordCallback="handlePerMinuteLogic")

  //- lesson 时长统计埋点
  SourceRecorderTimer(
    sourceType="Teaching::Lesson"
    :sourceId="lessonId"
    :lessonId="lessonId"
    :visible="false")
</template>

<style lang="stylus" scoped>
.lesson-plans
  height 100%

.timer-part
  padding 10px 16px
  text-align center
  .timer
    color #fff
</style>
