import { RouteConfig } from '@/interfaces/IRoute';
// 作业
const Reports = () => import(/* webpackChunkName: "teaching_student_lesson_report_index" */ './children/Reports.vue');
const Report = () => import(/* webpackChunkName: "teaching_student_lesson_report_show" */ './children/Report.vue');
const ReportForm = () =>
  import(/* webpackChunkName: "teaching_student_lesson_report_form" */ './children/ReportForm.vue');

const routes: RouteConfig[] = [
  {
    path: '/teaching/student/lessons/:lessonId',
    name: 'teaching_student_lessons_show',
    component: () => import(/* webpackChunkName: "teaching_student_lessons_show" */ './Show.vue'),
    meta: {
      title: '评价详情',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/lessons/:lessonId/lesson_plans',
    name: 'teaching_student_lesson_plans',
    component: () => import(/* webpackChunkName: "teaching_student_lessons_show" */ './LessonPlans.vue'),
    meta: {
      title: '课程教学平台',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/lessons/:lessonId/evaluation',
    name: 'teaching_student_lessons_evaluation',
    component: () => import(/* webpackChunkName: "teaching_student_lessons_evaluation" */ './Evaluation.vue'),
    meta: {
      title: '课程评价',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  // 考试
  {
    path: '/teaching/student/exam_activities/:activityId/exam/:answerSetId',
    name: 'teaching_student_exam_activity_do',
    component: () => import(/* webpackChunkName: "teaching_student_exam" */ './DoExam.vue'),
    meta: {
      title: '试卷答题',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  // 入口
  {
    path: '/teaching/student',
    name: 'teaching_student_lessons_entry',
    component: () => import(/* webpackChunkName: "teaching_student_lessons_index" */ './Entry.vue'),
    meta: {
      title: '课程教学平台',
      requireAuth: true,
      roles: ['Student'],
    },
    children: [
      {
        path: 'activities',
        name: 'teaching_student_activities_index',
        component: () => import(/* webpackChunkName: "teaching_student_lessons_index" */ './Activities.vue'),
        meta: {
          title: '学期课表',
          requireAuth: true,
          roles: ['Student'],
        },
      },
      {
        path: 'lessons',
        name: 'teaching_student_lessons_index',
        component: () => import(/* webpackChunkName: "teaching_student_lessons_index" */ './Lessons.vue'),
        meta: {
          title: '学期课表',
          requireAuth: true,
          roles: ['Student'],
        },
      },
      {
        path: 'week_list',
        name: 'teaching_student_week_list',
        component: () => import(/* webpackChunkName: "teaching_student_week_list" */ './WeekList.vue'),
        meta: {
          title: '列表视图',
          requireAuth: true,
          roles: ['Student'],
        },
      },
      {
        path: 'week_schedule',
        name: 'teaching_student_week_schedule',
        component: () => import(/* webpackChunkName: "teaching_student_week_schedule" */ './WeekSchedule.vue'),
        meta: {
          title: '日历视图',
          requireAuth: true,
          roles: ['Student'],
        },
      },
      {
        path: 'exam_activities',
        name: 'teaching_student_exam_activities',
        component: () => import(/* webpackChunkName: "teaching_student_exam_activities" */ './ExamActivities.vue'),
        meta: {
          title: '我的考试',
          requireAuth: true,
          roles: ['Student'],
        },
      },
    ],
  },
  // children
  // 作业信息
  {
    path: '/teaching/student/lessons/:lessonId/reports',
    name: 'teaching_student_lesson_report_index',
    component: Reports,
    meta: {
      title: '课堂作业列表',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/lessons/:lessonId/reports/:reportId',
    name: 'teaching_student_lesson_report_show',
    component: Report,
    meta: {
      title: '作业详情',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/lessons/:lessonId/reports/:reportId/report_form',
    name: 'teaching_student_lesson_report_form',
    component: ReportForm,
    meta: {
      title: '作业提交',
      requireAuth: true,
      roles: ['Student'],
    },
  },
];

export default routes;
