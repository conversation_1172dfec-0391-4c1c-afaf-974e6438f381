<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClassSchedule from '@/components/teaching/ClassSchedule.vue';
import lesson, { ILesson } from '@/models/teaching/lesson';

@Component({
  components: {
    ClassSchedule,
  },
})
export default class StudentLessons extends Vue {
  query: IObject = {};
  lessons: ILesson[] = []; // 所有课程
  loading: boolean = false;

  created() {
    this.fetchLessons();
  }

  async fetchLessons() {
    try {
      this.loading = true;
      const { data } = await lesson.setRole('student').index({
        page: 1,
        per_page: 1020,
        q: {
          ...this.query,
        },
      });
      this.lessons = data.lessons;
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
}
</script>

<template lang="pug">
ClassSchedule.schedule(:lessons="lessons" :loading="loading")
</template>

<style lang="stylus" scoped>
.schedule
  height 100%
  width 100%
</style>
