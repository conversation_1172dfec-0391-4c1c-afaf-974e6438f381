<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import session from '@/models/session';

@Component
export default class TeachingStudentLessonsEntry extends Vue {
  get activeIndex() {
    return [
      '/teaching/student/activities',
      '/teaching/student/week_schedule',
      '/teaching/student/week_list',
      '/teaching/student/exam_activities',
    ].indexOf(this.$route.path);
  }
}
</script>

<template lang="pug">
.lessons-container
  router-view
  van-tabbar.tabbar(:value="activeIndex" fixed safe-area-inset-bottom :zIndex="1000")
    van-tabbar-item(icon="notes-o" to="/teaching/student/activities" :replace="true") 学期课表
    van-tabbar-item(icon="calender-o" to="/teaching/student/week_schedule" :replace="true") 日历视图
    van-tabbar-item(icon="orders-o" to="/teaching/student/week_list" :replace="true") 列表视图
    van-tabbar-item(icon="records" to="/teaching/student/exam_activities" :replace="true") 我的考试
</template>

<style lang="stylus" scoped>
.lessons-container
  padding 0px 0px 50px
  height 100vh
  position relative
  overflow auto
  -webkit-overflow-scrolling touch
</style>
