<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { ExamActivity, IActivity } from '../../../../service/exam_activity';
import { QuestionSetService, IQuestionSet } from '../../../../service/question_set';
import { AnswerSetService } from '../../../../service/answer_set';
import { baseStore } from '../../../../store/modules/base.store';

@Component({
  components: {},
})
export default class StudentExamActivities extends Vue {
  activities: IActivity[] = [];
  loading: boolean = false;
  activeTab: string = 'todo';
  tabs: IObject[] = [
    { text: '最近三天', key: 'todo' },
    { text: '全部考试', key: '' },
  ];

  async mounted() {
    this.fetchActivities();
  }
  async fetchActivities(page: number = 1) {
    try {
      this.loading = true;
      const now = this.$dayjs();
      const query =
        this.activeTab === 'todo'
          ? {
              start_at_gt: now.format('YYYY-MM-DD'),
              start_at_lt: now.add(3, 'day').format('YYYY-MM-DD'),
            }
          : {};
      const { data } = await ExamActivity.fetch({
        page,
        per_page: 100,
        q: query,
      });
      this.activities = data.activities.map(a => {
        const nowTime = this.$dayjs();
        const endAt = this.$dayjs(a.start_at).add(a.duration_in_min!, 'minute');
        return {
          ...a,
          _recently: endAt.isAfter(nowTime) && endAt.isBefore(nowTime.add(1, 'day')),
        };
      });
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }
  async onShow(activity: IActivity) {
    const { state } = activity.own_answer_sets!;
    const { to_start, to_start_off, to_end } = activity.time_infos || { to_start: 0, to_start_off: 0, to_end: 0 };
    if (state === 'done') {
      this.$message.warning('已提交试卷');
    } else if (to_start > 0) {
      this.$message.warning('考试还未开始');
    } else if (to_start_off > 0) {
      this.confirmExam(activity);
      // } else if (to_end > 0 && state === 'todo') {
      //   this.$message.error('已迟到，无法进入考试');
      // } else if (to_end > 0 && state === 'doing') {
    } else if (to_end > 0) {
      this.confirmExam(activity);
    } else {
      this.$message.warning('考试已结束');
    }
  }
  confirmExam(activity: IActivity) {
    this.$dialog
      .confirm({
        title: '考试确认',
        message: `考试名称：${activity.title}、考试时长：${activity.duration_in_min}分钟、考试时间：${this.$dayjs(
          activity.start_at,
        ).format('YYYY-MM-DD HH:mm')}`,
      })
      .then(() => {
        this.doExam(activity);
      });
  }
  async doExam(ac: IActivity) {
    try {
      this.loading = true;
      const { state, id } = ac.own_answer_sets!;
      if (state === 'todo') {
        await AnswerSetService.UserOwn.update({ id, state: 'doing' });
      } else if (state === 'done') {
        this.$message.error('已提交试卷');
        this.loading = false;
        return;
      }
      this.$utils.open(`/teaching/student/exam_activities/${ac.id}/exam/${id}`, '_self');
      this.loading = false;
    } catch (error) {
      this.loading = false;
      this.$message.error('获取试卷信息异常');
    }
  }
  finishOn(a: IActivity, key: string) {
    this.$set(a.time_infos, key, 0);
    this.fetchActivities();
  }
}
</script>

<template lang="pug">
.container
  Tabs.tabs(v-model="activeTab" :tabs="tabs" @change="fetchActivities(1)")
  .activities(v-loading="loading")
    Empty(desc="暂无考试" v-if="activities.length === 0")
    .activity(v-for="a in activities" :key="a.id" @click="onShow(a)")
      .name {{ a.title }}
      KvCell(name="开始时间" :value="a.start_at | format")
      KvCell(name="考试时长" :value="`${a.duration_in_min}分钟`")
      .footer(v-if="a._recently")
        KvCell(name="倒计时" v-if="a.time_infos.to_start")
          van-count-down.text-primary(
            :time="a.time_infos.to_start * 1000"
            @finish="finishOn(a, 'to_start')"
            format="DD天 HH时 mm分 ss秒")
        KvCell(name="截止进入" v-else-if="a.time_infos.to_start_off")
          van-count-down.text-danger(
            :time="a.time_infos.to_start_off * 1000"
            @finish="finishOn(a, 'to_start_off')")
        KvCell(name="距离结束" v-else-if="a.time_infos.to_end")
          van-count-down.text-warning(
            :time="a.time_infos.to_end * 1000"
            @finish="finishOn(a, 'to_end')")
</template>

<style lang="stylus" scoped>
.activities
  padding 12px 12px 1px
  .activity
    margin-bottom 12px
    padding 16px
    border-radius 4px
    background #fff
    cursor pointer
    .name
      margin-bottom 10px
      color rgba(56, 56, 56, 1)
      font-weight 500
      font-size 16px
      line-height 20px
    .footer
      margin-top 8px
      padding-top 12px
      border-top 1px solid #E8E8E8
      &:empty
        display none
</style>
