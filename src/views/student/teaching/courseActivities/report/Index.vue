<template lang="pug">
NavContainer(title="作业提交情况" :loading="homeworkStore.loading")
  Tabs.tabs(v-model="tabIndex" :tabs="tabs")
  ListView(
    :data="reportStore.records"
    :loading="homeworkStore.loading"
    :pullup="!reportStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(reportStore.currentPage + 1)"
    @refresh="fetchData()")
    .reports
      van-panel.report(v-for="(item, index) in reportStore.records" :key="index" @click="onShow(item)")
        template(slot="header")
          strong {{ item.title }}
        template(slot="default")
          .cell(v-if="item.state !== 'pending'")
            .key 提交时间
            .value(v-if="item.answer && item.answer.published_at")
              | {{ $dayjs(item.answer.published_at).format('YYYY/MM/DD HH:mm') }}
          .cell
            .key 提交截止时间
            .value
              | {{ item.end_at ? $dayjs(item.end_at).format('YYYY/MM/DD HH:mm') : '-' }}
          .cell
            .key 评论数
            .value {{ item.comment_count }}
        template(slot="footer")
          .state
            van-tag(color="#F0F9F2" text-color="#6DC37D" v-if="item.state === 'scored'") 已评分
            van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else-if="item.state === 'published'") 已上传
            van-tag(color="#EDF7FF" text-color="#3DA8F5" v-else) 未提交
          template(v-if="item.state !== 'pending'")
            van-tag(color="#F0F9F2" text-color="#6DC37D" v-if="item.state === 'scored'")
              | 评分：{{ +item.score }}分
            van-tag(color="#f5f5f5" text-color="#A6A6A6" v-else) 等待打分
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import homeworkStore from '@/store/modules/teaching/homework.store';

@Component({
  components: {},
})
export default class Reports extends Vue {
  private queryObject: object = {};
  private tabIndex: string = 'published';
  private reportStore: any = {
    info: {},
    records: [],
  };
  get variables() {
    return [
      'user_of_Student_type_name',
      'user_of_Student_type_code',
      'user_of_Student_type_major_name',
      'user_of_Student_type_adminclass_name',
    ];
  }
  get role() {
    return this.$tools.getRole();
  }
  get tabs() {
    return [
      {
        text: '待评分',
        key: 'published',
      },
      {
        text: '已评分',
        key: 'scored',
      },
      {
        text: '未提交',
        key: 'pending',
      },
    ];
  }
  get homeworkStore() {
    return homeworkStore || {};
  }

  @Watch('queryObject')
  public watchQuery() {
    this.fetchData();
  }

  @Watch('tabIndex')
  public watchChange() {
    this.fetchData();
  }

  public mounted() {
    homeworkStore.changeNamespace(this.role);
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.courseId,
      parent: 'course',
      role: 'student',
      q: {
        s: ['created_at asc'],
        state_eq: this.tabIndex,
      },
    };
    const { data } = await homeworkStore.fetchHomeworks(params);
    const records = data.homeworks.map((item: any) => ({
      ...item.lesson,
      ...item,
      title: item.homework.title,
      lesson_id: item.lesson.id,
      homework_id: item.homework.id,
    }));
    this.reportStore = {
      ...data,
      currentPage: data.current_page,
      totalPages: data.total_pages,
      totalCount: data.total_count,
      records: data.current_page === 1 ? records : this.reportStore.records.concat(records),
      finish: data.current_page === data.total_pages,
    };
  }

  public onShow(val: any) {
    this.$router.push(`/teaching/student/courses/${this.$route.params.courseId}/reports/${val.homework_id}`);
  }
}
</script>

<style lang="stylus" scoped>
.reports
  padding 10px 16px
  width 100%
  background #f5f5f5
  .report
    margin-bottom 12px
    padding 16px 16px 12px
    width 100%
    border-radius 4px
    &:active
      opacity 0.8
    strong
      color #383838
      font-size 16px
      line-height 24px
    .cell
      display flex
      margin-top 8px
      color #808080
      font-size 14px
      line-height 20px
      .key
        width 68px
    .state
      display inline
      margin-right 10px

.van-panel__footer
  padding 12px 0px 0px
</style>
