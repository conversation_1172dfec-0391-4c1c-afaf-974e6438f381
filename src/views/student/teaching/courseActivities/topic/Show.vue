<template lang="pug">
NavContainer.pannel(title="课程论坛详情" :loading="loading")
  Tabs(v-model="currentTab" :tabs="typeSteps" slot="title")

  template(v-if="topic.type === TopicType.Question")
    TopicQuestionSets(:topic="topic" v-if="currentTab === 'question'")
    TopicDetail(:topic="topic" @edit="onEdit" @delete="onDelete" v-if="currentTab === 'questionContent'")

  template(v-if="topic.type === TopicType.Discuss")
    TopicDiscussions(:topic="topic" :courseId="courseId" v-show="currentTab === 'comment'")
    TopicDetail(:topic="topic" @edit="onEdit" @delete="onDelete" v-if="currentTab === 'content'")

  template(v-else)
    TopicDetail(:topic="topic" @edit="onEdit" @delete="onDelete" v-if="currentTab === 'content'")
    CommentPanel(
      v-if="currentTab === 'comment'"
      :commentableId="topicId"
      commentType="Tree"
      commentableType="Topic"
      :indexPath="`/teaching/user/courses/${courseId}/topics`"
      :deletePath="`/teaching/user/courses/${courseId}/topics/${topicId}`"
      :useCdn="true"
      :showHeader="false"
      :disabled="!topic.can_reply"
      @refresh="fetchData")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import CommentPanel from '@/components/comment/CommentPanel.vue';
import TopicDetail from '@/components/topic/TopicDetail.vue';
import { TopicService, ITopic, TopicType } from '@/service/topic';
import TopicDiscussions from '@/components/topic/TopicDiscussions.vue';
import TopicQuestionSets from '@/components/topic/TopicQuestionSets.vue';

@Component({
  components: {
    CommentPanel,
    TopicDetail,
    TopicDiscussions,
    TopicQuestionSets,
  },
})
export default class Show extends Vue {
  topic: ITopic = {};
  loading: boolean = false;
  // tabs
  currentTab: string = '';
  typeSteps: object[] = [];

  get role() {
    return this.$tools.getRole();
  }
  get courseId() {
    return +this.$route.params.courseId;
  }
  get topicId() {
    return +this.$route.params.topicId;
  }
  get TopicType() {
    return TopicType;
  }

  mounted() {
    this.fetchData();
  }

  async fetchData() {
    this.loading = true;
    const { data } = await TopicService.teaching
      .findByCourse({
        courseId: this.courseId,
        topicId: this.topicId,
      })
      .finally(() => {
        this.loading = false;
      });
    this.topic = data;
    this.setTypeSteps();
  }
  setTypeSteps() {
    if (this.topic.type === TopicType.Discuss) {
      this.currentTab = 'comment';
      this.typeSteps = [
        { key: 'content', text: '讨论内容' },
        { key: 'comment', text: '讨论框' },
      ];
    } else if (this.topic.type === TopicType.Forum) {
      this.currentTab = 'content';
      this.typeSteps = [
        { key: 'content', text: '评论内容' },
        { key: 'comment', text: '评论框' },
      ];
    } else if (this.topic.type === TopicType.Question) {
      this.currentTab = 'question';
      this.typeSteps = [
        { key: 'question', text: '试卷列表' },
        { key: 'questionContent', text: '试卷描述' },
      ];
    } else {
      this.currentTab = 'content';
      this.typeSteps = [
        { key: 'content', text: '通知内容' },
        { key: 'comment', text: '评论' },
      ];
    }
  }
  onEdit(topic: ITopic) {
    this.$router.push(`${topic.id}/edit`);
  }
  async onDelete(topic: ITopic) {
    try {
      this.loading = true;
      await TopicService.teaching.deleteByCourse({
        courseId: this.courseId,
        topicId: topic.id!,
      });
      this.loading = false;
      this.$message.success('已删除');
      this.$router.replace('../topics');
    } catch (error) {
      this.$message.error('删除失败');
      this.loading = false;
    }
  }
}
</script>

<style lang="stylus" scoped>
.pannel
  width 100%
  height 100%
  background #fff
  .main
    overflow auto
    padding 20px
    width 100%
    height 100%
    .info-miidle
      display flex
      align-items center
      padding 30px 0px
      width 100%
      border-top 1px rgba(229, 229, 229, 1) solid
      border-bottom 1px rgba(229, 229, 229, 1) solid
      .ant-input-lg
        height 44px
      .ant-btn-lg
        margin-left 12px
        width 110px
        height 44px
        font-weight 500
        font-size 16px
    .info-bottom
      padding-top 30px
      width 100%
      .comment-label
        margin-bottom 8px
        color rgba(51, 51, 51, 1)
        font-weight 500
        font-size 16px
        line-height 20px
      .pagination
        padding 20px 0px
        text-align right
</style>
