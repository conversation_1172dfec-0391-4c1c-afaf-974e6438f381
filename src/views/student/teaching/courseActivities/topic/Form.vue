<template lang="pug">
NavContainer(title="创建课程论坛" :loading="loading")
  van-form(@submit="onSubmit")
    van-field(
      v-model="topic.title"
      name="title"
      label="标题"
      placeholder="输入标题"
      required
      :rules="[{ required: true, message: '请输入标题' }]")
    van-field(
      v-model="topic.body"
      name="body"
      label="内容"
      placeholder="输入内容"
      rows="4"
      autosize
      type="textarea"
      required
      :rules="[{ required: true, message: '请输入内容' }]")
    div(style="margin: 16px 0px")
      van-button(
        block
        type="info"
        native-type="submit"
        :disabled="disabled || loading") 提交
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import { TopicService, ITopic, TopicType } from '@/service/topic';
import { baseStore } from '@/store/modules/base.store';

@Component({
  components: {},
})
export default class topicForm extends Vue {
  topic: ITopic = {
    cover_image: '',
    title: '',
    body: '',
    type: TopicType.Forum,
    state: 'published', // pending, :published, :verified, :rejected
  };

  get courseId() {
    return +this.$route.params.courseId;
  }
  get topicId() {
    return +this.$route.params.topicId;
  }
  get loading() {
    return baseStore.loading;
  }
  get disabled() {
    return !this.topic.body || !this.topic.title;
  }

  mounted() {
    if (this.topicId) {
      this.fetchTopic();
    }
  }

  async fetchTopic() {
    const { data } = await TopicService.teaching.findByCourse({
      courseId: this.courseId,
      topicId: this.topicId,
    });
    this.topic = data;
  }

  onSubmit() {
    if (this.topicId) {
      this.update(this.topic);
    } else {
      this.create(this.topic);
    }
  }

  async create(topic: ITopic) {
    try {
      const { title, body } = topic;
      const { data } = await TopicService.teaching.createByCourse({
        courseId: this.courseId,
        topic: {
          title,
          body,
          type: TopicType.Forum,
          state: 'published',
        },
      });
      this.$message.success('添加成功！');
      this.$router.replace(`${data.id}`);
    } catch (error) {
      this.$message.error('添加失败！');
    }
  }

  async update(topic: ITopic) {
    try {
      await TopicService.teaching.updateByCourse({
        courseId: this.courseId,
        topic,
      });
      this.$message.success('更新成功！');
      this.$router.replace(`../${topic.id}`);
    } catch (error) {
      this.$message.error('更新失败！');
    }
  }
}
</script>

<style lang="stylus" scoped>
.van-form
  padding 10px 10px
</style>
