<script lang="ts">
/**
 * 论坛首页
 */
import { Component, Vue, Prop } from 'vue-property-decorator';
import TopicListCell from '@/components/topic/TopicListCell.vue';
import { ITopic, TopicType, TopicService } from '@/service/topic';

@Component({
  components: {
    TopicListCell,
  },
})
export default class TeachingForumIndex extends Vue {
  page: number = 1;
  pageSize: number = 15;
  pages: number = 1;
  totalCount: number = 0;
  records: ITopic[] = [];
  loading: boolean = false;

  get courseId() {
    return +this.$route.params.courseId;
  }
  get TopicType() {
    return TopicType;
  }

  created() {
    this.fetchRecords(1);
  }

  async fetchRecords(page: number = this.page, query?: object, perPage?: number) {
    this.loading = true;
    const { data } = await TopicService.teaching
      .fetchByCourse(this.courseId, {
        page,
        per_page: this.pageSize,
      })
      .finally(() => {
        this.loading = false;
      });
    this.page = page;
    this.pages = data.total_pages;
    this.totalCount = data.total_count;
    this.records = data.topics;
  }
  onNew() {
    this.$router.push('topics/new');
  }
}
</script>

<template lang="pug">
NavContainer(title="课程论坛")
  template(slot="right")
    van-icon(name="add-o" size="large" @click="onNew")
  ListView(
    :data="records"
    :loading="loading"
    :pullup="page >= pages"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchRecords(this.page + 1)"
    @refresh="fetchRecords(1)")
    .topics
      TopicListCell(
        v-for="record in records"
        :key="record.id"
        :topic="record"
        :to="`topics/${record.id}`")

</template>

<style lang="stylus" scoped></style>
