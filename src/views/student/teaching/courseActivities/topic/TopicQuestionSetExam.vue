<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { AnswerSetService, IAnswerSet } from '@/service/answer_set';
import { QuestionTypes, IQuestion } from '@/service/question';
import { IAnswer, AnswerService, UserOwnAnswer } from '@/service/answer';
import { baseStore } from '@/store/modules/base.store';

@Component({
  components: {},
})
export default class TopicQuestionSetExam extends Vue {
  answerSetMode: 'show' | 'edit' = 'show';
  answerSet: IAnswerSet = {};
  answers: IAnswer[] = [];

  get answerSetId() {
    return +this.$route.params.answerSetId;
  }
  get QuestionTypes() {
    return QuestionTypes;
  }
  get hasExamed() {
    return this.answerSetMode !== 'edit' || this.answerSet.state === 'done';
  }
  get disabled() {
    return this.answers.some(o => !o.value);
  }
  get noAnser() {
    return this.answers.every(o => !o.value);
  }
  get loading() {
    return baseStore.loading;
  }

  mounted() {
    if (this.answerSetId) {
      this.answerSetMode = (window.sessionStorage.getItem('ANSWER_SET_MODE') as 'edit' | 'show') || 'show';
      this.fetchAnswerSet();
    }
  }

  async fetchAnswerSet() {
    const { data } = await AnswerSetService.UserOwn.find(this.answerSetId);
    this.answerSet = data;
    this.answers = (this.answerSet.answers || []).map(AnswerService.UserOwn.getProcessedAnswer);
  }

  setAnswerValue(answer: any, value: string) {
    if (this.hasExamed) return;
    this.$set(answer, 'value', value);
  }
  setMultiTypeAnswerValue(answer: any, optionKey: string) {
    if (this.hasExamed) return;
    const checkboxKey = `checkbox_${optionKey}`;
    const refs = this.$refs[checkboxKey] as any;
    if (refs && refs[0]) {
      refs[0].toggle();
      this.$forceUpdate();
    }
  }
  onCheckboxChange(answer: any, keys: string[]) {
    this.$set(answer, 'value', keys);
  }
  async submit() {
    if (this.disabled || this.loading) return;
    if (this.answers.every(o => !!o.value)) {
      await AnswerSetService.UserOwn.update({
        id: this.answerSet.id,
        state: 'done',
        answers_attributes: this.answers.map(a => ({ id: Number(a.id), value: a.value.toString()!, meta: {} })),
      });
      this.$message.success('提交成功');
      this.$router.replace('../.');
    } else {
      this.$message.warning('请完成试卷后提交');
    }
  }
  getOptionKeyTitle(label: string, index: number) {
    const ikey = UserOwnAnswer.getOptionIndexKey(index);
    return `${ikey}. ${label}`;
  }
}
</script>

<template lang="pug">
NavContainer.container(title="试卷答题" v-loading="loading")
  .questions
    .question-wrapper(v-for="(answer, index) in answers" :key="answer.id" :class="{ 'question-error': answer.isError }")
      .title
        .marked {{ index + 1 }}.
        .ck-content(v-html="answer.question.title")
      .question(v-if="answer.question.type === QuestionTypes.single")
        van-radio-group(v-model="answer.value" :disabled="hasExamed")
          van-cell-group.options(:border="false")
            van-cell(
              v-for="(option, j) in answer.question.choices.options"
              :key="option.key"
              :title="getOptionKeyTitle(option.value, j)"
              clickable
              @click="setAnswerValue(answer, option.key)")
              van-radio(slot="right-icon" :name="option.key")
        .answer(v-if="answer.answer_meta")
          span.label.text-success 正确答案
          span {{ answer.rightValue }}

      .question(v-else-if="answer.question.type === QuestionTypes.multiple")
        van-checkbox-group(
          v-model="answer.value"
          :disabled="hasExamed"
          @change="(val) => onCheckboxChange(answer, val)")
          van-cell-group.options(:border="false")
            van-cell(
              v-for="(option, j) in answer.question.choices.options"
              :key="option.key"
              :title="getOptionKeyTitle(option.value, j)"
              clickable
              @click="setMultiTypeAnswerValue(answer, option.key, j)")
              van-checkbox(slot="right-icon" :name="option.key" :ref="`checkbox_${option.key}`")
        .answer(v-if="answer.answer_meta")
          span.label.text-success 正确答案
          span {{ answer.rightValue }}

      .question(v-else-if="answer.question.type === QuestionTypes.fill")
        van-field.field(
          v-model.trim="answer.value"
          placeholder="请输入答案"
          autosize
          rows="2"
          type="textarea"
          :disabled="hasExamed")
        .answer(v-if="answer.answer_meta")
          span.label.text-success 正确答案
          span {{ answer.rightValue }}
    .action
      van-button(type="warning" block :disabled="hasExamed" :loading="loading" v-if="hasExamed && noAnser")
        | 已提交 未答题
      van-button(type="primary" block :disabled="hasExamed" :loading="loading" v-else-if="hasExamed")
        | 已答题
      van-button(type="info" block :disabled="disabled" :loading="loading" @click="submit" v-else)
        | 提交
</template>

<style lang="stylus" scoped>
.container
  background #fff
  .questions
    padding 0 20px 0
    .question-wrapper
      border-top 1px solid #E9E9E9
      &:first-child
        border-top none
    .question-error
      .question
        .options
          border-color red
    .question
      padding 20px 0 20px
      .title
        margin-bottom 16px
        color rgba(38, 38, 38, 0.85)
        font-weight 500
        font-size 15px
        line-height 24px
      .field
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
      .options
        border 1px solid rgba(232, 232, 232, 1)
        border-radius 3px
      .answer
        display flex
        align-items flex-start
        padding 8px 16px
        .label
          flex-shrink 0
          margin-right 14px
    .action
      position sticky
      bottom 0
      left 0
      padding 20px 0
      background #fff
</style>
