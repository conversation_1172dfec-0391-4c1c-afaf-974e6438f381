import { RouteConfig } from '@/interfaces/IRoute';

const Show = () => import(/* webpackChunkName: "teaching_student_course_show" */ './Show.vue');
// 课堂作业
const Reports = () => import(/* webpackChunkName: "teaching_student_course_report_index" */ './report/Index.vue');
const Report = () => import(/* webpackChunkName: "teaching_student_course_report_show" */ './report/Show.vue');
const ReportForm = () =>
  import(/* webpackChunkName: "teaching_student_course_report_form" */ '../lessons/children/ReportForm.vue');
// 论坛
const Topics = () => import(/* webpackChunkName: "teaching_student_course_topic_index" */ './topic/Index.vue');
const Topic = () => import(/* webpackChunkName: "teaching_student_course_topic_show" */ './topic/Show.vue');
const Form = () => import(/* webpackChunkName: "teaching_student_course_topic_form" */ './topic/Form.vue');
const TopicQuestionSetExam = () =>
  import(/* webpackChunkName: "teaching_student_course_topic_exam" */ './topic/TopicQuestionSetExam.vue');

const routes: RouteConfig[] = [
  {
    path: '/teaching/student/courses/:courseId',
    name: 'teaching_student_course_show',
    component: Show,
    meta: {
      title: '课程详情',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  // 课堂作业
  {
    path: '/teaching/student/courses/:courseId/reports',
    name: 'teaching_student_course_report_index',
    component: Reports,
    meta: {
      title: '课堂作业',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/courses/:courseId/reports/:reportId/edit',
    name: 'teaching_student_course_report_form',
    component: ReportForm,
    meta: {
      title: '提交课堂作业',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/courses/:courseId/reports/:reportId',
    name: 'teaching_student_course_report_show',
    component: Report,
    meta: {
      title: '课堂作业详情',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  // 课程论坛
  {
    path: '/teaching/student/courses/:courseId/topics',
    name: 'teaching_student_course_topic_index',
    component: Topics,
    meta: {
      title: '课程论坛',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/courses/:courseId/topics/new',
    name: 'teaching_student_course_topic_new',
    component: Form,
    meta: {
      title: '发布论坛',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/courses/:courseId/topics/:topicId/edit',
    name: 'teaching_student_course_topic_edit',
    component: Form,
    meta: {
      title: '修改论坛',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/courses/:courseId/topics/:topicId',
    name: 'teaching_student_course_topic_show',
    component: Topic,
    meta: {
      title: '课程论坛详情',
      requireAuth: true,
      roles: ['Student'],
    },
  },
  {
    path: '/teaching/student/courses/:courseId/topics/:topicId/exam/:answerSetId',
    name: 'teaching_student_course_topic_exam',
    component: TopicQuestionSetExam,
    meta: {
      title: '试卷答题',
      requireAuth: true,
      roles: ['Student'],
    },
  },
];

export default routes;
