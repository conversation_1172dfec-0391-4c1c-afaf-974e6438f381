<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class Show extends Vue {
  private state: string = '';
  loading: boolean = false;
  @Prop() private property!: string;
}
</script>

<template lang="pug">
NavContainer.container(title="工作台" :loading="loading")
  van-cell-group(title="作业")
    van-cell(title="课堂作业" icon="todo-list-o" is-link :to="`${this.$route.params.courseId}/reports`")
  van-cell-group(title="论坛")
    van-cell(title="课堂论坛" icon="chat-o" is-link :to="`${this.$route.params.courseId}/topics`")
</template>

<style lang="stylus" scoped></style>
