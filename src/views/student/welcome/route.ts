import { RouteConfig } from '@/interfaces/IRoute';

const routes: RouteConfig[] = [
  {
    path: '/studying/welcome',
    name: 'welcomeIndex',
    component: () => import(/* webpackChunkName: "welcome" */ './Index.vue'),
    meta: {
      title: '报到流程',
      roles: ['Student'],
      requireAuth: true,
    },
  },
  {
    path: '/studying/welcome/tokens/:tokenId',
    name: 'welcomePlace',
    component: () => import(/* webpackChunkName: "welcome" */ './Token.vue'),
    meta: {
      title: '提交资料',
      roles: ['Student'],
      requireAuth: true,
    },
  },
];

export default routes;
