<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import workflowModel, {
  WorkflowTypes,
  IWorkflow,
  IWorkflowCorePlace,
  TransitionTypes,
  IWorkflowCore,
} from '@/models/bpm/workflow';
import session, { Session } from '@/models/session';
import { IInstance } from '@/models/bpm/instance';
import { WelcomeEntry, IWelcomeEntry } from '@/models/welcome/entry';
import tokenModel, { IToken } from '@/models/bpm/token';
import WelcomeFlow from '@/components/welcome/WelcomeFlow.vue';
import QrCode from '@/components/global/QrCode.vue';
import { WechatService } from '@/service/wechat';
import { OauthService } from '@/service/oauth';

@Component({
  components: {
    WelcomeFlow,
    QrCode,
  },
})
export default class WelcomeIndex extends Vue {
  qrCodeVisible: boolean = false;
  isDueDate: boolean = true;
  workflow: IWorkflow = { id: null };
  entry: IWelcomeEntry = {
    instance: {},
    flowable_info: {},
    student: {},
  };
  loading: boolean = true;
  invalidTypes: TransitionTypes[] = [TransitionTypes.Condition, TransitionTypes.Start, TransitionTypes.Formula];

  get currentUser() {
    return sessionStore.currentUser;
  }
  get instance() {
    return this.entry.instance || {};
  }
  get year() {
    return new Date().getFullYear();
  }
  // 用户可以操作的业务 place
  get realPlaces() {
    if (!this.workflow.core) return [];
    return (this.workflow.core.places || [])
      .filter((o: IWorkflowCorePlace) => !this.invalidTypes.includes(o.transition_type!))
      .filter(
        (o: IWorkflowCorePlace) =>
          !o.place_meta || !o.place_meta.major || o.place_meta.major.includes(this.entry.student.admit_major_name),
      )
      .sort((a: IWorkflowCorePlace, b: IWorkflowCorePlace) => (a.position || 100) - (b.position || 100));
  }
  get showTree() {
    return (this.workflow.core || ({} as IWorkflowCore)).tree;
  }
  get placeToSourceIdMap() {
    return this.showTree.reduce((obj, item) => {
      Object.assign(obj, { [item.target.id]: item.source.id });
      return obj;
    }, {});
  }
  get placeTokenMap() {
    return this.$utils.objectify(this.realTokens, 'place_id') as any;
  }
  getPlaceToken(place: IWorkflowCorePlace) {
    return this.placeTokenMap[place.id!] || {};
  }

  placeVisiable(place: IWorkflowCorePlace) {
    const token: IToken = this.placeTokenMap[place.id!];
    return token || (place && place.name && !place.name.startsWith('【'));
  }

  get realVisiblePlaces() {
    const places = this.realPlaces;
    return places.filter(place => {
      return (
        (places.find(item => item.id === this.placeToSourceIdMap[place.id as number]) ||
          this.getPlaceToken(place).id) &&
        this.placeVisiable(place)
      );
    });
  }
  get realTokens() {
    return (this.instance.tokens || []).filter(t => !this.invalidTypes.includes(t.transition_type!));
  }
  get progress() {
    const doneCount = this.realTokens.filter(o => o.state === 'completed').length;
    return {
      done: doneCount,
      total: this.realVisiblePlaces.length,
      percent: this.realVisiblePlaces.length ? Math.round((doneCount / this.realVisiblePlaces.length) * 100) : 0,
    };
  }
  // 学生二维码序列：学校 ASCII 值 + 00 + 学号 ASCII 值
  get qrCodeData() {
    if (sessionStore.account && this.instance.id) {
      const schoolEncode = Session.getNumberAsciiCode(sessionStore.schoolId);
      const instanceCode = `${this.instance.id}`
        .split('')
        .map(Session.getNumberAsciiCode)
        .join('');
      return `${schoolEncode}00${instanceCode}`;
    }
    return '';
  }

  get apartmentInfo() {
    return `${this.entry.student.building_name}# \
            ${this.entry.student.floor}层 \
            ${this.entry.student.room}房 \
            ${this.entry.student.bed}床`;
  }

  mounted() {
    this.fetchData();
  }

  async fetchData() {
    await this.fetchStudentEntry();
    await this.fetchWorkflow();
  }

  async fetchStudentEntry() {
    try {
      this.loading = true;
      const entryModel = new WelcomeEntry();
      const res = await entryModel.studentEntry();
      const token = res.data.instance.current_token!;
      if (token.transition_type === TransitionTypes.Start && token.state === 'processing') {
        await tokenModel.accept(token.id, '迎新报道流程开始');
        const { data } = await entryModel.studentEntry();
        this.entry = data;
      } else {
        this.entry = res.data;
      }
      // 检查时间
      const start = this.$dayjs(this.entry.flowable_info.activity_start_at);
      const end = this.$dayjs(this.entry.flowable_info.activity_end_at).add(1, 'day');
      const now = this.$dayjs();
      if (process.env.NODE_ENV === 'development') {
        this.isDueDate = true;
      } else {
        this.isDueDate = now.isAfter(start) && now.isBefore(end);
      }
      this.loading = false;
    } catch (error) {
      this.loading = false;
      this.$message.error('未能获取迎新活动信息');
    }
  }

  async fetchWorkflow() {
    try {
      if (!this.instance.workflow_id) return;
      this.loading = true;
      const { data } = await workflowModel.find(this.instance.workflow_id);
      this.workflow = data;
      this.loading = false;
    } catch (error) {
      this.loading = false;
      this.$message.error('未能获取迎新流程数据');
    }
  }

  chooseToken(place: IToken) {
    this.$router.push(`/studying/welcome/tokens/${place.id}`);
  }

  closeQrCode() {
    if (this.qrCodeVisible) {
      this.qrCodeVisible = false;
      this.fetchData();
    }
  }

  logout() {
    if (this.loading) return;
    this.loading = true;
    WechatService.unbind().finally(this.deleteSession);
  }

  deleteSession() {
    // 释放 openid
    const openid = window.localStorage.getItem('openid');
    if (openid) {
      OauthService.destroy(openid);
    }
    session.signOut().finally(() => {
      window.localStorage.clear();
      this.loading = false;
      window.location.href = 'http://wchattest.stiei.edu.cn/wx-test/?#/home';
    });
  }
}
</script>

<template lang="pug">
.welcome-wrapper
  .welcome-container.welcome-image-container(v-if='isDueDate')
    .welcome-base
      .box-student
        .logout(@click.stop='logout')
          span 退出
        .version
          | - 欢迎进入 {{ year }} 级新生报到流程 -
        .box-info
          .title
            span.title-text 你好，{{ currentUser.name }}
            van-icon.qrcode(name='qr', @click.stop='qrCodeVisible = true')
          .infos
            span.info 录取通知书号：{{ entry.student.code1 }}
            span.info 性别：{{ entry.student.sex }}
            .info 身份证：{{ currentUser.identity_id }}
            .info 专业：{{ entry.student.admit_major_name }}
            .info(v-if='this.entry.student.building_name && this.entry.state === "线下报到成功"')
              | 现场报到成功，你的寝室：{{ apartmentInfo }}
        .box-progress
          .count 完成进度 {{ progress.done }}/{{ progress.total }}
          van-progress.progress-bar(
            :percentage='progress.percent',
            :show-pivot='false',
            stroke-width='8px',
            color='#6DC37D'
          )
    .welcome-content(v-loading='loading')
      WelcomeFlow(:workflow='workflow', :tokens='realTokens', :places='realVisiblePlaces', @tokenClick='chooseToken')
    van-overlay(:show='qrCodeVisible', @click='qrCodeVisible = false', z-index='100')
      .qrcode-box(@click='closeQrCode')
        QrCode(:data='qrCodeData')
  .image-container(v-else)
    .date {{ $dayjs(entry.flowable_info.activity_start_at).format('M月D日起') }}
    img.welcome-image(src='@/assets/images/welcome/报道时间.png')
</template>

<style lang="stylus" scoped>
@import '~@/assets/styles/share/studyingWelcome.styl'

.image-container
  position relative
  width 100%
  .date
    position absolute
    top 30%
    display flex
    justify-content center
    width 100%
    font-weight 400
    font-style normal
    font-size 3rem
    font-family 'PangMenZhengDao'
    line-height 4rem
  .welcome-image
    display block
    width 100%
    line-height 0

.welcome-container
  .box-student
    position relative
    overflow hidden
    padding 10px 20px 24px
    color #fff
    .logout
      position absolute
      top 10px
      right 10px
      color rgba(255, 255, 255, 0.6)
      font-size 12px
    .version
      height 20px
      color rgba(255, 255, 255, 0.6)
      text-align center
      font-weight 400
      font-size 12px
      line-height 20px
    .box-info
      margin-top 16px
      margin-bottom 16px
      font-size 14px
      .title
        display flex
        justify-content space-between
        margin-bottom 14px
        color #fff
        font-weight 500
        font-size 24px
        .title-text
          line-height 32px
        .qrcode
          font-size 33px
      .infos
        margin-bottom 12px
        &:last-child
          margin-bottom 0
        .info
          white-space nowrap
          &:first-child
            margin-right 24px
    .box-progress
      .count
        margin-bottom 4px
        color rgba(255, 255, 255, 0.8)
        font-weight 500
        font-size 14px

.qrcode-box
  position absolute
  top 50%
  left 50%
  margin auto
  padding 10px
  width 280px
  height 280px
  background #fff
  transform translate(-50%, -50%)
</style>
