<template lang="pug">
NavContainer(title="实习" :loading="practiceStore.loading")
  .practice
    .header
      .info-card
        .name {{ practice.student_name }}
        .info-cell(v-for="(info, index) in infos" :key="index")
          span {{ info.label }}：{{ info.value }}
    .main
      .cell(v-for="(item, index) in modules" :key="index")
        van-cell(
          is-link
          :title="item.label"
          :icon="item.icon"
          :border="false"
          @click="onMenu(item)")
          template(slot="title")
            label {{ item.label }}
          template(slot="default")
            van-tag(:color="item.tag.color" :text-color="item.tag.textColor")
              span {{ item.tag.text }}
        .divider(v-if="index !== 0")
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';

@Component({
  components: {},
})
export default class Show extends Vue {
  get practiceStore() {
    return practiceStore || {};
  }
  get practice() {
    const practice: any = practiceStore.record || {};
    return {
      ...practice,
      id: practice.id,
      student_name: practice.student && practice.student.name,
      student_code: practice.student && practice.student.code,
      guide_teachers_text: (practice.guide_teachers || []).map((e: any) => e.name).join(','),
      start_at: practice.start_at ? this.$dayjs(practice.start_at).format('YYYY/MM/DD') : '',
      end_at: practice.end_at ? this.$dayjs(practice.end_at).format('YYYY/MM/DD') : '',
    };
  }
  get infos() {
    return [
      {
        label: '学号',
        key: 'student_code',
      },
      {
        label: '开始日期',
        key: 'start_at',
      },
      {
        label: '结束日期',
        key: 'end_at',
      },
      {
        label: '指导老师',
        key: 'guide_teachers_text',
      },
      {
        label: '实习单位',
        key: 'company',
      },
    ].map((item: any) => ({
      ...item,
      value: this.practice[item.key],
    }));
  }
  get modules() {
    return [
      {
        label: '实习周记',
        icon: 'notes-o',
        key: 'reports',
      },
      {
        label: '企业评价',
        icon: 'comment-o',
        key: 'evaluation',
      },
      {
        label: '实习报告',
        icon: 'orders-o',
        key: 'thesis',
      },
      {
        label: '答辩',
        icon: 'description',
        key: 'reply',
      },
      {
        label: '打卡情况',
        icon: 'location-o',
        key: 'registers',
      },
    ].map((item: any) => ({
      ...item,
      tag: this.initTag(item.key),
    }));
  }
  get statistics() {
    const { statistics } = this.practice;
    return {
      ...statistics,
      report: statistics.report || {},
      register: statistics.register || {},
    };
  }

  public created() {
    practiceStore.changeNamespace('student');
    if (this.practice.id) {
      practiceStore.find(this.practice.id);
    }
  }

  public onMenu(val: any) {
    this.$router.push(`/pt/student/practices/${this.practice.id}/${val.key}`);
  }

  public initTag(key: string): any {
    const { report, register } = this.statistics;
    return (
      ({
        reports: ({
          scored: {
            color: '#F0F9F2',
            textColor: '#6DC37D',
            text: '本周已评分',
          },
          published: {
            color: '#EDF7FF',
            textColor: '#3DA8F5',
            text: '本周已上传',
          },
          pending: {
            color: '#ffe1e1',
            textColor: '#ad0000',
            text: '本周待上传',
          },
        } as any)[report.state],
        evaluation: ({
          scored: {
            color: '#F0F9F2',
            textColor: '#6DC37D',
            text: '已评分',
          },
          submited: {
            color: '#EDF7FF',
            textColor: '#3DA8F5',
            text: '等待老师评分',
          },
          pending: {
            color: '#ffe1e1',
            textColor: '#ad0000',
            text: '待上传',
          },
        } as any)[this.statistics.company],
        thesis: ({
          scored: {
            color: '#F0F9F2',
            textColor: '#6DC37D',
            text: '已评分',
          },
          published: {
            color: '#EDF7FF',
            textColor: '#3DA8F5',
            text: '等待老师评分',
          },
          pending: {
            color: '#ffe1e1',
            textColor: '#ad0000',
            text: '待提交',
          },
        } as any)[this.statistics.thesis],
        reply: {
          color: '#f5f5f5',
          textColor: '#808080',
          text: this.statistics.defence === 'pending' ? '等待分配时间' : '未知',
        },
        registers: {
          color: '#f5f5f5',
          textColor: '#808080',
          text: `${register.done}/${register.total}`,
        },
      } as any)[key] || {
        color: '#f5f5f5',
        textColor: '#808080',
        text: '加载中',
      }
    );
  }
}
</script>

<style lang="stylus" scoped>
.practice
  width 100%
  height 100%
  background #fff
  .header
    padding 20px 16px
    .info-card
      padding 16px
      border-radius 5px
      background #3DA8F5
      color #fff
      .name
        margin 2px 0px 6px
        font-weight 500
        font-size 17px
        line-height 20px
      .info-cell
        margin-top 6px
        width 100%
        font-size 15px
        line-height 20px
  .main
    width 100%
    .cell
      position relative
      margin-bottom 12px
      i
        color #A6A6A6
      label
        margin-left 7px
        color #808080
        font-size 15px
        line-height 24px
      .divider
        position absolute
        top -17px
        left 24px
        z-index 99999
        height 20px
        border-left 1px #E5E5E5 solid
</style>
