<template lang="pug">
NavContainer(title="打卡情况" :loading="registerStore.loading")
  ListView(
    :data="registerStore.records"
    :loading="registerStore.loading"
    :pullup="!registerStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(registerStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .practice-register
      .header
        .ring-box
          G2Pie(:charData.sync="basePie" unit="次" :colors="['#1FA0FF', '#CDCBCE']" id="A1" v-if="basePie.length")
        .count-box
          .count-item(v-for="(item, index) in infos" :key="index")
            .key {{ item.title }}
            .value
              strong.count {{ item.count }}
              strong.unit 次
      .main
        Empty(v-if="registerStore.records.length === 0")
        .register(v-for="(item, index) in registerStore.records" :key="index")
          .date-box
            .date {{ $dayjs(item.created_at).format('MM-DD')}}
            .time(v-if="item.state === 'done'") {{ $dayjs(item.created_at).format('HH:mm')}}
          .address(:class="{'text-gray': item.state === 'undo'}")
            | {{ item.state === 'undo' ? '未打卡' : item.address || '暂无地址' }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import G2Pie from '@/components/pt/G2Pie.vue';
import practiceStore from '@/store/modules/pt/practice.store';
import registerStore from '@/store/modules/pt/register.store';

@Component({
  components: {
    G2Pie,
  },
})
export default class Registers extends Vue {
  get registerStore() {
    return registerStore || {};
  }
  get infoData() {
    const practice = practiceStore.record || {};
    const statistics = practice.statistics || {};
    return statistics.register || {};
  }
  get infos() {
    return [
      {
        title: '应打卡',
        key: 'total',
      },
      {
        title: '已打卡',
        key: 'done',
      },
      {
        title: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      ...item,
      count: this.infoData[item.key],
    }));
  }

  get basePie() {
    return [
      {
        label: '已打卡',
        key: 'done',
      },
      {
        label: '未打卡',
        key: 'undo',
      },
    ].map((item: any) => ({
      state: item.label,
      amount: this.infoData[item.key],
      percent: +((this.infoData[item.key] / this.infoData.total) * 100).toFixed(2),
    }));
  }

  public mounted() {
    registerStore.changeNamespace('student');
    practiceStore.changeNamespace('student');
    practiceStore.find(this.$route.params.practiceId);
    this.fetchData();
  }

  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.practiceId,
      shouldAppend: true,
      q: {
        s: ['created_at desc'],
      },
    };
    registerStore.fetchByParent(params);
  }
}
</script>

<style lang="stylus" scoped>
.practice-register
  min-height 100%
  width 100%
  background #fff
  .header
    padding 30px 10px 36px
    .ring-box
      width 100%
    .count-box
      display flex
      justify-content space-around
      align-items center
      width 100%
      .count-item
        margin-top 36px
        .key
          margin-bottom 8px
          color #A6A6A6
          font-weight 500
          font-size 12px
          line-height 14px
        .count
          color #383838
          font-weight 500
          font-size 30px
          font-family DINCond-Medium, DINCond
          line-height 30px
        .unit
          margin-left 4px
          color #808080
          font-size 12px
          line-height 18px
  .main
    width 100%
    .register
      display flex
      align-items center
      margin-bottom 10px
      padding 10px 16px
      width 100%
      .date-box
        width 60px
        .date
          color #383838
          font-size 15px
          line-height 20px
        .time
          margin-top 2px
          color #a6a6a6
          font-size 13px
          line-height 16px
      .address
        padding 12px
        width 100%
        border-radius 5px
        background #F5F5F5
        color #383838
        font-size 15px
        line-height 24px
</style>
