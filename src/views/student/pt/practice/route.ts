import { RouteConfig } from '@/interfaces/IRoute';

const Index = () => import(/* webpackChunkName: "pt_student_practice_index" */ './Index.vue');
const Show = () => import(/* webpackChunkName: "pt_student_practice_show" */ './Show.vue');
const Register = () => import(/* webpackChunkName: "pt_student_register_show" */ './Register.vue');
const Reports = () => import(/* webpackChunkName: "pt_student_practice_report_index" */ './Reports.vue');
const Report = () => import(/* webpackChunkName: "pt_student_practice_report_show" */ './Report.vue');
const Evaluation = () => import(/* webpackChunkName: "pt_student_practice_evaluation_show" */ './Evaluation.vue');
const Thesis = () => import(/* webpackChunkName: "pt_student_practice_thesis_show" */ './Thesis.vue');
const Reply = () => import(/* webpackChunkName: "pt_student_practice_reply_show" */ './Reply.vue');
const Registers = () => import(/* webpackChunkName: "pt_student_practice_register_index" */ './Registers.vue');

export default [
  {
    path: '/pt/student',
    component: Index,
    children: [
      {
        path: 'practice',
        name: 'pt_student_practice_show',
        component: Show,
        meta: {
          title: '实习管理',
          roles: ['Student'],
        },
      },
      {
        path: 'register',
        name: 'pt_student_register_show',
        component: Register,
        meta: {
          title: '签到打卡',
          roles: ['Student'],
        },
      },
    ],
  },
  {
    path: '/pt/student/practices/:practiceId/reports',
    name: 'pt_student_practice_report_index',
    component: Reports,
    meta: {
      title: '周记列表',
      roles: ['Student'],
    },
  },
  {
    path: '/pt/student/reports/:id',
    name: 'pt_student_practice_report_show',
    component: Report,
    meta: {
      title: '周记详情',
      roles: ['Student'],
    },
  },
  {
    path: '/pt/student/practices/:practiceId/evaluation',
    name: 'pt_student_practice_evaluation_show',
    component: Evaluation,
    meta: {
      title: '企业评价',
      roles: ['Student'],
    },
  },
  {
    path: '/pt/student/practices/:practiceId/thesis',
    name: 'pt_student_practice_thesis_show',
    component: Thesis,
    meta: {
      title: '实习报告',
      roles: ['Student'],
    },
  },
  {
    path: '/pt/student/practices/:practiceId/reply',
    name: 'pt_student_practice_reply_show',
    component: Reply,
    meta: {
      title: '答辩',
      roles: ['Student'],
    },
  },
  {
    path: '/pt/student/practices/:practiceId/registers',
    name: 'pt_student_practice_register_index',
    component: Registers,
    meta: {
      title: '打卡情况',
      roles: ['Student'],
    },
  },
] as RouteConfig[];
