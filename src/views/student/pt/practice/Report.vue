<template lang="pug">
NavContainer(:title="'第' + report.windex + '周'" :loading="reportStore.loading")
  ListView(
    :data="ptCommentStore.records"
    :loading="ptCommentStore.loading"
    :pullup="!ptCommentStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(ptCommentStore.currentPage + 1)"
    @refresh="fetchData(1)")
    .practice
      .module
        .module-top.flex-between
          .flex
            van-icon(name="coupon-o")
            span 我的周记
          .submit(v-if="report !== 'scored'")
            van-button(size="small" @click="onSubmit('pending')") 保存
            van-button(size="small" @click="onSubmit('published')") 提交
        .module-middle
          template(v-if="report.state !== 'scored'")
            FileUploader.uploader(
              :value="files"
              label="上传附件"
              :disabled="report.loading"
              :multiple="true"
              style="padding: 14px 0px 10px"
              @change="onUploader")
          template(v-else-if="files.length > 0")
            Attachments(:attachments="files")
      .score-cell
        .flex
          van-icon(name="sign")
          span 评分
        span(:class="+report.score > 0 ? 'text-primary' : 'text-gray'")
          | {{ +report.score > 0 ? +report.score + '分' : '等待评分' }}
      .module
        .module-top
          van-icon(name="comment-o")
          span 评论 · {{ ptCommentStore.totalCount || 0 }}
        .module-middle
          .comments(:style="{paddingBottom: visibleForm ? '160px' : '60px'}")
            .comment-cell(v-for="(item, index) in ptCommentStore.records" :keuy="index")
              .comment-top
                .flex
                  .avatar {{ (item.user_name || '').slice(0, 1) }}
                  .name {{ item.user_name }}
                .date
                  span {{ $dayjs(item.created_at).format('MM月DD日 HH:mm') }}
              .comment-content
                .text-pre {{ item.body }}
                .actions(
                  @click="onDelete(item)"
                  v-if="(role === 'student' && auth.id === item.user_id) || role !== 'student'")
                  van-icon(name="delete")
  .comment(@click="onCreate")
    van-icon(name="comment-o")
  .submit-box(v-if="visibleForm")
    van-field(
      v-model="comment.body"
      label="评论"
      type="textarea"
      rows="2"
      maxlength="1000"
      show-word-limit
      placeholder="添加评论")
    .submit
      van-button(
        @click="visibleForm = false") 取消
      van-button(
        type="info"
        :loading="ptCommentStore.loading"
        :disabled="!comment.body || ptCommentStore.loading"
        @click="submit") 发布
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import reportStore from '@/store/modules/pt/report.store';
import commentStore from '@/store/modules/comm/comment.store';
import ptCommentStore from '@/store/modules/pt/comment.store';
import commentReportStore from '@/store/modules/comm/report.store';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {},
})
export default class Report extends Vue {
  private visibleForm: boolean = false;
  private comment: any = {
    body: '',
  };
  get role() {
    return this.$tools.getRole();
  }
  get reportStore() {
    return reportStore || {};
  }
  get ptCommentStore() {
    return ptCommentStore || {};
  }
  get report() {
    return reportStore.record || {};
  }
  get files() {
    return this.report.files || [];
  }
  get auth() {
    return sessionStore.currentUser || {};
  }

  public created() {
    reportStore.changeNamespace(this.role);
    ptCommentStore.changeNamespace(this.role);
    ptCommentStore.changeParentResource('reports');
    reportStore.find(this.$route.params.id);
    this.fetchData();
  }

  public onSubmit(state: string) {
    const files = this.report.files || [];
    if (!this.report.body && !files.length) {
      this.$message.warning('周记内容不能为空！');
      return;
    }
    this.report.state = state;
    const val = {
      id: this.report.id,
      state,
      noCompare: true,
    };
    this.update(val);
  }

  public async update(val: any) {
    try {
      await commentReportStore.update(val);
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败');
    }
  }

  // 评论
  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.$route.params.id,
      shouldAppend: true,
    };
    ptCommentStore.fetchByParent(params);
  }

  public onCreate() {
    this.comment = {
      body: '',
    };
    this.visibleForm = true;
  }

  public async submit() {
    try {
      const val = {
        ...this.comment,
        commentable_id: this.$route.params.id,
        commentable_type: 'Report',
      };
      await commentStore.create(val);
      this.fetchData();
      this.visibleForm = false;
      this.$message.success('创建成功！');
    } catch (error) {
      this.$message.error('创建失败！');
    }
  }

  public onDelete(val: any) {
    this.$dialog
      .confirm({
        title: '提醒',
        message: '确定要继续操作吗？',
      })
      .then(async () => {
        this.delete(val);
      })
      .catch(() => {});
  }

  public async delete(val: any) {
    try {
      if (val.user_id === this.auth.id) {
        await commentStore.delete(val.id);
      } else {
        const params = {
          id: val.id,
          role: this.role,
          parentResource: 'reports',
          parentId: this.$route.params.id,
        };
        await ptCommentStore.deleteByParent(params);
      }
      this.fetchData();
      this.$message.success('删除成功！');
    } catch (error) {
      this.$message.error('删除失败！');
    }
  }

  public onUploader(fileItems: any[]) {
    this.report.files = fileItems;
    const obj: any = {
      ...this.report,
      attachments: {
        files: fileItems,
      },
      noCompare: true,
    };
    commentReportStore.update(obj);
  }
}
</script>

<style lang="stylus" scoped>
.practice
  padding 0px 20px
  width 100%
  background #fff
  .module
    padding 20px 0px
    width 100%
    .module-top
      display flex
      align-items center
      width 100%
      span
        margin-left 12px
        color #808080
        font-size 15px
        line-height 24px
      .submit
        display flex
        justify-content space-between
        width 130px
        .van-button
          padding 0px
          width 60px
          height 32px
          .van-button__text
            margin 0px
  .score-cell
    display flex
    justify-content space-between
    align-items center
    padding 20px 0px
    border-top 1px #e8e8e8 solid
    border-bottom 1px #e8e8e8 solid
    span
      margin-left 12px
      color #808080
      font-size 15px
      line-height 24px

.comments
  width 100%
  background #fff
  .comment-cell
    padding 10px
    width 100%
    border-bottom 1px #e8e8e8 solid
    .comment-top
      display flex
      justify-content space-between
      align-items center
      width 100%
      .avatar
        width 28px
        height 28px
        border-radius 50%
        background #CDD184
        color #fff
        text-align center
        font-weight 500s
        font-size 12px
        line-height 28px
      .name
        margin-left 8px
        color #383838
        font-weight 500s
        font-size 14px
        line-height 20px
    .comment-content
      display flex
      flex-wrap wrap
      align-items center
      margin-top 4px
      padding-left 36px
      width 100%
      .actions
        margin-left 14px
    span
      color #808080
      font-size 14px
      line-height 20px

.submit-box
  position fixed
  bottom 0px
  left 0px
  overflow auto
  width 100%
  height 160px
  border-top 1px #e8e8e8 solid
  background #fff
  box-shadow 0px 2px 12px 0px #eee
  .submit
    position fixed
    bottom 0px
    left 0px
    display flex
    justify-content flex-end
    padding 10px 20px
    width 100%
    border-top 1px #e8e8e8 solid
    background #fff
    .van-button
      margin-left 10px
      width 100px

.comment
  position fixed
  right 20px
  bottom 10px
  display flex
  justify-content center
  align-items center
  width 44px
  height 44px
  border-radius 50%
  background #fff
  box-shadow 0px 2px 4px 0px rgba(0, 0, 0, 0.1)

.van-icon
  color #A6A6A6
  font-size 16px
</style>
