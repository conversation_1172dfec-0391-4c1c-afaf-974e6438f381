<template lang="pug">
NavContainer(title="打卡" :loading="registerStore.loading")
  ListView(
    :data="registerStore.records"
    :loading="registerStore.loading"
    :pullup="!registerStore.finish"
    :pulldown="true"
    emptyText="暂无内容"
    @loadMore="fetchData(registerStore.currentPage + 1)"
    @refresh="fetchData()")
    .practice-register
      .header(:class="todayRegister.state === 'done' ? 'success-bg' : 'primary-bg'")
        template(v-if="todayRegister.state === 'done'")
          .round(style="margin: 0px auto")
            .title.text-success 打卡成功
            .time.text-success {{ $dayjs(todayRegister.created_at).format('HH:mm') }}
        template(v-else)
          .round-loop
            .middle-loop
              .round(@click="onDialog")
                .title 点击打卡
                .time {{ currentTime }}
        .date(:style="{marginTop: todayRegister.state === 'done' ? '30px' : '12px'}")
          span(v-if="todayRegister.state === 'done'") {{ $dayjs(todayRegister.created_at).format('YYYY.MM.DD') }}
          span(v-else) {{  $dayjs().format('YYYY.MM.DD') }}
        .address(@click="fetchAddres")
          van-icon(name="location-o")
          span {{ todayRegister.address || location.address || '重新定位' }}
      .main
        .title 打卡记录
        .register(v-for="(item, index) in registerStore.records" :key="index")
          .date-box
            .date {{ $dayjs(item.created_at).format('MM-DD')}}
            .time(v-if="item.state === 'done'") {{ $dayjs(item.created_at).format('HH:mm')}}
          .address(:class="{'text-gray': item.state === 'undo'}")
            | {{ item.state === 'undo' ? '未打卡' : item.address || '暂无地址' }}

  van-dialog(
    v-model="visible"
    show-cancel-button
    @cancel="visible = false"
    @confirm="onSubmit")
    .van-dialog__header(slot="title") {{ todayRegister.state === 'done' ? '更新打卡' : '确认打卡' }}
    .van-dialog__body
      .cell
        .key 打卡时间
        .value {{ $dayjs().format('YYYY.MM.DD HH:mm')}}
      .cell(style="border: none" @click="fetchAddres")
        .key 打卡地点
        .value {{ location.address || '重新定位'}}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import wechatSdk from '@/utils/wechatSdk';
import practiceStore from '@/store/modules/pt/practice.store';
import registerStore from '@/store/modules/pt/register.store';

@Component({
  components: {},
})
export default class Register extends Vue {
  private currentTime: string = '';
  private location: any = {
    address: '',
    lon: '',
    lat: '',
  };
  private visible: boolean = false;
  get registerStore() {
    return registerStore || {};
  }
  get practice() {
    return practiceStore.record || {};
  }
  get todayRegister() {
    const register: any = registerStore.records[0] || {};
    const registerDate = register.id ? this.$dayjs(register.created_at).format('YYYY-MM-DD') : '';
    return registerDate === this.$dayjs().format('YYYY-MM-DD') ? register : {};
  }

  public created() {
    this.nowTimes();
    practiceStore.changeNamespace('student');
    registerStore.changeNamespace('student');
    if (this.practice.id) {
      this.fetchData();
    }
    this.fetchAddres();
  }

  public fetchAddres() {
    wechatSdk.wx.checkJsApi({
      jsApiList: ['getLocation'],
      success: (res: any) => {
        if (!res.checkResult && res.checkResult.getLocation) {
          this.$message.error('你的微信版本太低，不支持微信定位，请升级到最新的微信版本！');
        } else {
          wechatSdk.wx.getLocation({
            type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
            success: async (result: any) => {
              const params = {
                lon: result.longitude,
                lat: result.latitude,
              };
              const { data } = await registerStore.getAddress(params);
              this.location = {
                ...params,
                address: `${data.province} ${data.city} ${data.district}`,
              };
            },
            cancel: (result: any) => {
              this.$message.error('你拒绝授权获取地理位置！');
            },
          });
        }
      },
    });
  }

  public fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 10,
      parentId: this.practice.id,
      shouldAppend: true,
      q: {
        s: ['created_at desc'],
      },
    };
    registerStore.fetchByParent(params);
  }

  onDialog() {
    if (this.todayRegister.id) {
      this.visible = true;
    } else {
      this.$message.warning('今日不可打卡');
    }
  }

  public onSubmit() {
    const { lon, lat, address } = this.location;
    if (!lon || !lat || !address) {
      this.$message.error('定位失败！');
      return;
    }
    if (this.todayRegister.id) {
      this.update();
    } else {
      this.create();
    }
  }

  public async create() {
    try {
      const obj: any = {
        ...this.location,
        source_id: this.practice.id,
        source_type: 'Pt::Practice',
        parentId: this.practice.id,
      };
      await registerStore.createByParent(obj);
      this.fetchData();
      this.$message.success('打卡成功！');
    } catch (error) {
      this.$message.error('打卡失败！');
    }
  }

  public async update() {
    try {
      const obj: any = {
        ...this.location,
        id: this.todayRegister.id,
        noCompare: true,
        state: 'done',
        created_at: new Date(),
      };
      await registerStore.update(obj);
      this.fetchData();
      this.$message.success('操作成功！');
    } catch (error) {
      this.$message.error('操作失败！');
    }
  }

  // 计时器
  public nowTimes() {
    window.setInterval(() => {
      this.currentTime = this.$dayjs().format('HH:mm:ss');
    }, 1000);
  }
}
</script>

<style lang="stylus" scoped>
.practice-register
  min-height 100%
  width 100%
  background #fff
  .header
    width 100%
    text-align center
    .round-loop
      margin 0px auto
      padding 7px
      width 166px
      height 166px
      border 3px solid rgba(255, 255, 255, 0.1)
      border-radius 50%
      box-shadow 0px 6px 16px 0px rgba(61, 168, 245, 0.3)
      .middle-loop
        padding 5px
        width 146px
        height 146px
        border 3px solid rgba(255, 255, 255, 0.4)
        border-radius 50%
        box-shadow 0px 6px 16px 0px rgba(61, 168, 245, 0.3)
    .round
      padding 40px 0px 34px
      width 130px
      height 130px
      border-radius 50%
      background #fff
      &:active
        opacity 0.8
      .title
        color #3DA8F5
        font-size 20px
        line-height 30px
      .time
        margin-top 2px
        color #3DA8F5
        font-size 15px
        line-height 24px
    .date
      margin 12px 0px 10px
      color #fff
      font-weight 500
      font-size 17px
      line-height 24px
    .address
      display flex
      justify-content center
      padding 0px 20px
      color #fff
      font-size 14px
      line-height 20px
      &:active
        opacity 0.8
      span
        margin-left 4px
      .van-icon
        font-size 16px
        line-height 20px
  .main
    width 100%
    .title
      padding 26px 0px 24px
      color #383838
      text-align center
      font-weight 500
      font-size 16px
      line-height 24px
    .register
      display flex
      align-items center
      margin-bottom 10px
      padding 10px 16px
      width 100%
      .date-box
        width 60px
        .date
          color #383838
          font-size 15px
          line-height 20px
        .time
          margin-top 2px
          color #a6a6a6
          font-size 13px
          line-height 16px
      .address
        padding 12px
        width 100%
        background #F5F5F5
        color #383838
        font-size 15px
        line-height 24px

.primary-bg
  padding 24px 0px 40px
  background linear-gradient(180deg, rgba(131, 211, 248, 1) 0%, rgba(61, 168, 245, 1) 100%)

.success-bg
  padding 42px 0px 40px
  background linear-gradient(360deg, rgba(134, 195, 109, 1) 0%, rgba(109, 195, 125, 1) 100%)

.van-dialog__header
  margin-top -8px
  padding 0px 0px 16px

.van-dialog__body
  padding 0px 16px
  width 100%
  border-top 1px #e8e8e8 solid
  border-bottom 1px #e8e8e8 solid
  .cell
    display flex
    padding 16px 0px
    width 100%
    border-bottom 1px #e8e8e8 solid
    &:active
      opacity 0.8
    .key
      min-width 60px
      color #808080
      font-size 15px
      line-height 24px
    .value
      margin-left 20px
      color #383838
      font-size 15px
      line-height 24px
</style>
