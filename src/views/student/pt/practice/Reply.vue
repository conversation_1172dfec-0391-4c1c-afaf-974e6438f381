<template lang="pug">
NavContainer.container(title="答辩" :loading="practiceStore.loading")
  Empty
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';

@Component({
  components: {},
})
export default class Reply extends Vue {
  get practiceStore() {
    return practiceStore || {};
  }
  public created() {}
}
</script>

<style lang="stylus" scoped>
.container
  width 100%
  height 100%
  background #fff
</style>
