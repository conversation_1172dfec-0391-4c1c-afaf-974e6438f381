<template lang="pug">
.container
  template(v-if="practice.id")
    router-view
  template(v-if="practice.state === 'starting'")
    van-tabbar.tabbar(:value="tabIndex" fixed safe-area-inset-bottom)
      van-tabbar-item(v-for="(tab, index) in tabs" :key="index" :icon="tab.icon" @click="onTab(tab, index)")
        | {{ tab.label }}
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';

@Component({
  components: {},
})
export default class Index extends Vue {
  private tabIndex: number = 0;
  private practice: any = {};
  get tabs() {
    return [
      {
        label: '打卡',
        path: 'pt_student_register_show',
        icon: 'location-o',
      },
      {
        label: '实习',
        path: 'pt_student_practice_show',
        icon: 'points',
      },
    ];
  }

  public created() {
    this.tabIndex = this.$route.name === 'pt_student_register_show' ? 0 : 1;
    practiceStore.changeNamespace('student');
    this.fetchData();
  }

  public async fetchData(page: number = 1) {
    const params = {
      page,
      per_page: 1,
    };
    const { data } = await practiceStore.fetch(params);
    const firstPractice = data.practices[0] || {};
    if (firstPractice.id) {
      if (firstPractice.state !== 'starting') {
        this.$router.replace('/pt/student/practice');
      }
      const res = await practiceStore.find(firstPractice.id);
      this.practice = res.data;
    }
  }

  public onTab(val: any, index: number) {
    if (this.tabIndex !== index) {
      this.tabIndex = index;
      this.$router.replace({ name: val.path });
    }
  }
}
</script>

<style lang="stylus" scoped>
.container
  padding-bottom 50px
  width 100%
  height 100%
  background #fff
  .tabbar
    background #f4f5f5
</style>
