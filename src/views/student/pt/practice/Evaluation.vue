<template lang="pug">
NavContainer.container(title="企业评价" :loading="practiceStore.loading")
  .steps
    van-steps(direction="vertical" :active="stepIndex")
      van-step(v-for="(item, index) in steps" :key="index" active-color="#3DA8F5")
        template(slot="active-icon")
          .point.active {{ index + 1 }}
        template(slot="inactive-icon")
          .point.inactive(v-if="stepIndex === 0") {{ index + 1 }}
          .point.success(v-else)
            van-icon(name="success" color="#3DA8F5")
        .module
          .title {{ item.label }}
          template(v-if="item.key === 'uploader'")
            Attachments(
              v-if="files.length > 0"
              :attachments="files")
            .hint(v-else) 请前往电脑端上传附件
          template(v-else-if="+company_info.score > 0")
            .score {{ +company_info.score }} 分
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import practiceStore from '@/store/modules/pt/practice.store';

@Component({
  components: {},
})
export default class Evaluation extends Vue {
  get practiceStore() {
    return practiceStore || {};
  }
  get practice() {
    return practiceStore.record || {};
  }
  get steps() {
    return [
      {
        label: '上传附件',
        key: 'uploader',
      },
      {
        label: +this.company_info.score > 0 ? '老师评分' : '等待老师评分',
        key: 'score',
      },
    ];
  }
  get company_info() {
    return this.practice.company_info || {};
  }
  get files() {
    return this.company_info.files || [];
  }
  get stepIndex() {
    const fileLength = this.files.length > 0 ? 1 : 0;
    return +this.company_info.score > 0 ? 2 : fileLength;
  }

  public created() {
    practiceStore.changeNamespace('student');
    practiceStore.find(this.$route.params.practiceId);
  }
}
</script>

<style lang="stylus" scoped>
.container
  width 100%
  height 100%
  background #fff
  .steps
    padding 14px 10px
    width 100%
    .point
      display flex
      justify-content center
      align-items center
      margin-top 8px
      width 28px
      height 28px
      border-radius 50%
      font-weight 600
      font-size 16px
    .active
      background #3DA8F5
      color #fff
    .inactive
      border 1px #CCCCCC solid
      background #fff
      color #CCCCCC
    .success
      border 1px #3DA8F5 solid
      background #fff
    .module
      padding 0px 0px 40px 4px
      width 100%
      .title
        color #383838
        font-size 15px
        line-height 24px
      .hint
        color #808080
        font-size 15px
        line-height 24px
      .score
        margin-top 10px
        color #383838
        font-weight 600
        font-size 16px
        line-height 24px
</style>
