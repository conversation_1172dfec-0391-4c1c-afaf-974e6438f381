import { ActiveModule, ActiveStore, getModule } from '@/lib/ActiveStore';
import { AssessmentUserScoredScore } from '@/models/assessment/user/scored/score';

@ActiveModule(AssessmentUserScoredScore, { name: 'AssessmentUserScoredScoreStore' })
export class AssessmentUserScoredScoreStore extends ActiveStore<IAssessmentScore> {}

export const assessmentUserScoredScoreStore = getModule(AssessmentUserScoredScoreStore);
