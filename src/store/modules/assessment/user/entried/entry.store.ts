import { ActiveModule, ActiveStore, getModule } from '@/lib/ActiveStore';
import { AssessmentUserEntriedEntry } from '@/models/assessment/user/entried/entry';

@ActiveModule(AssessmentUserEntriedEntry, { name: 'AssessmentUserEntriedEntryStore' })
export class AssessmentUserEntriedEntryStore extends ActiveStore<IAssessmentEntry> {}

export const assessmentUserEntriedEntryStore = getModule(AssessmentUserEntriedEntryStore);
