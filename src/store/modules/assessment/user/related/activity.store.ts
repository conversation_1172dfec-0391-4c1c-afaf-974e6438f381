import { ActiveModule, ActiveStore, getModule } from '@/lib/ActiveStore';
import { AssessmentUserRelatedActivity } from '@/models/assessment/user/related/activity';

@ActiveModule(AssessmentUserRelatedActivity, { name: 'AssessmentUserRelatedActivityStore' })
export class AssessmentUserRelatedActivityStore extends ActiveStore<IAssessmentActivity> {}

export const assessmentUserRelatedActivityStore = getModule(AssessmentUserRelatedActivityStore);
