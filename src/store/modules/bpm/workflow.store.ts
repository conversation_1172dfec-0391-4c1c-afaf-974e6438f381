import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import workflow, { IWorkflow, TransitionTypes, IWorkflowCorePlace } from '@/models/bpm/workflow';
import BaseStore, { InjectModel } from '../../BaseStore';

@InjectModel(workflow)
@Module({ store, dynamic: true, namespaced: true, name: 'bpmWorkflow' })
class WorkflowStore extends BaseStore<IWorkflow> {
  get formatedRecords(): IWorkflow[] {
    const stateEnum = { todo: '草稿箱', done: '已发布' };
    return this.records.map((o: IWorkflow) => ({
      ...o,
      stateText: stateEnum[o.state!],
    }));
  }

  @Action({ rawError: true })
  async find(id: number | string) {
    try {
      this.context.commit('SET_LOADING', true);
      const { data } = await workflow.find(id);
      const { fields } = data.form || { fields: [] };
      const startPlace = (data.core as any).places.find((o: any) => o.type === 'Places::StartPlace') || {
        fields: { fields: [] },
      };
      const newFormFields = workflow.getAccessibilityFields(fields, startPlace);
      const newData = {
        ...data,
        form: {
          fields: newFormFields,
        },
      };
      this.context.commit('SET_RECORD', newData);
      this.context.commit('SET_FORM_DATA', newData);
      this.context.commit('SET_LOADING', false);
      return { data: newData };
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }
}

export default getModule(WorkflowStore);
