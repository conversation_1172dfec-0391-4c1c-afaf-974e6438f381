import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import activity, { IActivity } from '@/models/meeting/activity';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(activity, 'meeting_activities');

interface IActivityExtra {
  typeText: string;
  balance: number;
}

@Module({ store, dynamic: true, namespaced: true, name: 'meetingActivity' })
class ActivityStore extends BaseModule<IActivity> {
  get typeMap() {
    return {
      'Meeting::ActivityAssign': { text: '管理员指定', value: 'Meeting::ActivityAssign' },
      'Meeting::ActivityInviteAuto': { text: '自动通过', value: 'Meeting::ActivityInviteAuto' },
      'Meeting::ActivityInvite': { text: '管理员审核', value: 'Meeting::ActivityInvite' },
    };
  }

  get activities(): Array<IActivity & IActivityExtra> {
    return this.records.map(o => ({
      ...o,
      typeText: ((this.typeMap as any)[this.record.type] || {}).text,
      balance: (o.limit_count || 0) - (o.supposed_count || 0),
    }));
  }

  get currentActivity(): IActivity & IActivityExtra {
    return {
      ...this.record,
      typeText: ((this.typeMap as any)[this.record.type] || {}).text,
      balance: (this.record.limit_count || 0) - (this.record.supposed_count || 0),
    };
  }
}

export default getModule(ActivityStore);
