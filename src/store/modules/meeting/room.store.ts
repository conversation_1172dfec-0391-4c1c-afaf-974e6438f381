import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import room, { IRoom } from '@/models/meeting/room';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(room, 'rooms');

@Module({ store, dynamic: true, namespaced: true, name: 'meetingRoom' })
class RoomStore extends BaseModule<IRoom> {}

export default getModule(RoomStore);
