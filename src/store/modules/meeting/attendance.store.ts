import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import attendance, { IAttendance } from '@/models/meeting/attendance';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(attendance, 'meeting_attendances');

@Module({ store, dynamic: true, namespaced: true, name: 'meetingAttendance' })
class AttendanceStore extends BaseModule<IAttendance> {
  @Action({ rawError: true })
  async cancel(id: number) {
    try {
      this.context.commit('SET_LOADING', true);
      await attendance.cancel(id);
      this.context.commit('UPDATE_RECORD', { state: '已取消' });
      this.context.commit('SET_LOADING', false);
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }
}

export default getModule(AttendanceStore);
