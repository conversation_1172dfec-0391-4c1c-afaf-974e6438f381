import { Action, getModule, Module, Mutation, MutationAction, VuexModule } from 'vuex-module-decorators';
import store, { getModulePersistState } from '@/store';
import { Session, IThirdAuthAccount, IAccount } from '@/models/session';
import student, { IStudentInfo } from '@/models/studying/student';
import { Teacher, ITeacher } from '@/models/teaching/teacher';
import { WechatService } from '@/service/wechat';
import { OauthService } from '@/service/oauth';

interface ISessionState {
  thirdAuthId?: string | null;
  schoolId: number;
  account: IAccount;
  currentUser: IStudentInfo;
}

// 获取持久化数据
const initialState = getModulePersistState('session');

@Module({ store, namespaced: true, name: 'session', dynamic: true })
class SessionStore extends VuexModule implements ISessionState {
  schoolId: number = 1;
  thirdAuthId: string = '5d2ee7620047759f710bce75';
  account: IAccount = initialState.account || {};
  currentUser: IStudentInfo | ITeacher = initialState.currentUser || {};

  get token() {
    return this.account.token;
  }
  get fileToken() {
    return this.account.fileToken;
  }
  get role() {
    return (this.account.type || 'Teacher').toLocaleLowerCase();
  }

  @Action({ rawError: true })
  public async signIn(account: IThirdAuthAccount) {
    const sessionInstance = new Session();
    const { data } = await sessionInstance.thirdSignIn({
      ...account,
      thirdAuthId: this.thirdAuthId,
    });
    this.SET_USER(data);
    await this.fetchInfo();
  }

  @Action({ rawError: true })
  public async wechatSignIn(code: string) {
    const { data } = await WechatService.signInWithWechatCode(code);
    this.SET_USER(data);
    await this.fetchInfo();
  }

  @Action({ rawError: true })
  public async oauthSignIn(code: string, openid: string, account: string) {
    const { data } = await OauthService.fetchInfo(code, openid, account);
    this.SET_USER(data);
    await this.fetchInfo();
  }

  @MutationAction({ mutate: ['account'], rawError: true })
  public async signOut() {
    const sessionInstance = new Session();
    await sessionInstance.signOut();
    return { account: null };
  }

  @Action({ rawError: true })
  public async check() {
    const sessionInstance = new Session();
    await sessionInstance.checkToken();
    await this.fetchInfo();
  }

  @Action({ rawError: true })
  public async fetchInfo() {
    if (this.account.type === 'Student') {
      const { data } = await student.info();
      this.SET_USER_INFO(data);
    } else {
      // Teacher | Expert (专家暂时都使用教室账号)
      const teacher = new Teacher();
      const { data } = await teacher.info();
      this.SET_USER_INFO(data);
    }
  }

  @Mutation
  public SET_USER(this: ISessionState, account: IAccount) {
    this.account = account;
  }
  @Mutation
  public SET_USER_INFO(this: ISessionState, info: IStudentInfo) {
    this.currentUser = info;
  }
  @Mutation
  public RESET(this: ISessionState) {
    this.account = {};
    this.currentUser = {};
  }
}

export default getModule(SessionStore);
