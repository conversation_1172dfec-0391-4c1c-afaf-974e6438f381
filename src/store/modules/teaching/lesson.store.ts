import lesson, { <PERSON>on, <PERSON>esson } from '@/models/teaching/lesson';
import store from '@/store';
import { Action, getModule, Module } from 'vuex-module-decorators';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(lesson, 'lessons');

@Module({ store, dynamic: true, namespaced: true, name: 'teachingLessons' })
class LessonStore extends BaseModule<ILesson> {
  @Action({ rawError: true })
  setRole(role: string = 'teacher') {
    this.setConfig({ namespace: `/teaching/${role}` });
  }
}

export default getModule(LessonStore);
