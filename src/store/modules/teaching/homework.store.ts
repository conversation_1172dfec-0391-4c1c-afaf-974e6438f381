import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import homeworkModel, { IHomework } from '@/models/teaching/homework';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(homeworkModel, 'reports');

@Module({ store, dynamic: true, namespaced: true, name: 'teachingHomework' })
class HomeworkStore extends BaseModule<IHomework> {
  @Action({ rawError: true })
  public async fetchHomeworks(params: any = {}) {
    const { data } = await homeworkModel.homeworks(params);
    return { data };
  }
  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'teacher') {
    homeworkModel.setConfig({
      namespace: `/teaching/${val}`,
    });
  }
}

export default getModule(HomeworkStore);
