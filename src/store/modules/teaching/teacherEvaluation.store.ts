import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import teacherEvaluation, { IEvaluation } from '@/models/teaching/teacherEvaluation';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(teacherEvaluation, 'teaching_evaluations');

@Module({ store, dynamic: true, namespaced: true, name: 'teachingEvaluation' })
class TeacherEvaluationStore extends BaseModule<IEvaluation> {}

export default getModule(TeacherEvaluationStore);
