import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import reportModel, { IReport } from '@/models/teaching/report';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(reportModel, 'reports');

@Module({ store, dynamic: true, namespaced: true, name: 'teachingReport' })
class ReportStore extends BaseModule<IReport> {
  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'teacher') {
    reportModel.setConfig({
      namespace: `/teaching/${val}`,
    });
  }

  // chang parent resource (参数：lessons, courses)
  @Action({ rawError: true })
  public changeParentResource(val: string = 'lessons') {
    reportModel.setConfig({
      parentResource: val,
    });
  }
}

export default getModule(ReportStore);
