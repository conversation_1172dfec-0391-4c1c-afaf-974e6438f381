import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import teacherModel, { ITeacher } from '@/models/hr/teacher';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(teacherModel, 'teachers');

@Module({ store, dynamic: true, namespaced: true, name: 'hrTeacher' })
class TeacherStore extends BaseModule<ITeacher> {}

export default getModule(TeacherStore);
