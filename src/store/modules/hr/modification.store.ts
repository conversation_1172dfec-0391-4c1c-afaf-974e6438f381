import { getModule, Module, Action } from 'vuex-module-decorators';
import BaseStore, { InjectModel } from '@/store/BaseStore';
import store from '@/store';
import tools from '@/utils/tools';
import { IModification, Modification } from '@/models/hr/modification';

// Base
class ModificationStore extends BaseStore<IModification> {}

// Role: admin
@InjectModel(new Modification('admin'))
@Module({ store, dynamic: true, namespaced: true, name: 'hrAdminModification' })
class AdminModificationStore extends ModificationStore {
  get modifications() {
    return (this.records || []).map((item: any) => ({
      ...item,
      ...tools.formatUserInfo(item.user),
      state_zh: tools.formatState(item.state),
    }));
  }
  get modification() {
    return this.record.id
      ? {
          ...this.record,
          ...tools.formatUserInfo(this.record.user),
          state_zh: tools.formatState(this.record.state),
        }
      : this.record;
  }
}
// Role: teacher
@InjectModel(new Modification('teacher'))
@Module({ store, dynamic: true, namespaced: true, name: 'hrTeacherModification' })
class TeacherModificationStore extends ModificationStore {
  get modifications() {
    return (this.records || []).map((item: any) => ({
      ...item,
      ...tools.formatUserInfo(item.user),
      state_zh: tools.formatState(item.state),
    }));
  }
  get modification() {
    return this.record.id
      ? {
          ...this.record,
          ...tools.formatUserInfo(this.record.user),
          state_zh: tools.formatState(this.record.state),
        }
      : this.record;
  }
}

export const adminModificationStore = getModule(AdminModificationStore);
export const teacherModificationStore = getModule(TeacherModificationStore);
