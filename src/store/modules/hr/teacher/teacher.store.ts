import { ActiveModule, ActiveStore, getModule } from '@/lib/ActiveStore';
import { ITeacher } from '../../../../models/teaching/teacher';
import { HrTeacherTeacher } from '../../../../models/hr/teacher/teacher';

@ActiveModule(HrTeacherTeacher, { name: 'HrTeacherTeacherStore' })
export class HrTeacherTeacherStore extends ActiveStore<ITeacher> {}

export const hrTeacherTeacherStore = getModule(HrTeacherTeacherStore);
