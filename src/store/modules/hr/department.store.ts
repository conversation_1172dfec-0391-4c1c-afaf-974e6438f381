import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import DepartmentModel, { IDepartment } from '@/models/hr/department';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(DepartmentModel, 'duty');

@Module({ store, dynamic: true, namespaced: true, name: 'duty' })
class DepartmentStore extends BaseModule<IDepartment> {}

export default getModule(DepartmentStore);
