import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import informAdvise, { IAdvise } from '@/models/inform/advise';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(informAdvise, 'advises');

@Module({ store, dynamic: true, namespaced: true, name: 'informAdvise' })
class InformAdviseStore extends BaseModule<IAdvise> {}

export default getModule(InformAdviseStore);
