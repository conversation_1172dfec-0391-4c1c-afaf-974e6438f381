import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import informMod, { IMod } from '@/models/inform/mod';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(informMod, 'inform_mods');

@Module({ store, dynamic: true, namespaced: true, name: 'informMod' })
class InformModStore extends BaseModule<IMod> {}

export default getModule(InformModStore);
