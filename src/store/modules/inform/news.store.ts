import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import informNews, { INews } from '@/models/inform/news';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(informNews, 'news');

@Module({ store, dynamic: true, namespaced: true, name: 'informNews' })
class InformNewsStore extends BaseModule<INews> {}

export default getModule(InformNewsStore);
