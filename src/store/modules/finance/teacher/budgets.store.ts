import { ActiveModule, ActiveStore, getModule } from '@/lib/ActiveStore';
import { FinanceTeacherBudgets } from '@/models/finance/teacher/budgets';
import { IBudget } from '@/models/finance/budget';

@ActiveModule(FinanceTeacherBudgets, { name: 'FinanceTeacherBudgetsStore' })
export class FinanceTeacherBudgetsStore extends ActiveStore<IBudget> {}

export const financeTeacherBudgetsStore = getModule(FinanceTeacherBudgetsStore);
