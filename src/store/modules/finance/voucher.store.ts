import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import BaseStore, { InjectModel } from '@/store/BaseStore';
import { FinanceVoucher, IVoucher } from '@/models/finance/voucher';
import { IBaseModel } from '../../../models/BaseModel';

const getRoleStore = (model: IBaseModel) => {
  const moduleKey = `finance_${model.parentResource}_${model.role}`;

  @InjectModel(model)
  @Module({ store, dynamic: true, namespaced: true, name: moduleKey })
  class FinanceVoucherStore extends BaseStore<IVoucher> {
    get voucherTypeText() {
      return {
        'Finance::RoutineVoucher': '日常报销',
        'Finance::OutsideVoucher': '差旅报销',
      };
    }
    get voucherStateText() {
      return {
        created: { label: '待提交', class: 'text-warning' },
        preparing: { label: '待处理', class: 'text-black' },
        processing: { label: '处理中', class: 'text-primary' },
        completed: { label: '已通过', class: 'text-success' },
        rejected: { label: '已拒绝', class: 'text-danger' },
        canceled: { label: '已取消', class: 'text-gray' },
      };
    }
  }
  return getModule(FinanceVoucherStore);
};
export namespace VoucherStore {
  export const withActivityTeacher = getRoleStore(FinanceVoucher.withActivityTeacher);
}
