import { Mutation, VuexModule, Module, getModule } from 'vuex-module-decorators';
import store from '@/store';

export interface IBaseModuleState {
  loading: boolean;
}

@Module({ store, name: 'base', namespaced: true, dynamic: true })
export class BaseModule extends VuexModule implements IBaseModuleState {
  readonly loading: boolean = false;

  @Mutation
  public SET_LOADING(this: IBaseModuleState, loading: boolean) {
    this.loading = loading;
  }
}

export const baseStore = getModule(BaseModule);
