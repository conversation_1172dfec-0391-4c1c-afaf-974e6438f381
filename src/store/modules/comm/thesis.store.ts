import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import thesisModel, { IThesis } from '@/models/comm/thesis';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(thesisModel, 'theses');

@Module({ store, dynamic: true, namespaced: true, name: 'commThesis' })
class ThesisStore extends BaseModule<IThesis> {}

export default getModule(ThesisStore);
