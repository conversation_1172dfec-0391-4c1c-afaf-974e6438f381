import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import registerModel, { IRegister } from '@/models/comm/register';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(registerModel, 'registers');

@Module({ store, dynamic: true, namespaced: true, name: 'commRegister' })
class RegisterStore extends BaseModule<IRegister> {}

export default getModule(RegisterStore);
