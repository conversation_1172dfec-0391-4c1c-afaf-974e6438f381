import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import topicModel, { ITopic } from '@/models/comm/topic';
import BaseStore, { InjectModel } from '../../BaseStore';

@InjectModel(topicModel)
@Module({ store, dynamic: true, namespaced: true, name: 'commTopic' })
class TopicStore extends BaseStore<ITopic> {
  @Action({ rawError: true })
  public async fetchByParent(params: any = {}) {
    const { data } = await topicModel.indexByParent(params);
    return { data };
  }

  @Action({ rawError: true })
  public async findByParent(params: any = {}) {
    const { data } = await topicModel.findByParent(params);
    return { data };
  }

  @Action({ rawError: true })
  public async deleteByParent(params: any = {}) {
    const { data } = await topicModel.deleteByParent(params);
    return { data };
  }
}

export default getModule(TopicStore);
