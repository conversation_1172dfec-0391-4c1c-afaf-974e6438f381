import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import formworkModel, { IFormwork } from '@/models/comm/formwork';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(formworkModel, 'formworks');

@Module({ store, dynamic: true, namespaced: true, name: 'commFormwork' })
class FormworkStore extends BaseModule<IFormwork> {
  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'teacher') {
    formworkModel.setConfig({
      namespace: `/comm/${val}`,
    });
  }
}

export default getModule(FormworkStore);
