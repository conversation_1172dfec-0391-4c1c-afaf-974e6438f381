import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import commentModel, { IComment } from '@/models/comm/comment';
import BaseStore, { InjectModel } from '../../BaseStore';

@InjectModel(commentModel)
@Module({ store, dynamic: true, namespaced: true, name: 'commComment' })
class ReportStore extends BaseStore<IComment> {
  @Action({ rawError: true })
  public async fetchByParent(params: any = {}) {
    const { data } = await commentModel.indexByParent(params);
    return { data };
  }
  @Action({ rawError: true })
  public async deleteByParent(params: any = {}) {
    const { data } = await commentModel.deleteByParent(params);
    return { data };
  }
}

export default getModule(ReportStore);
