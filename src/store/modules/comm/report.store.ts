import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import reportModel, { IReport } from '@/models/comm/report';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(reportModel, 'reports');

@Module({ store, dynamic: true, namespaced: true, name: 'commReport' })
class ReportStore extends BaseModule<IReport> {}

export default getModule(ReportStore);
