import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import tofuApp, { ITofu } from '@/models/tofu/app';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(tofuApp, 'apps');

@Module({ store, dynamic: true, namespaced: true, name: 'tofuApp' })
class TofuAppStore extends BaseModule<ITofu> {}

export default getModule(TofuAppStore);
