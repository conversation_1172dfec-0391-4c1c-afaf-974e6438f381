import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import tofuStar, { ITofuStar } from '@/models/tofu/star';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(tofuStar, 'stars');

@Module({ store, dynamic: true, namespaced: true, name: 'tofuStar' })
class TofuStarStore extends BaseModule<ITofuStar> {}

export default getModule(TofuStarStore);
