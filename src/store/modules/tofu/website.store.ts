import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import tofuWebsite, { ITofuWebsite } from '@/models/tofu/website';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(tofuWebsite, 'websites');

@Module({ store, dynamic: true, namespaced: true, name: 'tofuWebsite' })
class TofuWebsiteStore extends BaseModule<ITofuWebsite> {}

export default getModule(TofuWebsiteStore);
