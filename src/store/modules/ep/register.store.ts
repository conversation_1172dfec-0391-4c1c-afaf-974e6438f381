import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import { Register, IRegister } from '@/models/ep/register';
import BaseStore, { InjectModel } from '../../BaseStore';

const userRegister = new Register('user');
const inspectRegister = new Register('inspect');
const adminRegister = new Register('admin');

@InjectModel(userRegister)
@Module({ store, dynamic: true, namespaced: true, name: 'epUserRegister' })
class UserRegisterStore extends BaseStore<IRegister> {
  @Action({ rawError: true })
  getAddress(params: any = {}) {
    return userRegister.getAddress(params);
  }
}

@InjectModel(inspectRegister)
@Module({ store, dynamic: true, namespaced: true, name: 'epInspectRegister' })
class InspectRegisterStore extends BaseStore<IRegister> {}

@InjectModel(adminRegister)
@Module({ store, dynamic: true, namespaced: true, name: 'epAdminRegister' })
class AdminRegisterStore extends BaseStore<IRegister> {}

export const userRegisterStore = getModule(UserRegisterStore);
export const inspectRegisterStore = getModule(InspectRegisterStore);
export const adminRegisterStore = getModule(AdminRegisterStore);
