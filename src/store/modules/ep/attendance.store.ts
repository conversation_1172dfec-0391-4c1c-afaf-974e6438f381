import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import attendanceModel, { IAttendance } from '@/models/ep/attendance';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(attendanceModel, 'attendances');

@Module({ store, dynamic: true, namespaced: true, name: 'ptAttendance' })
class AttendanceStore extends BaseModule<IAttendance> {
  get attendances() {
    return this.records.map((item: any) => ({
      ...item,
      user_name: item.user.name,
      user_code: item.user.code,
      user_major_name: item.user.major_name,
      user_college_name: item.user.college_name,
      register: item.nested_infos && item.nested_infos.register ? item.nested_infos.register : {},
    }));
  }
  get attendance() {
    return this.record.id
      ? {
          ...this.record,
          user_name: this.record.user.name,
          user_code: this.record.user.code,
          user_major_name: this.record.user.major_name,
          user_college_name: this.record.user.college_name,
          register:
            this.record.nested_infos && this.record.nested_infos.register ? this.record.nested_infos.register : {},
        }
      : this.record;
  }

  @Action({ rawError: true })
  public async changeNamespace(role: string = 'admin') {
    attendanceModel.setConfig({
      namespace: `/ep/${role}`,
    });
  }
}

export default getModule(AttendanceStore);
