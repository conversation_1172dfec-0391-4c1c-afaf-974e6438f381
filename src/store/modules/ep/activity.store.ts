import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import { Activity, IActivity } from '@/models/ep/activity';
import dayjs from 'dayjs';
import BaseStore, { InjectModel } from '../../BaseStore';

const adminActivity = new Activity('admin');
const inspectActivity = new Activity('inspect');

@InjectModel(adminActivity)
@Module({ store, dynamic: true, namespaced: true, name: 'epActivity' })
class AdminActivityStore extends BaseStore<IActivity> {
  @Action({ rawError: true })
  public async getRegisters(params: any = {}) {
    const { data } = await adminActivity.registers(params);
    return { data };
  }

  @Action({ rawError: true })
  public async fetchQuestions(params: any = {}) {
    const { data } = await adminActivity.questions(params);
    return { data };
  }

  @Action({ rawError: true })
  public async fetchPrograms(params: any = {}) {
    const { data } = await adminActivity.programs(params);
    return { data };
  }
}

@InjectModel(inspectActivity)
@Module({ store, dynamic: true, namespaced: true, name: 'epInspectActivity' })
class InspectActivityStore extends BaseStore<IActivity> {
  get activities() {
    return (this.records || []).map((item: any) => ({
      ...item,
      zh_state: (this.states.find((e: any) => e.value === item.state) as any).label,
    }));
  }
  get activity() {
    return this.record.id
      ? {
          ...this.record,
          start_at: this.record.start_at ? dayjs(this.record.start_at).format('YYYY-MM-DD') : '',
          end_at: this.record.end_at ? dayjs(this.record.end_at).format('YYYY-MM-DD') : '',
          zh_state: this.record.state ? (this.states.find((e: any) => e.value === this.record.state) as any).label : '',
        }
      : this.record;
  }
  get states() {
    return [
      {
        label: '未开始',
        value: 'pending',
      },
      {
        label: '进行中',
        value: 'starting',
      },
      {
        label: '已完成',
        value: 'completed',
      },
    ];
  }

  @Action({ rawError: true })
  public async getRegisters(params: any = {}) {
    const { data } = await inspectActivity.registers(params);
    return { data };
  }

  @Action({ rawError: true })
  public async fetchQuestions(params: any = {}) {
    const { data } = await inspectActivity.questions(params);
    return { data };
  }

  @Action({ rawError: true })
  public async fetchPrograms(params: any = {}) {
    const { data } = await inspectActivity.programs(params);
    return { data };
  }
}

export const adminActivityStore = getModule(AdminActivityStore);
export const inspectActivityStore = getModule(InspectActivityStore);
