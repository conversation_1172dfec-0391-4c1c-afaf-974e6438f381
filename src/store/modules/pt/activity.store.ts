import { getModule, Module, Action } from 'vuex-module-decorators';
import dayjs from 'dayjs';
import store from '@/store';
import activityModel, { IActivity } from '@/models/pt/activity';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(activityModel, 'activities');

@Module({ store, dynamic: true, namespaced: true, name: 'ptActivity' })
class ActivityStore extends BaseModule<IActivity> {
  get activities() {
    return (this.records || []).map((item: any) => ({
      ...item,
      zh_state: (this.states.find((e: any) => e.value === item.state) as any).label,
    }));
  }
  get activity() {
    return {
      ...this.record,
      start_at: this.record.start_at ? dayjs(this.record.start_at).format('YYYY-MM-DD') : '',
      end_at: this.record.end_at ? dayjs(this.record.end_at).format('YYYY-MM-DD') : '',
      zh_state: this.record.state ? (this.states.find((e: any) => e.value === this.record.state) as any).label : '',
    };
  }
  get states() {
    return [
      {
        label: '未开始',
        value: 'pending',
      },
      {
        label: '进行中',
        value: 'starting',
      },
      {
        label: '已完成',
        value: 'completed',
      },
      {
        label: '已取消',
        value: 'canceled',
      },
    ];
  }

  // teacher统计
  @Action({ rawError: true })
  public async getStatistics(params: any = {}) {
    const { data } = await activityModel.statistics(params);
    return { data };
  }

  @Action({ rawError: true })
  public async getRegisters(params: any = {}) {
    const { data } = await activityModel.registers(params);
    return { data };
  }

  @Action({ rawError: true })
  public async getReports(params: any = {}) {
    const { data } = await activityModel.reports(params);
    return { data };
  }

  @Action({ rawError: true })
  public async getCompanies(params: any = {}) {
    const { data } = await activityModel.companies(params);
    return { data };
  }

  @Action({ rawError: true })
  public async getTheses(params: any = {}) {
    const { data } = await activityModel.theses(params);
    return { data };
  }

  // chang namespace value: admin, teacher, student
  @Action({ rawError: true })
  public changeNamespace(val: string = 'admin') {
    activityModel.setConfig({
      namespace: `/pt/${val}`,
    });
  }
}

export default getModule(ActivityStore);
