import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import thesisModel, { IThesis } from '@/models/pt/thesis';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(thesisModel, 'theses');

@Module({ store, dynamic: true, namespaced: true, name: 'ptThesis' })
class ThesisStore extends BaseModule<IThesis> {
  public async fetchComments(params: any = {}) {
    const { data } = await thesisModel.comments(params);
    return { data };
  }

  @Action({ rawError: true })
  public async deleteComment(params: any = {}) {
    const { data } = await thesisModel.deleteComment(params);
    return { data };
  }

  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'admin') {
    thesisModel.setConfig({
      namespace: `/pt/${val}`,
    });
  }
}

export default getModule(ThesisStore);
