import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import commentModel, { IComment } from '@/models/pt/comment';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(commentModel, 'comments');

@Module({ store, dynamic: true, namespaced: true, name: 'ptComment' })
class ReportStore extends BaseModule<IComment> {
  @Action({ rawError: true })
  public async deleteByParent(params: any = {}) {
    const { data } = await commentModel.deleteByParent(params);
    return { data };
  }
  @Action({ rawError: true })
  public changeNamespace(val: string = 'admin') {
    commentModel.setConfig({
      namespace: `/pt/${val}`,
    });
  }

  @Action({ rawError: true })
  public changeParentResource(val: string = 'reports') {
    commentModel.setConfig({
      parentResource: val,
    });
  }
}

export default getModule(ReportStore);
