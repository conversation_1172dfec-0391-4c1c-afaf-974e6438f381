import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import registerModel, { IRegister } from '@/models/pt/register';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(registerModel, 'registers');

@Module({ store, dynamic: true, namespaced: true, name: 'ptRegister' })
class registerStore extends BaseModule<IRegister> {
  @Action({ rawError: true })
  public async getAddress(params: any = {}) {
    const { data } = await registerModel.getAddress(params);
    return { data };
  }
  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'admin') {
    registerModel.setConfig({
      namespace: `/pt/${val}`,
    });
  }
}

export default getModule(registerStore);
