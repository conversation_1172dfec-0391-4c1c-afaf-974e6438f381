import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import practiceModel, { IPractice } from '@/models/pt/practice';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(practiceModel, 'practices');

@Module({ store, dynamic: true, namespaced: true, name: 'ptPractice' })
class PracticeStore extends BaseModule<IPractice> {
  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'admin') {
    practiceModel.setConfig({
      namespace: `/pt/${val}`,
    });
  }
}

export default getModule(PracticeStore);
