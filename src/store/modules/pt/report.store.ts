import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import reportModel, { IReport } from '@/models/pt/report';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(reportModel, 'reports');

@Module({ store, dynamic: true, namespaced: true, name: 'ptReport' })
class ReportStore extends BaseModule<IReport> {
  @Action({ rawError: true })
  public async fetchComments(params: any = {}) {
    const { data } = await reportModel.comments(params);
    return { data };
  }

  @Action({ rawError: true })
  public async deleteComment(params: any = {}) {
    const { data } = await reportModel.deleteComment(params);
    return { data };
  }
  // chang namespace
  @Action({ rawError: true })
  public changeNamespace(val: string = 'admin') {
    reportModel.setConfig({
      namespace: `/pt/${val}`,
    });
  }
}

export default getModule(ReportStore);
