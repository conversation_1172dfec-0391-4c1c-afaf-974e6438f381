import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import room, { IConferenceRoom } from '@/models/conference/conference_room';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(room, 'meeting_rooms');

@Module({ store, dynamic: true, namespaced: true, name: 'meetingRoom' })
class ConferenceRoomStore extends BaseModule<IConferenceRoom> {}

export default getModule(ConferenceRoomStore);
