import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import { IActivityMeeting, ActivityMeeting } from '@/models/conference/activity_meeting';
import createBaseModule from '../../createBaseModule';

const activity_meeting = new ActivityMeeting();
const BaseModule = createBaseModule(activity_meeting, 'activity_meetings');

@Module({ store, dynamic: true, namespaced: true, name: 'ActivityMeeting' })
class ActivityMeetingStore extends BaseModule<IActivityMeeting> {}

export default getModule(ActivityMeetingStore);
