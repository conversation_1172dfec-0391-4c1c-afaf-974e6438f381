import { getModule, Module, Action } from 'vuex-module-decorators';
import store from '@/store';
import activity, { Activity, IActivity } from '@/models/assess/activity';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(activity, 'access_activities');

@Module({ store, dynamic: true, namespaced: true, name: 'assessActivities' })
class AssessActivityStore extends BaseModule<IActivity> {
  get stageRecords() {
    return this.records.map(o => ({
      ...o,
      ...Activity.getActivityExtra(o),
      entry_meta: o.entry_meta || {},
    }));
  }
  get stageRecord() {
    return {
      ...this.record,
      ...Activity.getActivityExtra(this.record),
      entry_meta: this.record.entry_meta || {},
    };
  }
}

export default getModule(AssessActivityStore);
