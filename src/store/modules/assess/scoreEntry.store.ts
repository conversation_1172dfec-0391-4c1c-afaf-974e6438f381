// 待考核人员
import { getModule, Module } from 'vuex-module-decorators';
import store from '@/store';
import entry, { IEntry } from '@/models/assess/scoreEntry';
import createBaseModule from '../../createBaseModule';

const BaseModule = createBaseModule(entry, 'access_entries');

@Module({ store, dynamic: true, namespaced: true, name: 'assessScoreEntries' })
class AssessScoreEntryStore extends BaseModule<IEntry> {}

export default getModule(AssessScoreEntryStore);
