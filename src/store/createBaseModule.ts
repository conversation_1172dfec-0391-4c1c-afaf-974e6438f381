import { Action, Mutation, VuexModule } from 'vuex-module-decorators';
import diff from '@/utils/diff';
import { IBaseModel, IModelConfig } from '../models/BaseModel';

interface IModelRequired {
  id?: number | string | null | undefined;
  noCompare?: boolean; // 是否在更新时比对数据
}
interface IAssociationParams {
  parentId?: number | string;
  shouldAppend?: boolean; // 是否追加元素，而不是整页替换, 手机端默认为追加元素
  [key: string]: any;
}
interface IParams {
  shouldAppend?: boolean; // 是否追加元素，而不是整页替换
  [key: string]: any;
}

interface IIndexResponse {
  current_page?: number;
  total_pages?: number;
  total_count?: number;
  [key: string]: any;
}

export default function createBaseModule(model: IBaseModel, indexKey: string) {
  class BaseStore<IModel extends IModelRequired> extends VuexModule {
    // data
    perPage: number = 15;
    currentPage: number = 0;
    totalPages: number = 1;
    totalCount: number = 0;
    records: IModel[] = [];
    record: IModel | IObject = {};
    formData: IModel | IObject = {};
    loading: boolean = false;
    finish: boolean = false;

    // =============== actions ===============
    @Action({ rawError: true })
    async fetch(params?: IParams) {
      try {
        this.context.commit('SET_LOADING', true);
        const { data } = await model.index({
          per_page: this.perPage,
          page: this.currentPage,
          ...params,
        });
        if (params && params.shouldAppend === false) {
          this.context.commit('SET_RECORDS', data);
        } else {
          this.context.commit('APPEND_RECORDS', data);
        }
        this.context.commit('SET_LOADING', false);
        return { data };
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }

    @Action({ rawError: true })
    async find(id: number | string) {
      try {
        this.context.commit('SET_LOADING', true);
        const { data } = await model.find(id);
        this.context.commit('SET_RECORD', data);
        this.context.commit('SET_FORM_DATA', data);
        this.context.commit('SET_LOADING', false);
        return { data };
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }

    @Action({ rawError: true })
    async create(formData: IModel) {
      try {
        this.context.commit('SET_LOADING', true);
        const { data } = await model.create(formData);
        this.context.commit('ADD_RECORD', data);
        this.context.commit('SET_LOADING', false);
        return { data };
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }

    @Action({ rawError: true })
    async update(formData: IModel) {
      try {
        this.context.commit('SET_LOADING', true);
        if (!formData.id) {
          throw new Error('Failed: id is empty.');
        }
        if (formData.noCompare) {
          await model.update(formData);
        } else {
          // Get origin record
          let originData: any = null;
          if (this.record.id === formData.id) {
            originData = this.record;
          } else {
            const existOne: any = this.records.find(o => o.id === formData.id);
            if (existOne) {
              originData = existOne;
            } else {
              const { data } = model.find(formData.id);
              originData = data;
            }
          }
          const patchData: IObject = diff(originData, formData);
          patchData.id = formData.id;
          await model.update(patchData);
        }
        this.context.commit('UPDATE_RECORD', formData);
        this.context.commit('SET_LOADING', false);
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }

    @Action({ rawError: true })
    async delete(id: number | string) {
      try {
        this.context.commit('SET_LOADING', true);
        await model.delete(id);
        this.context.commit('DELETE_RECORD', id);
        this.context.commit('SET_LOADING', false);
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }

    /**
     * fetchByParent 只使用于标准路由
     */
    @Action({ rawError: true })
    async fetchByParent(params: IAssociationParams) {
      try {
        this.context.commit('SET_LOADING', true);
        const realParams = { ...params };
        delete realParams.parentId;
        const { data } = await model.indexByParent(params.parentId!, {
          per_page: this.perPage,
          ...realParams,
        });
        // 一对一
        if (data.id) {
          this.context.commit('SET_LOADING', false);
          this.context.commit('SET_RECORD', data);
          this.context.commit('SET_FORM_DATA', data);
        } else if (params.shouldAppend === false) {
          // 一对多
          this.context.commit('SET_RECORDS', data);
        } else {
          // 一对多
          this.context.commit('APPEND_RECORDS', data);
        }
        this.context.commit('SET_LOADING', false);
        return { data };
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }

    /**
     * createByParent 只使用于标准路由
     */
    @Action({ rawError: true })
    async createByParent(payload: IAssociationParams & IModel) {
      try {
        this.context.commit('SET_LOADING', true);
        const realPayload = { ...payload };
        delete realPayload.parentId;
        const { data } = await model.createByParent(payload.parentId!, realPayload);
        this.context.commit('ADD_RECORD', data);
        this.context.commit('SET_LOADING', false);
        return { data };
      } catch (error) {
        this.context.commit('SET_LOADING', false);
        throw error;
      }
    }
    /**
     * 手动设置模型参数
     * @param config 模型参数
     */
    @Action({ rawError: true })
    setConfig(config: IModelConfig | IObject) {
      model.setConfig(config);
    }

    @Action({ rawError: true })
    async reset() {
      try {
        const data = {
          current_page: 0,
          total_pages: 0,
          total_count: 0,
          [indexKey]: [],
        };
        this.context.commit('SET_RECORD', data);
        this.context.commit('SET_RECORDS', data);
        this.context.commit('SET_FORM_DATA', data);
      } catch (error) {
        throw error;
      }
    }

    // =============== mutations ===============
    @Mutation
    public SET_RECORD(this: any, payload: IModel | IObject) {
      this.record = payload;
    }
    @Mutation
    public SET_FORM_DATA(this: any, payload: IModel | IObject) {
      const deepCopyPayload = JSON.parse(JSON.stringify(payload));
      this.formData = deepCopyPayload;
    }
    @Mutation
    public SET_RECORDS(this: any, payload: IIndexResponse) {
      this.currentPage = typeof payload.current_page === 'number' ? payload.current_page : this.currentPage;
      this.totalPages = typeof payload.total_pages === 'number' ? payload.total_pages : this.totalPages;
      this.totalCount = typeof payload.total_count === 'number' ? payload.total_count : this.totalCount;
      this.records = payload[indexKey] || this.records;
      this.finish = this.currentPage >= this.totalPages;
    }
    @Mutation
    public APPEND_RECORDS(this: any, payload: IIndexResponse) {
      this.currentPage = typeof payload.current_page === 'number' ? payload.current_page : this.currentPage;
      this.totalPages = typeof payload.total_pages === 'number' ? payload.total_pages : this.totalPages;
      this.totalCount = typeof payload.total_count === 'number' ? payload.total_count : this.totalCount;
      this.finish = this.currentPage >= this.totalPages;
      if (this.currentPage === 1) {
        this.records = payload[indexKey] || this.records;
      } else {
        this.records = this.records.concat(payload[indexKey] || []);
      }
    }
    @Mutation
    public ADD_RECORD(this: any, payload: IModel) {
      this.records.push(payload);
    }
    @Mutation
    public UPDATE_RECORD(this: any, payload: IModel) {
      const index = this.records.findIndex((o: IModel) => o.id === payload.id);
      if (index >= 0) {
        Object.assign(this.records[index], payload);
      }
      if (payload.id === this.record.id) {
        Object.assign(this.record, payload);
      }
    }
    @Mutation
    public DELETE_RECORD(this: any, id: number) {
      const index = this.records.findIndex((o: IModel) => o.id === id);
      if (index >= 0) {
        this.records.splice(index, 1);
      }
      if (id === this.record.id) {
        this.record = {};
      }
    }
    @Mutation
    public SET_LOADING(this: any, payload: boolean) {
      this.loading = payload;
    }
  }
  return BaseStore;
}
