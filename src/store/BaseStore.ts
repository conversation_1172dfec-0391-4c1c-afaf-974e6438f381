import { Action, Mutation, VuexModule } from 'vuex-module-decorators';
import { cloneDeep } from 'lodash/fp';
import diff from '@/utils/diff';
import BaseModel, { IBaseModel, IModelConfig } from '../models/BaseModel';

interface IModelRequired {
  id?: number | string | null | undefined;
}
interface IAssociationParams {
  parentId: number | string;
  shouldAppend?: boolean; // 是否追加元素，而不是整页替换
  [key: string]: any;
}
interface IParams {
  shouldAppend?: boolean; // 是否追加元素，而不是整页替换
  [key: string]: any;
}

function diffAttributes(originAttributeObjects: any, currentAttributeObjects: any) {
  const originAttributeObjectIds = originAttributeObjects.map((o: any) => o.id);
  const currentAttributeObjectIds = currentAttributeObjects.map((o: any) => o.id);
  const newObjects = currentAttributeObjects.filter((o: any) => !originAttributeObjectIds.includes(o.id));
  const deleteObjects = originAttributeObjects
    .filter((o: any) => !currentAttributeObjectIds.includes(o.id))
    .map((o: any) => ({
      ...o,
      _destroy: o.id,
    }));
  return newObjects.concat(deleteObjects);
}

function getDiffResourceAttributes(record: IObject) {
  const attributesKeys = Object.keys(record).filter((key: string) => key.includes('_attributes'));
  return attributesKeys.reduce(
    (obj, key) => ({
      ...obj,
      [key]: diffAttributes(record[key.split('_attributes').shift() || ''] || [], record[key]),
    }),
    {},
  );
}

export default class BaseStore<IModel extends IModelRequired = IModelRequired> extends VuexModule {
  model: IBaseModel = new BaseModel({
    name: 'name',
    resource: 'resource',
  });
  // data
  perPage: number = 15;
  currentPage: number = 1;
  totalPages: number = 0;
  totalCount: number = 0;
  records: IModel[] = [];
  record: IModel | IObject = {};
  formData: IModel | IObject = {};
  loading: boolean = false;
  finish: boolean = false;

  // =============== actions ===============
  @Action({ rawError: true })
  async fetch(params?: IParams) {
    try {
      this.context.commit('SET_LOADING', true);
      this.context.commit('SET_PARAMS', params);
      const { data } = await this.model.index({
        per_page: this.perPage,
        page: this.currentPage,
        ...params,
      });
      if (params && params.shouldAppend === false) {
        this.context.commit('SET_RECORDS', data);
      } else {
        this.context.commit('APPEND_RECORDS', data);
      }
      this.context.commit('SET_LOADING', false);
      return { data };
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  @Action({ rawError: true })
  async find(id: number | string) {
    try {
      this.context.commit('SET_LOADING', true);
      const res = await this.model.find(id);
      this.context.commit('SET_RECORD', res.data);
      this.context.commit('SET_FORM_DATA', res.data);
      this.context.commit('SET_LOADING', false);
      return res;
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  @Action({ rawError: true })
  async create(formData: IModel) {
    try {
      this.context.commit('SET_LOADING', true);
      const { data } = await this.model.create(formData);
      this.context.commit('ADD_RECORD', data);
      this.context.commit('SET_LOADING', false);
      return { data };
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  @Action({ rawError: true })
  async update(formData: IModel) {
    try {
      this.context.commit('SET_LOADING', true);
      if (!formData.id) {
        throw new Error('Failed: id is empty.');
      }
      // Get origin record
      let originData: any = null;
      if (this.record.id === formData.id) {
        originData = this.record;
      } else {
        const existOne: any = this.records.find(o => o.id === formData.id);
        if (existOne) {
          originData = existOne;
        } else {
          const { data } = await this.model.find(formData.id);
          originData = data;
        }
      }
      // support resources_attributes
      const diffAttributesData = getDiffResourceAttributes(formData);
      // diff origin data and formData
      const patchData: IObject = diff(originData, formData);
      Object.assign(patchData, {
        id: formData.id,
        ...diffAttributesData,
      });
      await this.model.update(patchData);
      const { data } = await this.model.find(formData.id);
      this.context.commit('UPDATE_RECORD', data);
      this.context.commit('SET_LOADING', false);
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  @Action({ rawError: true })
  async delete(id: number | string) {
    try {
      this.context.commit('SET_LOADING', true);
      await this.model.delete(id);
      this.context.commit('DELETE_RECORD', id);
      this.context.commit('SET_LOADING', false);
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  /**
   * fetchByParent 只使用于标准路由
   */
  @Action({ rawError: true })
  async fetchByParent(params: IAssociationParams) {
    try {
      this.context.commit('SET_LOADING', true);
      this.context.commit('SET_PARAMS', params);
      const realParams = { ...params };
      delete realParams.parentId;
      const { data } = await this.model.indexByParent(params.parentId!, {
        per_page: this.perPage,
        ...realParams,
      });
      // 一对一
      if (data.id) {
        this.context.commit('SET_LOADING', false);
        this.context.commit('SET_RECORD', data);
        this.context.commit('SET_FORM_DATA', data);
      } else if (params.shouldAppend === false) {
        // 一对多
        this.context.commit('SET_RECORDS', data);
      } else {
        // 一对多
        this.context.commit('APPEND_RECORDS', data);
      }
      this.context.commit('SET_LOADING', false);
      return { data };
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  /**
   * createByParent 只使用于标准路由
   */
  @Action({ rawError: true })
  async createByParent(payload: IAssociationParams & IModel) {
    try {
      this.context.commit('SET_LOADING', true);
      const realPayload = { ...payload };
      delete realPayload.parentId;
      const { data } = await this.model.createByParent(payload.parentId!, realPayload);
      this.context.commit('ADD_RECORD', data);
      this.context.commit('SET_LOADING', false);
      return { data };
    } catch (error) {
      this.context.commit('SET_LOADING', false);
      throw error;
    }
  }

  @Action({ rawError: true })
  async reset() {
    try {
      const data = {
        current_page: 0,
        total_pages: 0,
        total_count: 0,
        [this.model.indexKey || this.model.resource]: [],
      };
      this.context.commit('SET_RECORDS', data);
    } catch (error) {
      throw error;
    }
  }

  // =============== mutations ===============
  @Mutation
  public SET_RECORD(this: any, payload: IModel | IObject) {
    this.record = cloneDeep(payload);
  }
  @Mutation
  public SET_FORM_DATA(this: any, payload: IModel | IObject) {
    this.formData = cloneDeep(payload);
  }
  @Mutation
  public SET_PARAMS(this: any, payload: IObject) {
    this.currentPage = payload.page || this.currentPage;
    this.perPage = payload.per_page || this.perPage;
  }
  @Mutation
  public SET_RECORDS(this: any, payload: IObject) {
    this.currentPage = payload.current_page || this.currentPage;
    this.totalPages = payload.total_pages || this.totalPages;
    this.totalCount = typeof payload.total_count === 'number' ? payload.total_count : this.totalCount;
    const records = cloneDeep(payload[this.model.indexKey || this.model.resource] || this.records);
    this.records = records.map((o: any, i: number) => ({ _index: i + 1, ...o }));
    this.finish = this.currentPage >= this.totalPages;
  }
  @Mutation
  public APPEND_RECORDS(this: any, payload: IObject) {
    this.currentPage = payload.current_page || this.currentPage;
    this.totalPages = payload.total_pages || this.totalPages;
    this.totalCount = typeof payload.total_count === 'number' ? payload.total_count : this.totalCount;
    this.finish = this.currentPage >= this.totalPages;
    if (this.currentPage === 1) {
      this.records = payload[this.model.indexKey || this.model.resource]
        ? cloneDeep(payload[this.model.indexKey || this.model.resource])
        : this.records;
    } else {
      this.records = this.records.concat(cloneDeep(payload[this.model.indexKey || this.model.resource] || []));
    }
  }
  @Mutation
  public ADD_RECORD(this: any, payload: IModel) {
    this.records.push(cloneDeep(payload));
  }
  @Mutation
  public UPDATE_RECORD(this: any, payload: IModel) {
    const index = this.records.findIndex((o: IModel) => o.id === payload.id);
    if (index >= 0) {
      Object.assign(this.records[index], payload);
    }
    if (payload.id === this.record.id) {
      Object.assign(this.record, payload);
    }
  }
  @Mutation
  public DELETE_RECORD(this: any, id: number) {
    const index = this.records.findIndex((o: IModel) => o.id === id);
    if (index >= 0) {
      this.records.splice(index, 1);
    }
    if (id === this.record.id) {
      this.record = {};
    }
  }
  @Mutation
  public SET_LOADING(this: any, payload: boolean) {
    this.loading = payload;
  }

  /**
   * 手动设置模型参数
   * @param config 模型参数
   */
  @Action({ rawError: true })
  setConfig(config: IModelConfig | IObject) {
    this.model.setConfig(config);
  }

  /**
   * 手动设置模型角色
   * @param role 角色
   */
  @Action({ rawError: true })
  setRole(role: string) {
    this.model.setRole(role);
  }
}

// decorator
export function InjectModel(model: IBaseModel) {
  return (constructor: any) => {
    constructor.state.model = model;
  };
}
