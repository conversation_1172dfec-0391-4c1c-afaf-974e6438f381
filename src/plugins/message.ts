import { Notify } from 'vant';

const message = (options: string | object) => {
  if (typeof options === 'string') {
    Notify({ message: options, type: 'primary' });
  } else {
    Notify(options);
  }
};

message.warning = (text: string) => {
  Notify({ message: text, type: 'warning' });
};
message.success = (text: string) => {
  Notify({ message: text, type: 'success' });
};
message.error = (text: string) => {
  Notify({ message: text, type: 'danger' });
};
message.primary = (text: string) => {
  Notify({ message: text, type: 'primary' });
};

export default message;
