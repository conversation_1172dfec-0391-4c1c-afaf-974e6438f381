import wx from 'weixin-js-sdk';
import Axios from 'axios';

const { location } = window;
const defaultOptions = {
  title: '上海电子课程教学平台', // 分享标题
  desc: '扫码签到', // 分享描述
  link: location.href.split('#')[0], // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
  imgUrl: 'http://wchattest.stiei.edu.cn/campus-mobile/logo.jpeg', // 分享图标
  type: 'link', // 分享类型,music、video或link，不填默认为link
  data_url: '', // 如果type是music或video，则要提供数据链接，默认为空
};

interface ISdk {
  wx: any;
  register: (callback?: any, options?: object, debug?: Boolean) => void;
  scanQRCode: (callback: (code: string) => void, errCallback?: (err: any) => void) => void;
  getLocation: () => Promise<{ latitude: number; longitude: number }>;
}

export default {
  wx,
  register(options?: object, debug?) {
    return new Promise((resolve, reject) => {
      Axios.post('http://soa-wechat.tallty.com/campus/wechat/js_hash', {
        page_url: location.href.split('#')[0],
      }).then(({ data }) => {
        (window as any).wx = wx;
        wx.config({
          debug,
          appId: process.env.VUE_APP_APPID,
          timestamp: data.timestamp,
          nonceStr: data.noncestr,
          signature: data.signature,
          jsApiList: ['scanQRCode', 'getLocation'],
        });
        const configOptions = {
          ...defaultOptions,
          ...options,
        };
        wx.ready(() => {
          resolve({ wx, options: configOptions });
        });
        if (process.env.NODE_ENV === 'development') {
          resolve({ wx, options: configOptions });
        }
      });
    });
  },
  scanQRCode(callback: (code: string) => void, errCallback?: (err: any) => void) {
    wx.scanQRCode({
      needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
      scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
      success: async (res: any) => {
        callback(res.resultStr);
      },
      fail: (err: any) => {
        if (errCallback) {
          errCallback(err);
        }
      },
    });
  },
  getHtml5Location() {
    return new Promise((resolve, reject) => {
      if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
          (position: Position) => {
            resolve(position.coords);
          },
          err => {
            reject(err);
          },
        );
      } else {
        reject(new Error('地理位置服务不可用'));
      }
    });
  },
  getLocation() {
    if (process.env.NODE_ENV === 'development') {
      return Promise.resolve({ latitude: '31.185187', longitude: '121.442563' });
      // return this.getHtml5Location();
    }
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject,
      });
    });
  },
} as ISdk;
