import * as fp from 'lodash/fp';

interface IObject {
  [key: string]: any;
}

export default function(oldObject: IObject, newObject: IObject) {
  return Object.keys(newObject).reduce((res: IObject, key: string) => {
    const oldValue = oldObject[key];
    const newValue = newObject[key];
    if (!fp.isEqual(oldValue, newValue)) {
      Object.assign(res, { [key]: newValue });
    }
    return res;
  }, {});
}
