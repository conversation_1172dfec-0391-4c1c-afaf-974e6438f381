import dayjs from 'dayjs';

export default {
  // global
  dateDiff(endDate: string): number {
    const startDate = new Date().toDateString();
    const dateSpan = Date.parse(endDate) - Date.parse(startDate);
    const iDays = Math.floor(Math.abs(dateSpan) / (24 * 3600 * 1000));
    return iDays;
  },
  formatDefaultDate(date: string, format: string = 'YYYY-MM-DD') {
    return date ? dayjs(dayjs(date), format) : null;
  },
  getRole(): string {
    const pathname = window.location.pathname || '';
    if (pathname.includes('/student/')) return 'student';
    if (pathname.includes('/teacher/')) return 'teacher';
    return 'admin';
  },
  fileVerifyMessage() {
    return '文件正在上传中， 请确定上传完成或取消上传再操作!';
  },
  // pt
  formatDirectory(str: string = '') {
    const newStr = str.replace(/<h1/g, 'separator<h1') || '';
    const strArr = newStr.split('separator') || [];
    return strArr.length
      ? ((strArr || []).slice(1) || []).map((string: string) => ({
          title: (string.match(/<h1.*?h1>/g) || [])[0] || '',
          children: (string.match(/<h2.*?h2>/g) || []).map((h2: string) => ({
            title: h2 || '',
            children: [],
          })),
        }))
      : [];
  },
  // hr
  formatUserInfo(val: any = {}) {
    return {
      user_name: val.name,
      user_code: val.code,
      user_department_name: val.department_name,
    };
  },
  formatState(state: string) {
    return ({
      pending: '待提交',
      verifying: '待审批',
      published: '已提交',
      scored: '已评分',
      verified: '已审批',
      rejected: '已退回',
      processing: '进行中',
      completed: '已完成',
    } as any)[state];
  },
  getDynamicType(val: string = ''): string {
    if (val.includes('date_')) return 'date';
    if (val.includes('time_')) return 'time';
    if (val.includes('datetime_')) return 'datetime';
    return 'text';
  },
  formatDegree(val: string = '') {
    return (
      ({
        bachelor: '学士学位',
        master: '硕士学位',
        doctor: '博士学位',
        postdoctor: '博士后学位',
        no: '其他',
      } as any)[val] || ''
    );
  },
  formatEducation(val: string = '') {
    return (
      ({
        high: '本科',
        other: '其他',
      } as any)[val] || ''
    );
  },
};
