import router from '@/router';
import sessionStore from '@/store/modules/session.store';
import { ITeacher } from '../models/teaching/teacher';

type func = (item: any) => string;

const REDIRECT_URL_KEY = 'CAMPUS_MOBILE_REDIRECT_PATH';

export type PermissionModule =
  | 'teaching'
  | 'ems'
  | 'studying'
  | 'hr'
  | 'res'
  | 'lm'
  | 'fund'
  | 'bpm'
  | 'meeting'
  | 'access'
  | 'permit'
  | 'finance'
  | 'ep'
  | 'pt'
  | 'forms'
  | 'exam'
  | 'wechat'
  | 'conference'
  | 'portal';

export default {
  /**
   * 权限验证
   * 1. hasPermission('meeting', 'admin') 【admin 权限才能显示】
   * 2. hasPermission('meeting', ['admin', 'edit']) 【admin 或 edit 权限能显示】
   * 3. hasPermission('meeting', ['admin', 'edit'], 'some') 【admin 或 edit 权限能显示，等价于 2】
   * 4. hasPermission('meeting', ['admin', 'edit'], 'every') 【admin 且 edit 权限能显示】
   * 5. hasPermission('meeting', ['admin', 'edit'], 'not') 【不是 admin 且不是 edit 权限能显示】
   */
  hasPermission(module: PermissionModule, powers: string | string[], mode: 'every' | 'some' | 'not' = 'some'): boolean {
    if (sessionStore.role === 'student') return false;
    const roles = (sessionStore.currentUser as ITeacher).roles_name || [];
    if (mode === 'not') {
      if (powers instanceof Array) {
        return !powers.some(o => roles.includes(`${module}_${o}`));
      }
      if (typeof powers === 'string' && !!powers) {
        return !roles.includes(`${module}_${powers}`);
      }
    } else {
      if (powers instanceof Array) {
        return mode === 'every'
          ? powers.every(o => roles.includes(`${module}_${o}`))
          : powers.some(o => roles.includes(`${module}_${o}`));
      }
      if (typeof powers === 'string' && !!powers) {
        return roles.includes(`${module}_${powers}`);
      }
    }
    return true;
  },
  only(obj: IObject, keys: string | string[]) {
    obj = obj || {};
    if (typeof keys === 'string') keys = keys.split(/ +/);
    return keys.reduce((ret: IObject, key: string) => {
      if (obj[key] == null) return ret;
      ret[key] = obj[key];
      return ret;
    }, {});
  },
  // resolve async function one by one
  promiseSerial(funcs: Promise<any>[]) {
    const concat = (res: any) => Array.prototype.concat.bind(res);
    const promiseConcat = (func: any) => (res: any) => func().then(concat(res));
    const promiseReduce = (acc: any, func: any) => acc.then(promiseConcat(func));
    funcs.reduce(promiseReduce, Promise.resolve([]));
  },
  groupBy(array: any[], func: any) {
    return array.map(typeof func === 'function' ? func : val => val[func]).reduce(
      (group: any, val: any, index: number) => ({
        ...group,
        [val]: (group[val] || []).concat(array[index]),
      }),
      {},
    );
  },
  objectify(ary: any[], key: string | func, valueKey?: string | number) {
    return ary.reduce((obj, item) => {
      const v = valueKey ? item[valueKey] : item;
      const k = typeof key === 'function' ? key(item) : item[key];
      Object.assign(obj, { [k]: v });
      return obj;
    }, {});
  },
  // 保存 redirect url, 防止覆盖，只有当不存在 redirect url 时，才能设置
  setRedirectPath(path?: string) {
    if (path) {
      window.localStorage.setItem(REDIRECT_URL_KEY, path);
      return path;
    }
    const reg = new RegExp(process.env.VUE_APP_PUBLIC_PATH || '/');
    const nowPath = (window.location.pathname + window.location.search).replace(reg, '/');
    window.localStorage.setItem(REDIRECT_URL_KEY, nowPath);
    return nowPath;
  },
  // 每次获取 redirect_path 后，移除该数据
  getRedirectPath(shouldRemove: boolean = true, defaultPath: string = '/') {
    const reg = new RegExp(process.env.VUE_APP_PUBLIC_PATH || defaultPath);
    const nowPath = window.location.pathname.replace(reg, defaultPath);
    const redirectPath = window.localStorage.getItem(REDIRECT_URL_KEY) || defaultPath;
    if (shouldRemove) {
      window.localStorage.removeItem(REDIRECT_URL_KEY);
    }
    if (redirectPath.includes(nowPath) || redirectPath.includes('login')) {
      return defaultPath;
    }
    return redirectPath;
  },
  weekDay(index: number) {
    return ['周日', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][index];
  },
  getVueRouteByPath(path?: string) {
    if (path) {
      return (router as any).match(path);
    }
    const publicPath: string = process.env.VUE_APP_PUBLIC_PATH as string;
    const realPath = window.location.pathname.substring(
      window.location.pathname.indexOf(publicPath) + publicPath.length,
    );
    return (router as any).match(realPath);
  },
  open(path: string, target: string = '_blank') {
    const publicPath = process.env.VUE_APP_PUBLIC_PATH || '';
    if (path.includes('http') || path.includes(publicPath)) {
      window.open(path, target);
      return;
    }
    const newPath = path.charAt(0) === '/' ? path.slice(1) : path;
    window.open(`${publicPath}${newPath}`, target);
  },
  parseStringToArray(string: string) {
    if (!string) return [];
    const pattern = /[,，;；\s、!@#$%^&*_\-+=《》<>?\\/[\]()（）'"‘’“”]/g;
    const formatString = string
      .replace(pattern, ' ')
      .trim()
      .replace(/\s{2,}/g, ' ');
    return formatString.split(' ');
  },
  stringSplice(string: string | number, start: number, delCount: number, newSubStr: string) {
    return String(string).slice(0, start) + (newSubStr || '') + String(string).slice(start + Math.abs(delCount));
  },
  toUsCurrency(price: number | string, decimalCount: number = 2, suffix: string = '') {
    const priceNumber = Number(price);
    if (Number.isNaN(priceNumber)) return null;
    const priceArray = priceNumber.toFixed(decimalCount).split('.');
    return `${Number(priceArray[0]).toLocaleString('en-US')}.${
      priceArray[1] ? priceArray[1].padEnd(decimalCount, '0') : suffix
    }`;
  },
  isAndroid() {
    const u = navigator.userAgent;
    return navigator.userAgent.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
  },
  isIos() {
    const u = navigator.userAgent;
    return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  },
  // 解决输入框键盘收回时，会造成页面底部空缺
  resumeDocumentHeight() {
    window.scrollTo(0, 0);
  },
  parseSeconds(seconds: number) {
    const secPerMinute = 60;
    const secPerHour = 60 * 60;
    const hours = Math.floor(seconds / secPerHour);
    const hourString = String(hours).padStart(2, '0');
    const minutesLeft = seconds - hours * secPerHour;
    const minutes = Math.floor(minutesLeft / secPerMinute);
    const minuteString = String(minutes).padStart(2, '0');
    const secondsCount = Math.round(minutesLeft - minutes * secPerMinute);
    const secondString = String(secondsCount).padStart(2, '0');
    return {
      hour: hourString,
      minute: minuteString,
      second: secondString,
      toString() {
        return `${hourString}:${minuteString}:${secondString}`;
      },
    };
  },
  // 使用搜狐api获取本机IP, 导入方式问题script(index.html）
  getIp(): { cip: string; cid: string; cname: string } {
    return (window as any).returnCitySN || {};
  },
};
