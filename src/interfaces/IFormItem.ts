export interface IFormItemLayout {
  component: string;
  type?: string; // html native type
  placeholder?: string;
  span?: number;
  required?: boolean;
  config?: object;
  options?: Array<{ label: string }>;
  disabled?: boolean;
  multiple?: boolean;
  accept?: string;
  wechat?: {
    name: string;
    desc: string;
    appid: string;
  };
  attra?: IObject[];
  notEditable?: boolean;
  fields?: IFormItem[];
  templateIndexAry?: number[];
  conditionKey?: string;
  conditionValue?: string;
}

export interface IFormItemModel {
  attr_type: string;
}

export interface IFormItem {
  key: string;
  name: string;
  layout: IFormItemLayout;
  model: IFormItemModel;
  transitionAccessibility?: 'read_and_write' | 'readonly' | 'hidden';
}

export function getRuleType(item: IFormItem) {
  const specialTypeMap: IObject = {
    text: 'string',
    json: 'object',
  };
  const attr_type = item.model && item.model.attr_type;
  let type = specialTypeMap[attr_type || ''] || attr_type;
  if (['wechat_articles'].includes(item.layout.component)) {
    type = 'object';
  } else if (['checkbox', 'file', 'contacts', 'record', 'table'].includes(item.layout.component)) {
    type = 'array';
  } else if (['date', 'datetime', 'time'].includes(item.layout.component)) {
    type = 'string';
  }
  return type;
}
