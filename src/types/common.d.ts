declare interface IObject {
  [key: string]: any;
}

declare module '*.png' {
  const value: string;
  export default value;
}

declare module '*.svg' {
  const value: string;
  export default value;
}

declare module '*.json' {
  const value: { [key: string]: any } | any[];
  export default value;
}

declare module 'weixin-js-sdk';
declare module 'vue-infinite-scroll';
declare module 'file-saver';
declare module 'vue-pdf';
