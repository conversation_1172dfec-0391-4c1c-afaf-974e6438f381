// 1. Make sure to import 'vue' before declaring augmented types
import Vue from 'vue';
import dayjs from 'dayjs';
import { RouteConfig } from '@/interfaces/IRoute';
import { PermissionModule } from '../utils/index';

type func = (item: any) => string;

// 2. Specify a file with the types you want to augment
//    Vue has the constructor type in types/vue.d.ts
declare module 'vue/types/vue' {
  // 3. Declare augmentation for Vue
  interface Vue {
    $dayjs: typeof dayjs;
    $message: {
      success(arg: string): void;
      primary(arg: string): void;
      warning(arg: string): void;
      error(arg: string): void;
    };
    $utils: {
      hasPermission: (module: PermissionModule, powers: string | string[], mode?: 'every' | 'some' | 'not') => boolean;
      only(obj: IObject, keys: string | string[]): IObject;
      groupBy(array: any[], func: any): any[];
      objectify(array: any[], key: string | func, valueKey?: string | number): object;
      setRedirectPath(url?: string): void;
      getRedirectPath(removeCache?: boolean, defaultPath?: string): string;
      weekDay(index: number): string;
      getVueRouteByPath(url?: string): RouteConfig;
      stringSplice(string: string | number, start: number, delCount: number, newSubStr: string): string;
      promiseSerial(funcs: Promise<any>[]): void;
      toUsCurrency(price: number | string, decimalCount?: number, suffix?: string): string;
      resumeDocumentHeight(): void;
      addKeyboardToggleHandler(options: { onOpen?: () => void; onHide?: () => void }): () => void;
      parseSeconds: (seconds: number) => { hour: string; minute: string; second: string; toString: () => string };
      getIp: () => { cip: string; cid: string; cname: string };
      open: (path: string, target?: string) => void;
    };
    $tools: {
      dateDiff(endDate: string): number;
      formatDefaultDate(date: string, format: string): any;
      getRole(): string;
      formatDirectory(str: string): any[];
      fileVerifyMessage(): string;
      formatDegree(val: any): string;
      formatEducation(val: any): string;
    };
  }
}
