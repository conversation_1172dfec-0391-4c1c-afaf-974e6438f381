interface IAssessmentActivity {
  id: number;
  attachments?: { documents: import('../models/file').IFile[] };
  content: string;
  created_at: string;
  deleted_at: string;
  end_at: string;
  entry_count: number;
  group_count: number;
  meta: IObject;
  name: string;
  records: IObject[];
  school_id: number;
  stage: string;
  stages: { stages: IAssessmentActivityStagesItem[] };
  start_at: string;
  state: string;
  updated_at: string;
  entried: boolean;
  scored: boolean;
}

interface IAssessmentActivityStagesItem {
  end_at: string;
  key: string;
  name: string;
  start_at: string;
  state?: string;
}

interface ICommScoreTemplate {
  id: number;
  name: string;
  form: ICommScoreTemplateForm;
}

interface ICommScoreTemplateForm {
  catalogs: ICommScoreTemplateFormCatalog[];
}

interface ICommScoreTemplateFormCatalog {
  id: string;
  name: string;
  weight: number;
  items: ICommScoreTemplateFormCatalogItem[];
}

interface ICommScoreTemplateFormCatalogItem {
  id: string;
  name: string;
  max_score: number;
}

interface IAssessmentCatalog {
  activity_id: number;
  confirm_workflow_id: number;
  created_at: string;
  id: number;
  name: string;
  score_template_id: string;
  score_template_name: string;
  submit_workflow_id: string;
  updated_at: string;
  scored: boolean;
  entried: boolean;
}

interface IAssessmentScore {
  id: number;
  activity_id: number;
  entry_id: number;
  user_type: string;
  user_id: number;
  dimension_id: number;
  score: number;
  state: string;
  score_template_id: number;
  score_template: ICommScoreTemplate;
  catalog_payload: IObject;
  item_payload: IObject;
  entry_user_name: string;
  entry_user_code: string;
  entry_user_department_name: string;
  catalog_name: string;
  group_name: string;
}

interface IAssessmentEntry {
  activity_id: number;
  catalog_name: string;
  created_at: string;
  deleted_at: string;
  dimension_stat: IObject[];
  id: number;
  is_prepare: boolean;
  score: number;
  score_stat: IObject;
  sub_group_id: number;
  updated_at: string;
  user_code: string;
  user_department_name: string;
  user_id: number;
  user_name: string;
  user_type: string;
  catalog: IAssessmentCatalog;
  submit_instance_id: number;
  confirm_instance_id: number;
}
