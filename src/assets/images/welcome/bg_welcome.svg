<?xml version="1.0" encoding="UTF-8"?>
<svg width="375px" height="375px" viewBox="0 0 375 375" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 53.2 (72643) - https://sketchapp.com -->
    <title>bg@1x</title>
    <desc>Created with <PERSON>ketch.</desc>
    <defs>
        <path d="M214.175051,544.60453 C174.079923,548.54802 157.941777,529.126611 142.266125,491.257936 C126.590473,453.38926 72.4067036,444.266582 59.9110814,404.723754 C47.4154593,365.180926 73.0883125,365.180926 64.398511,323.489031 C55.7087095,281.797136 5.10949833e-14,229.526933 3.99993452e-14,192.326922 C3.22809213e-14,167.526914 2.84217094e-14,122.884013 2.84217094e-14,58.3982168 L421.695457,53.1809056 C404.871365,169.204449 387.074251,239.251206 368.304113,263.321176 C340.148906,299.42613 331.256544,295.32964 323.583428,323.489031 C315.910313,351.648422 331.173758,374.367165 321.405912,400.206585 C311.638066,426.046004 275.760524,441.462716 273.682651,467.355694 C271.604778,493.248672 254.270179,540.66104 214.175051,544.60453 Z" id="path-1"></path>
        <filter x="-4.0%" y="-3.5%" width="108.1%" height="106.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="16.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.239215686   0 0 0 0 0.658823529   0 0 0 0 0.960784314  0 0 0 0.1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M207.221433,513.594503 C177.911638,513.594503 165.514977,511.273418 137.788325,452.040624 C110.061673,392.807829 53.7719439,375.420254 46.2872578,329.400099 C38.8025717,283.379944 67.7562999,251.720417 67.7562999,196.188024 C67.7562999,159.166429 60.5999525,108.984121 46.2872578,45.6410997 L414.371803,39.61117 C416.105368,172.162573 404.250022,252.985839 378.805764,282.080969 C340.639377,325.723663 284.245216,339.898627 269.57063,372.531187 C254.896043,405.163748 273.941786,427.428596 262.960411,458.040794 C251.979037,488.652993 236.531227,513.594503 207.221433,513.594503 Z" id="path-3"></path>
        <filter x="-4.6%" y="-3.6%" width="109.2%" height="107.2%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="16.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.239215686   0 0 0 0 0.658823529   0 0 0 0 0.960784314  0 0 0 0.1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M176.105815,500.360203 C176.105815,447.941783 131.355028,446.278723 131.355028,387.544946 C131.355028,328.811169 142.716504,347.89247 142.716504,305.74659 C142.716504,263.60071 102.705794,250.239292 102.705794,196.003606 C102.705794,141.76792 107.977291,75.5157631 114.344842,36.3403515 C120.712393,-2.83506012 243.255672,-20.5856484 260.803826,36.3403515 C278.351981,93.2663514 315.72457,168.867 303.606295,220.16494 C291.48802,271.46288 236.905962,318.088441 233.786242,345.007619 C230.666522,371.926796 256.333635,409.753085 241.935474,465.481017 C232.336699,502.632972 210.39348,524.392725 176.105815,530.760276 C176.105815,545.439173 176.105815,535.305815 176.105815,500.360203 Z" id="path-5"></path>
        <filter x="-8.4%" y="-3.2%" width="116.7%" height="106.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="16.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.239215686   0 0 0 0 0.658823529   0 0 0 0 0.960784314  0 0 0 0.1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-74.6%" y="-91.4%" width="249.1%" height="282.8%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="10" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="17" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.120956405   0 0 0 0 0.609917491   0 0 0 0 0.961786685  0 0 0 0.258413462 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="83.6769666%" y1="31.0830431%" x2="28.1931869%" y2="70.8606002%" id="linearGradient-8">
            <stop stop-color="#2BABFA" offset="0%"></stop>
            <stop stop-color="#1F9EFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="91.5309991%" y1="34.4512921%" x2="19.4577259%" y2="69.597206%" id="linearGradient-9">
            <stop stop-color="#0A99FF" offset="0%"></stop>
            <stop stop-color="#0891FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="58.82%" y1="0%" x2="49.3210181%" y2="91.0704415%" id="linearGradient-10">
            <stop stop-color="#0A99FF" offset="0%"></stop>
            <stop stop-color="#0891FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="18.6631944%" x2="46.1508963%" y2="75.7403288%" id="linearGradient-11">
            <stop stop-color="#0A99FF" offset="0%"></stop>
            <stop stop-color="#0891FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="81.3368056%" y1="0%" x2="47.5876277%" y2="91.0704415%" id="linearGradient-12">
            <stop stop-color="#0A99FF" offset="0%"></stop>
            <stop stop-color="#0891FA" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1-3-报道流程1-完成进度0">
            <g id="bg" transform="translate(-5.000000, -171.000000)">
                <g id="Group-7">
                    <g id="Path-3">
                        <use fill-opacity="0.2" fill="#61BDFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    </g>
                    <g id="Path-2">
                        <use fill-opacity="0.3" fill="#0A98FF" fill-rule="evenodd" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    </g>
                    <g id="Path">
                        <use fill-opacity="0.6" fill="#0A99FF" fill-rule="evenodd" xlink:href="#path-5"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    </g>
                </g>
                <g id="Group-32" filter="url(#filter-7)" transform="translate(66.000000, 318.000000)">
                    <path d="M37.2309673,5.04864877 C37.6638156,5.20530411 37.855685,5.6921407 37.6478265,6.15608152 C37.4502467,6.60315176 36.9340266,6.84536502 36.5011783,6.68870968 C36.06833,6.53205434 35.8810289,6.03557741 36.0786087,5.58971221 C36.2864673,5.12456635 36.798119,4.89199343 37.2309673,5.04864877 Z M40.3606088,6.1789773 C40.7934571,6.33563264 40.9853265,6.82246924 40.777468,7.2876151 C40.5798882,7.73227526 40.0636681,7.97569355 39.6308198,7.81903821 C39.1968294,7.66238287 39.019807,7.16831603 39.2173869,6.72365587 C39.4252454,6.25851001 39.9277606,6.02232196 40.3606088,6.1789773 Z M43.4990443,7.31340298 C43.9318926,7.47005832 44.123762,7.95689491 43.9159035,8.42083573 C43.7183237,8.86670093 43.2021036,9.10891419 42.7692553,8.95225885 C42.3352649,8.79560351 42.1491059,8.29912658 42.3466857,7.85446642 C42.5545443,7.38932056 43.0661961,7.15674764 43.4990443,7.31340298 Z" id="Combined-Shape" fill="#29A6FF"></path>
                    <path d="M40.5754373,6.64082829 C40.8018397,6.16291342 40.5928529,5.66271235 40.1213885,5.50175656 C39.6499241,5.34080078 39.1013336,5.58347258 38.8761752,6.06138746 C38.6609685,6.51825427 38.8537837,7.02588406 39.3264921,7.18683985 C39.7979565,7.34779564 40.3602306,7.09769511 40.5754373,6.64082829 Z M43.993865,7.80466245 C44.2190235,7.3279857 44.0112806,6.82778463 43.5398162,6.66682884 C43.0683518,6.50587306 42.5110536,6.74606862 42.2846511,7.22274537 C42.0694445,7.67961219 42.2722115,8.19095634 42.7436758,8.35067401 C43.2163842,8.5116298 43.7786583,8.26276739 43.993865,7.80466245 Z M37.1669613,5.47823226 C37.3921198,5.00031738 37.1831329,4.50135444 36.7116686,4.34039865 C36.2414481,4.17944286 35.6829059,4.4184003 35.4577475,4.89507706 C35.2425408,5.35442011 35.4465517,5.86452615 35.9180161,6.02548194 C36.3894805,6.1851996 36.9517546,5.93633719 37.1669613,5.47823226 Z M113.225734,32.7998582 C112.869959,33.552636 112.525379,34.2645559 112.188264,35 C89.3975367,27.5512808 72.3927206,21.9190969 61.1738153,18.1034483 C53.4619954,15.4805903 42.0707236,11.4550142 27,6.02672006 C27.5349068,4.91983949 28.0399584,3.87115062 28.4852994,2.92646396 C29.555113,0.895944788 31.8738718,-0.277794342 34.0396224,0.0564984483 L111.195576,26.2761963 C113.631268,27.1057377 114.683666,29.7367457 113.534238,32.1486063 L113.225734,32.7998582 Z" id="Fill-8" fill="#3DAEFF"></path>
                    <path d="M90.1824763,34.5928819 L88.9664841,37.1587889 L61.9581959,27.9914692 L63.1741881,25.4255622 L90.1824763,34.5928819 Z M98.1698688,43.2715755 C98.8904568,43.5158885 99.20071,44.2897532 98.8641854,45.0028496 L95.2049495,52.7278543 C94.872178,53.4297892 94.0127267,53.8179617 93.2921387,53.5736486 L57.1651599,41.3108704 C56.4433208,41.0665573 56.1380718,40.2827713 56.4708433,39.5795962 L60.1300792,31.8545916 C60.4666038,31.1427353 61.322302,30.7644842 62.04289,31.0087972 L98.1698688,43.2715755 Z M92.0761465,56.1383154 C92.7979855,56.3826285 93.1082387,57.1564931 92.770463,57.8695896 L87.2784817,69.4663978 C86.9407061,70.178254 86.0850078,70.5565052 85.3644199,70.3121922 L49.238692,58.0494139 C48.516853,57.8051008 48.2065999,57.0312362 48.5431245,56.3193799 L54.0363568,44.7213316 C54.3728814,44.0094753 55.2298307,43.6312241 55.9504186,43.8755372 L92.0761465,56.1383154 Z M83.7148238,73.7949551 C84.4354118,74.0392682 84.8294834,74.637029 84.5980445,75.1244149 L82.0759866,80.4509352 C81.8457987,80.9383211 81.0739189,81.1404684 80.3520799,80.8961554 L12.9508319,58.0175416 C12.2277419,57.7732285 11.8349213,57.1754677 12.0663602,56.6868416 L14.5884181,51.3615615 C14.819857,50.8741756 15.5917368,50.6707881 16.3123248,50.9163413 L83.7148238,73.7949551 Z M56.133443,27.5096641 C56.4712186,26.7965676 56.1609655,26.022703 55.4491346,25.7821104 L34.5946178,18.7032332 C33.8740298,18.4589201 33.0195826,18.8359311 32.6818069,19.5490275 L21.8992586,42.3160263 L22.1682281,42.3048648 L29.6705999,42.7339628 C30.3073695,42.7686875 30.941637,42.3966372 31.2156106,41.8211994 L31.6772372,40.8464276 C31.8711455,40.4346919 31.8486271,39.9981528 31.6484638,39.6471854 L30.6313838,37.9196318 C30.4199613,37.566184 30.4137062,37.1222039 30.6026104,36.7216298 L33.0195826,31.6195798 C33.3561072,30.9077236 33.045854,30.1350991 32.325266,29.890786 C31.4745719,29.6005868 31.2268698,28.5997715 31.8186026,27.8606315 C34.430734,24.5692264 38.8981293,23.1529549 42.6624509,24.4303276 L45.1019415,25.2587597 C47.9855445,26.237252 49.2278081,29.3314704 47.8792077,32.1788955 L43.6332431,41.145308 C43.444339,41.544642 43.0765388,41.8608847 42.6336775,41.9935827 L40.4894278,42.6483912 C40.0490685,42.7810892 39.6812684,43.0973319 39.4911132,43.4979061 L38.5440904,45.4970565 L38.7217354,45.5578247 C38.8405824,45.5987502 38.9506722,45.6570381 39.0545069,45.7277276 L45.1657436,50.1117205 L45.3508947,50.2766628 L56.133443,27.5096641 Z M59.262246,24.0992029 C59.982834,24.3435159 60.2930872,25.1161404 59.9615667,25.8180753 L45.9363724,55.4332801 C45.5985968,56.1463765 44.7428985,56.5233875 44.0223106,56.2790745 L17.9610451,47.4341984 C17.2404571,47.1886452 16.930204,46.4160207 17.2679796,45.7029243 C22.2309517,35.1374031 25.9581511,27.2186996 28.4495778,21.946814 C29.0869511,20.5981254 30.0529517,18.5859674 31.3475796,15.9103402 C31.6791001,15.2071651 32.5360494,14.8301541 33.2566374,15.0744671 L59.262246,24.0992029 Z" id="Combined-Shape" fill="url(#linearGradient-8)"></path>
                    <path d="M62.7764645,25.6569506 L61.5697132,28.2163269 L88.3727526,37.3659735 L89.5795039,34.8053596 L62.7764645,25.6569506 Z M59.7546201,32.0727179 L56.124434,39.7817871 C55.7941914,40.482274 56.0971207,41.2644432 56.8122326,41.5094899 L92.6646655,53.7457344 C93.3797774,53.990781 94.2339388,53.6034093 94.5641815,52.9029223 L98.1943676,45.1926156 C98.5295763,44.4822277 98.2216809,43.7099595 97.506569,43.4661504 L61.6541361,31.2286683 C60.9390242,30.9848593 60.0898288,31.3610925 59.7546201,32.0727179 Z M14.5597966,51.5378391 L12.0556634,56.8533716 C11.8272249,57.3409898 12.2170602,57.9375175 12.9346551,58.1813266 L79.8224477,81.0127454 C80.5400426,81.2565545 81.3060566,81.0548241 81.5344951,80.5684436 L84.0373867,75.2529111 C84.2670668,74.7665306 83.87599,74.1700028 83.1608781,73.9261938 L16.2706025,51.0947749 C15.5554906,50.8497283 14.7894766,51.0526962 14.5597966,51.5378391 Z M53.7072068,44.9129158 L48.2569617,56.4858016 C47.921753,57.1961895 48.2296484,57.9684577 48.9460018,58.2135044 L84.7971931,70.4497489 C85.512305,70.6947955 86.3615004,70.3173247 86.6967091,69.6069369 L92.1469543,58.0328134 C92.482163,57.3224256 92.1755091,56.5501573 91.4591557,56.3063483 L55.6079643,44.0688662 C54.8916109,43.8250571 54.0424155,44.2012904 53.7072068,44.9129158 Z M31.1377291,16.3389892 L17.2191189,45.8931025 C16.8839102,46.6034903 17.1918056,47.374521 17.9069175,47.6195677 L43.7688896,56.4461981 C44.485243,56.6900072 45.3344384,56.3137739 45.6696471,55.6021485 L59.5882573,26.0480352 C59.9172584,25.3475483 59.609363,24.5765176 58.8942511,24.3327085 L33.032279,15.5048405 C32.3171671,15.2610314 31.4667303,15.6372647 31.1377291,16.3389892 Z M0.16692815,62.986964 L27.00473,6 L112,35.013278 L86.4968254,89.1438394 C84.9585899,92.4098907 83.5805097,93.2428018 82.701518,92.9433003 C82.4879776,92.8702814 82.3042336,92.7304315 82.1540104,92.5423149 L80.2718757,90.1599167 C80.0930977,89.9359094 79.8547271,89.772545 79.5828356,89.6784867 C79.3097025,89.5856659 79.0042902,89.563389 78.6926702,89.6215566 L75.0413784,90.2960538 C74.731,90.3529838 74.4231046,90.3307068 74.1474885,90.2366485 C73.873114,90.1425902 73.6322603,89.9792257 73.4547239,89.7539808 L71.3702224,87.1215856 C71.1914444,86.8963406 70.9530738,86.7342138 70.6811823,86.6401555 C70.4080492,86.5473348 70.1026369,86.5262954 69.7910169,86.5832255 L66.1397251,87.256485 C65.8281052,87.3146526 65.5214513,87.2923757 65.2470768,87.1983173 C64.9751853,87.104259 64.7355731,86.9421322 64.5617612,86.7181249 L60.2437765,83.3233622 C60.063757,83.0981173 59.8229034,82.9347528 59.5485288,82.8406945 C59.2753958,82.7478738 58.9675004,82.7231216 58.6558805,82.7812893 L55.0132793,83.4594992 C54.7016593,83.5164293 54.3937639,83.4929147 54.1193894,83.400094 C53.8450149,83.3060357 53.6029197,83.1414336 53.4253832,82.9174263 L51.3421232,80.2862687 C51.1633452,80.0610237 50.9212501,79.8964217 50.6468755,79.8023634 C50.372501,79.7095427 50.0658471,79.6847905 49.7542272,79.7429581 L46.1016939,80.4174553 C45.79504,80.4768605 45.4896276,80.4545835 45.2152531,80.3605252 C44.9433616,80.2677045 44.7012664,80.1031025 44.5237299,79.8790952 L42.4292963,77.2442247 C42.2517598,77.0189798 42.0121477,76.8556153 41.7414977,76.7627946 C41.4683647,76.6699739 41.1641938,76.6464593 40.8525739,76.704627 L37.2000406,77.3791241 C36.8884206,77.4372917 36.5805252,77.4137772 36.3073922,77.3197188 C36.0342592,77.2268981 35.7958886,77.0635337 35.6220766,76.8395264 L33.527643,74.2058935 C33.3501066,73.9806486 33.1092529,73.8160465 32.8348784,73.7219882 C32.5592623,73.6291675 32.2513669,73.6056529 31.939747,73.662583 L28.2884552,74.3370801 C27.9768352,74.3952478 27.6726644,74.3729708 27.3995313,74.2801501 C27.1276398,74.1873294 26.8892692,74.0239649 26.7104912,73.79872 L24.6172991,71.1638495 C24.4385212,70.9386046 24.1976675,70.7740026 23.923293,70.6811818 C23.6501599,70.5883611 23.3447476,70.5648466 23.0380937,70.6242518 L19.3768698,71.2962737 C19.0702159,71.355679 18.7648035,71.3321644 18.4916705,71.2393437 C18.2185375,71.146523 17.9776838,70.9819209 17.7976643,70.756676 L15.7057137,68.1218055 C15.5269358,67.8965606 15.2860821,67.7331962 15.0117076,67.6391379 C14.7398161,67.5463171 14.4344037,67.5240402 14.1265083,67.5834454 L10.4603183,68.2641306 C10.1511814,68.3173478 9.84701054,68.291358 9.57511903,68.1985373 C9.30322753,68.1057166 9.06361539,67.9448274 8.88235438,67.7245329 L6.78419622,65.0772863 C6.60541824,64.8520414 6.36456458,64.6874394 6.09143157,64.5946186 C5.81829855,64.5017979 5.51288618,64.4782834 5.20623229,64.5376886 L1.89263221,65.1465925 C1.56859713,65.2059977 1.26815081,65.1812455 1.0049499,65.0909 C0.183067828,64.8112003 -0.263877107,63.9015574 0.16692815,62.986964 Z M29.5271134,42.9290279 L22.0817557,42.500815 L21.8148302,42.5119535 L32.5154369,19.7919195 C32.8506456,19.0802941 33.6985995,18.7040608 34.4149529,18.9478699 L55.1097449,26.0121445 C55.8161662,26.2522408 56.1240616,27.0245091 55.7888529,27.7361345 L45.0882462,50.4561685 L44.9045022,50.2915664 L38.8397077,45.9166171 C38.7366621,45.8460733 38.6274089,45.7879057 38.5094651,45.7470646 L38.3331701,45.6876593 L39.2742375,43.6913953 C39.4617061,43.2916475 39.8267111,42.9760571 40.2637239,42.8436328 L42.3916784,42.1901751 C42.8311743,42.0577509 43.1961793,41.7421605 43.3836479,41.3436502 L47.5973454,32.395734 C48.9356972,29.5541827 47.7041156,26.4663472 44.841185,25.4898734 L42.4202333,24.6631503 C38.6845185,23.3884125 34.2523146,24.8030001 31.6587924,28.086378 C31.0715564,28.8252308 31.3173762,29.822744 32.1616055,30.1123446 C32.8767174,30.3561537 33.1846128,31.1271843 32.8506456,31.8375722 L30.4520411,36.9290974 C30.2645725,37.3288452 30.2720216,37.7719094 30.4818374,38.1246281 L31.4899466,39.848618 C31.6885888,40.1988615 31.710936,40.6345 31.5185014,41.0453863 L31.0603828,42.0181474 C30.7897328,42.5923981 30.1590439,42.963681 29.5271134,42.9290279 Z" id="Shape" fill="url(#linearGradient-9)"></path>
                </g>
                <g id="Group-6" transform="translate(258.000000, 234.000000)">
                    <path d="M19,14 L32,14 C34.209139,14 36,15.790861 36,18 L36,53.5 C36,59.2989899 31.2989899,64 25.5,64 L25.5,64 C19.7010101,64 15,59.2989899 15,53.5 L15,18 C15,15.790861 16.790861,14 19,14 Z" id="Rectangle" fill="url(#linearGradient-10)" opacity="0.5" transform="translate(25.500000, 39.000000) rotate(90.000000) translate(-25.500000, -39.000000) "></path>
                    <path d="M43.4570306,14.3883095 L49.3659,24.7265407 C50.6712514,27.0103997 49.8965979,29.9307589 47.6356629,31.2493455 L17.9557232,48.5588091 C12.8686197,51.5256289 6.36376564,49.7649862 3.42672504,44.6263035 C0.489684443,39.4876208 2.23265484,32.9168128 7.31975838,29.9499929 L36.9996981,12.6405293 C39.260633,11.3219427 42.1516792,12.1044506 43.4570306,14.3883095 Z" id="Rectangle" fill="url(#linearGradient-11)" opacity="0.5"></path>
                    <path d="M26.2734593,2.63410002 L36.6116905,8.54296939 C38.8955494,9.84832077 39.6780573,12.739367 38.3594707,15.0003019 L21.0500071,44.6802416 C18.0831872,49.7673452 11.5123792,51.5103156 6.37369646,48.573275 C1.23501377,45.6362344 -0.525628946,39.1313803 2.44119089,34.0442768 L19.7506545,4.36433706 C21.0692411,2.10340215 23.9896003,1.32874864 26.2734593,2.63410002 Z" id="Rectangle" fill="url(#linearGradient-12)" opacity="0.5"></path>
                    <path d="M5,0 L18,0 C20.209139,-4.05812251e-16 22,1.790861 22,4 L22,39.5 C22,45.2989899 17.2989899,50 11.5,50 L11.5,50 C5.70101013,50 1,45.2989899 1,39.5 L1,4 C1,1.790861 2.790861,4.05812251e-16 5,0 Z" id="Rectangle" fill="url(#linearGradient-10)"></path>
                    <circle id="Oval" fill="#3DAEFF" cx="11.5" cy="40.5" r="2.5"></circle>
                </g>
                <path d="M115,274 C119.755063,279.426675 122.755063,284.426675 124,289" id="Path-4" stroke="#2597FA" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M248.916375,359.886927 C257.719841,364.969611 261.669078,370.378838 260.764085,376.114608" id="Path-4-Copy" stroke="#2597FA" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" transform="translate(254.903500, 368.000768) rotate(-120.000000) translate(-254.903500, -368.000768) "></path>
                <circle id="Oval" stroke="#2597FA" stroke-width="3" cx="276" cy="306" r="3.5"></circle>
            </g>
        </g>
    </g>
</svg>