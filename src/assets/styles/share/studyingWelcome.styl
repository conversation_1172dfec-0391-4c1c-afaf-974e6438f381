.welcome-container
  position fixed
  top 0
  right 0
  bottom 0
  left 0
  display flex
  flex-direction column
  height 100vh
  background linear-gradient(
    180deg,
    rgba(32, 159, 250, 100) 260px,
    rgba(32, 159, 250, 0) 420px
  )
  .welcome-base
    flex-shrink 0
    color #fff
  .welcome-content
    position relative
    z-index 10
    flex-grow 1
    overflow auto
    margin 0px 8px
    padding 20px
    border-radius 20px 20px 0px 0px
    background rgba(255, 255, 255, 1)
    box-shadow 0px 0px 4px 0px rgba(0, 0, 0, 0.1)
    -webkit-overflow-scrolling touch

.welcome-image-container
  &:before
    position absolute
    top 0
    left 0
    z-index 0
    width 100%
    height 420px
    background-image url('~@/assets/images/welcome/bg_welcome.svg')
    background-position bottom 50px right 0
    background-size cover
    background-repeat no-repeat
    content ''
