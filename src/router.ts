import Vue from 'vue';
import Router, { Route } from 'vue-router';

import auth from '@/views/auth/route';
import home from '@/views/home/<USER>';
// ===== student
import studentPtPractice from '@/views/student/pt/practice/route';
import studyingWelcome from '@/views/student/welcome/route';
import teachingStudentLessons from '@/views/student/teaching/lessons/route';
import teachingStudentCourseActivities from '@/views/student/teaching/courseActivities/route';
// ===== teacher
import meetingHome from '@/views/teacher/meeting/home/<USER>';
import meetingActivities from '@/views/teacher/meeting/activities/route';
import meetingAttendances from '@/views/teacher/meeting/attendances/route';
import teachingTeacherLessons from '@/views/teacher/teaching/lessons/route';
import teachingTeacherCourseActivities from '@/views/teacher/teaching/courseActivities/route';
import assessTeacherEntries from '@/views/teacher/assess/entries/route';
import assessTeacherActivities from '@/views/teacher/assess/activities/route';
import assessTeacherScores from '@/views/teacher/assess/scores/route';
import financeTeacherHome from '@/views/teacher/finance/home/<USER>';
import financeTeacherInstances from '@/views/teacher/finance/instances/route';
import ptActivity from '@/views/teacher/pt/activity/route';
import hrContact from '@/views/teacher/hr/contact/route';
import hrModification from '@/views/teacher/hr/modification/route';
import ConferenceTeacherActivity from '@/views/teacher/conference/activities/route';
import PortalTeacherActivity from '@/views/teacher/portal/route';
// ===== global
import bpmInstances from '@/views/bpm/instances/route';
import bpmWorkflows from '@/views/bpm/workflows/route';
import epRegister from '@/views/ep/register/route';
import epActivity from '@/views/ep/activity/route';

// 人事管理

import hr from '@/views/teacher/hr/route';

import errors from '@/views/errors/route';
import { RouteConfig } from './interfaces/IRoute';
import utils from './utils';

Vue.use(Router);

const requireNewRoute = require.context('./router', true, /\.route\.ts$/);
const RoutesNew: RouteConfig[] = [];

requireNewRoute.keys().forEach(fileName => {
  const moduleRoutes = requireNewRoute(fileName).default;
  if (Array.isArray(moduleRoutes)) {
    RoutesNew.push(...moduleRoutes);
  }
});

const router = new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: [
    ...home,
    // * 登录  auth
    ...auth,
    // * 人事  hr          主要是老师的人事信息
    ...hrContact,
    ...hrModification,
    ...hr,
    // 人事考核
    ...assessTeacherEntries,
    ...assessTeacherActivities,
    ...assessTeacherScores,
    // *顶岗实习
    ...studentPtPractice,
    ...epActivity,
    // * 学工  studying    主要是学生的信息和相关学生活动的系统，迎新系统属于学工的一种
    // * 迎新  welcome
    ...studyingWelcome,
    ...teachingStudentLessons,
    ...teachingStudentCourseActivities,
    // * 教务  teaching    主要是选课和教师安排
    ...teachingTeacherLessons,
    ...teachingTeacherCourseActivities,
    // * 权限  permit      权限设置模块
    // * 后勤  lm          包含：宿舍
    // * 会议  meeting
    ...meetingHome,
    ...meetingActivities,
    ...meetingAttendances,
    // * 资金卡
    ...financeTeacherHome,
    ...financeTeacherInstances,
    // * bpm
    ...bpmInstances,
    ...bpmWorkflows,
    // 疫情统计
    ...epRegister,
    // *顶岗实习
    ...ptActivity,
    // 会议预约
    ...ConferenceTeacherActivity,
    // 统一门户
    ...PortalTeacherActivity,
    ...RoutesNew,
    // * 错误页面
    ...errors,
  ],
});

const oauthPaths = ['/portal/user/todo', '/conference/teacher/week_schedule', '/portal/user/informs'];

router.beforeEach((to, from, next) => {
  if (from.path === '/' && oauthPaths.includes(to.path) && to.query.internal !== 'redirect') {
    utils.setRedirectPath(to.path);
    next({
      path: '/oauth',
      query: { ...to.query },
      replace: true,
    });
  } else {
    next();
  }
});

export default router;
