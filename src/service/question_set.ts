import { BaseService } from './BaseService';
import { IAnswerSet } from './answer_set';

export interface IQuestionSet {
  id?: number;
  type?: any;
  name?: any;
  category?: any;
  meta?: any;
  school_id?: any;
  creator_id?: number;
  creator_type?: string;
  creator_name?: string;
  questionable_type?: 'Topic';
  questionable_id?: number;
  question_catalog_id?: number;
  answer_mode?: 'disable' | 'enable' | 'auto'; // 隐藏答案/公布答案/答题后自动公布答案)
  own_answer_sets?: IAnswerSet;
  created_at?: string;
  updated_at?: string;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  question_sets: IQuestionSet[];
}

interface copyFormData {
  name: string;
  questionable_type: 'Topic';
  questionable_id: number;
}

/**
 * 教师端管理试卷
 */
export class CatalogQuestionSet extends BaseService {
  private resource = '/comm/user_res_catalog/question_catalogs/{question_catalog_id}/question_sets';

  constructor(args: { questionCatalogId: number }) {
    super();
    const { questionCatalogId } = args;
    this.resource = `/comm/user_res_catalog/question_catalogs/${questionCatalogId}/question_sets`;
  }

  fetch(params: object) {
    return this.request.get<IResponse>(`${this.resource}`, {
      params,
    });
  }
  find(id: number) {
    return this.request.get<IQuestionSet>(`${this.resource}/${id}`);
  }
  copy(args: { id: number; formData: copyFormData }) {
    return this.request.post<IQuestionSet>(`${this.resource}/${args.id}/copy`, {
      question_set: args.formData,
    });
  }
  create(questionSet: IQuestionSet) {
    return this.request.post<IQuestionSet>(`${this.resource}`, {
      question_set: questionSet,
    });
  }
  update(questionSet: IQuestionSet) {
    return this.request.patch(`${this.resource}/${questionSet.id}`, {
      question_set: questionSet,
    });
  }
  delete(id: number) {
    return this.request.delete(`${this.resource}/${id}`);
  }
}

/**
 * 论坛试卷接口
 */
export class TeachingQuestionSet extends BaseService {
  topicQuestionSets(args: { courseId: number; topicId: number }) {
    const { courseId, topicId } = args;
    return this.request.get<IResponse>(`/teaching/user/courses/${courseId}/topics/${topicId}/question_sets`, {
      params: {
        page: 1,
        per_page: 1000,
      },
    });
  }
}

/**
 * 老师/巡课老师/学生用来查看相关的接口
 */
export class UserViewQuestionSet extends BaseService {
  find(id: number) {
    return this.request.get<IQuestionSet>(`/comm/user_view/question_sets/${id}`);
  }
}

/**
 * 学生用来提交试卷相关的接口
 */
export class UserOwnQuestionSet extends BaseService {
  fetch(params: object) {
    return this.request.get<IResponse>('/comm/user_own/question_sets', {
      params,
    });
  }
  find(id: number) {
    return this.request.get<IQuestionSet>(`/comm/user_own/question_sets/${id}`);
  }
  create(questionSet: IQuestionSet) {
    return this.request.post<IQuestionSet>(`/comm/user_own/question_sets`, {
      question_set: questionSet,
    });
  }
  update(questionSet: IQuestionSet) {
    return this.request.patch(`/comm/user_own/question_sets/${questionSet.id}`, {
      question_set: questionSet,
    });
  }
  delete(id: number) {
    return this.request.delete(`/comm/user_own/question_sets/${id}`);
  }
}

export namespace QuestionSetService {
  export const UserView = new UserViewQuestionSet();
  export const UserOwn = new UserOwnQuestionSet();
  export const Teaching = new TeachingQuestionSet();
}
