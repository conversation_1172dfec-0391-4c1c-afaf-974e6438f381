import { BaseService } from './BaseService';

export interface IStudent {
  id: number;
  name: string;
  code: string;
  college_name: string;
  major_name: string;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  students: IStudent[];
}

class TeachingStudent extends BaseService {
  fetchByLesson(args: { lessonId: number; params: IObject }) {
    return this.request.get<IResponse>(`/teaching/teacher/inspect/lessons/${args.lessonId}/students`, {
      params: args.params,
    });
  }

  fetchByCourse(args: { courseId: number; params: IObject }) {
    return this.request.get<IResponse>(`/teaching/teacher/share/courses/${args.courseId}/students`, {
      params: args.params,
    });
  }
}

export namespace StudentService {
  export const teaching = new TeachingStudent();
}
