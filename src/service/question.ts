import { BaseService } from './BaseService';

export interface IQuestion {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: QuestionTypes;
  title?: string;
  choices?: IQuestionChoices;
  answer_meta?: { value: string };
  question_set_id?: number;
  position?: number;
}

export interface IQuestionChoices {
  options?: Array<{ key: string; value: string }>; // 单选题 | 多选题 | 填空题
}

export enum QuestionTypes {
  single = 'Question::SingleChoice',
  multiple = 'Question::MultipleChoice',
  fill = 'Question::FillBlank',
  essay = 'Question::Essay',
  any = 'Question::Any',
}
export const QuestionTypeMap = {
  [QuestionTypes.single]: { text: '单选题', value: QuestionTypes.single },
  [QuestionTypes.multiple]: { text: '多选题', value: QuestionTypes.multiple },
  [QuestionTypes.fill]: { text: '填空题', value: QuestionTypes.fill },
  [QuestionTypes.essay]: { text: '简单题', value: QuestionTypes.essay },
  [QuestionTypes.any]: { text: '综合题', value: QuestionTypes.any },
};
export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  questions: IQuestion[];
}

export interface IQuestionAna {
  answer_count?: number;
  title?: string;
  type?: QuestionTypes;
  stat_info: Array<{ key: string; value: string; count: number }>;
}

export class QuestionService extends BaseService {
  private namespace = '/comm/user_res_catalog';
  private resource =
    '/comm/user_res_catalog/question_catalogs/{question_catalog_id}/question_sets/{question_set_id}/questions';

  constructor(args: { questionCatalogId: number; questionSetId: number }) {
    super();
    const { questionCatalogId, questionSetId } = args;
    this.resource = `${this.namespace}/question_catalogs/${questionCatalogId}/question_sets/${questionSetId}/questions`;
  }

  fetch(params: object) {
    return this.request.get<IResponse>(`${this.resource}`, {
      params,
    });
  }
  find(id: number) {
    return this.request.get<IQuestion>(`${this.resource}/${id}`);
  }
  create(question: IQuestion) {
    return this.request.post<IQuestion>(`${this.resource}`, {
      question,
    });
  }
  update(question: IQuestion) {
    return this.request.patch(`${this.resource}/${question.id}`, {
      question,
    });
  }
  delete(id: number) {
    return this.request.delete(`${this.resource}/${id}`);
  }
}

export class UserViewQuestionService extends BaseService {
  static fetch(questionSetId: number, params: object) {
    return this.request.get<IResponse>(`/comm/user_view/question_sets/${questionSetId}/questions`, {
      params,
    });
  }
  static find(questionSetId: number, id: number) {
    return this.request.get<IQuestion>(`/comm/user_view/question_sets/${questionSetId}/questions/${id}`);
  }
  static findAna(questionSetId: number, id: number) {
    return this.request.post<IQuestionAna>(`/comm/user_view/question_sets/${questionSetId}/questions/${id}/ana`);
  }
}

export class UserOwnQuestionService extends BaseService {
  static fetch(questionSetId: number, params: object) {
    return this.request.get<IResponse>(`/comm/user_own/question_sets/${questionSetId}/questions`, {
      params,
    });
  }
  static find(questionSetId: number, id: number) {
    return this.request.get<IQuestion>(`/comm/user_own/question_sets/${questionSetId}/questions/${id}`);
  }
  static create(questionSetId: number, question: IQuestion) {
    return this.request.post<IQuestion>(`/comm/user_own/question_sets/${questionSetId}/questions`, {
      question,
    });
  }
  static update(questionSetId: number, question: IQuestion) {
    return this.request.patch<IQuestion>(`/comm/user_own/question_sets/${questionSetId}/questions/${question.id}`, {
      question,
    });
  }
  static delete(questionSetId: number, id: number) {
    return this.request.delete<IQuestion>(`/comm/user_own/question_sets/${questionSetId}/questions/${id}`);
  }
}
