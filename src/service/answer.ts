import { BaseService } from './BaseService';
import { IQuestion, QuestionTypes } from './question';
import { IFile } from '../models/file';

export interface IAnswer {
  id: number;
  created_at?: string;
  updated_at?: string;
  type?: any;
  answer_set_id?: number;
  question_id?: number;
  attachments?: { files: IFile[] };
  value?: any;
  meta?: any;
  question?: IQuestion;
  answer_meta?: { value: string };
  state?: 'doing' | 'todo' | 'done';
  isError?: boolean;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  answers: IAnswer[];
}

export class UserOwnAnswer extends BaseService {
  fetch(answerSetId: number, params: object) {
    return this.request.get<IResponse>(`/comm/user_own/answer_sets/${answerSetId}/answers`, {
      params,
    });
  }
  find(answerSetId: number, id: number) {
    return this.request.get<IAnswer>(`/comm/user_own/answer_sets/${answerSetId}/answers/${id}`);
  }
  update(answerSetId: number, answer: IAnswer) {
    return this.request.patch(`/comm/user_own/answer_sets/${answerSetId}/answers/${answer.id}`, {
      answer,
    });
  }

  getProcessedAnswer(answer: IAnswer) {
    let { value } = answer;
    if (answer.question!.type === QuestionTypes.multiple) {
      value = value ? value.split(',') : [];
    }
    const rightValue = UserOwnAnswer.getAnswerValueByType(answer, 'answer_meta.value');
    const myValue = UserOwnAnswer.getAnswerValueByType(answer, 'value');
    return {
      ...answer,
      attachments: answer.attachments || { files: [] },
      value,
      rightValue,
      myValue,
      isError: UserOwnAnswer.isError(answer),
    };
  }

  static isError(answer: IAnswer) {
    const { value, answer_meta, type } = answer;
    if (!answer_meta) return false;
    if (type !== QuestionTypes.multiple) {
      return value !== answer_meta.value;
    }
    const valueAry = value.split(',');
    const rightValueAry = answer_meta.value.split(',');
    return !(valueAry.length === rightValueAry.length && valueAry.every((o: string) => rightValueAry.includes(o)));
  }

  static getAnswerValueByType(answer: IAnswer, dataIndex: string = 'value') {
    const { question, value, answer_meta } = answer;
    const answerMeta = answer_meta || { value: '' };
    const _value = dataIndex === 'value' ? String(value || '') : String(answerMeta.value || '');
    if (question!.type === QuestionTypes.fill || question!.type === QuestionTypes.essay) {
      return _value;
    }
    const keys: string[] = _value.split(',');
    const options = question!.choices ? question!.choices.options || [] : [];
    return keys.map(k => this.getAnswerKey(k, options)).join(' '); // 返回答案 key
  }
  static getOptionIndexKey(index: number) {
    return 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.charAt(index);
  }
  static getAnswerKey(answer: string, options: any[]) {
    const index = (options || []).findIndex(o => o.key === answer);
    return this.getOptionIndexKey(index);
  }
}

export namespace AnswerService {
  export const UserOwn = new UserOwnAnswer();
}
