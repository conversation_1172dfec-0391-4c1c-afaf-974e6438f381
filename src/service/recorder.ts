/**
 * 课程学习记录
 */
import { BaseService } from './BaseService';
import { IStudent } from './student';

export enum SourceType {
  Lesson = 'Teaching::Lesson',
  LessonItem = 'Teaching::LessonItem',
}

export interface IRecorder {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: string;
  user_type?: 'Student' | 'Teacher';
  user_id?: number;
  semester_id?: number;
  course_set_id?: number;
  course_id?: number;
  source_type?: SourceType;
  source_id?: number;
  prepare_in_sec?: number; // 课前学习时长
  study_in_sec?: number; // 课中学习时长
  review_in_sec?: number; // 课后学习时长
  total_in_sec?: number; // 总学习时长
  state?: string;
  meta?: IObject;
  user?: IStudent;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  recorders: IRecorder[];
}

type ShareArgs = { sourceType: SourceType; sourceId: number; courseId: number; lessonId: number };

class StudentService extends BaseService {
  find(args: ShareArgs) {
    const { sourceType, sourceId, courseId, lessonId } = args;
    return this.request.get<IRecorder>(`/teaching/student/courses/${courseId}/student_recorder`, {
      params: {
        source_type: sourceType,
        source_id: sourceId,
        lesson_id: lessonId,
      },
    });
  }

  update(args: ShareArgs & { recorder: IRecorder }) {
    const { sourceType, sourceId, courseId, lessonId, recorder } = args;
    return this.request.patch(
      `/teaching/student/courses/${courseId}/student_recorder`,
      {
        recorder,
      },
      {
        params: {
          source_type: sourceType,
          source_id: sourceId,
          lesson_id: lessonId,
        },
      },
    );
  }
}

class TeacherService extends BaseService {
  fetchStudentRecorders(args: ShareArgs & { params: object }) {
    const { sourceType, sourceId, courseId, lessonId, params } = args;
    return this.request.get<IResponse>(`/teaching/teacher/share/courses/${courseId}/student_recorders`, {
      params: {
        source_type: sourceType,
        source_id: sourceId,
        lesson_id: lessonId,
        ...params,
      },
    });
  }

  findStudentRecorder(args: ShareArgs & { recorderId: IObject }) {
    const { sourceType, sourceId, courseId, lessonId, recorderId } = args;
    return this.request.get<IRecorder>(`/teaching/teacher/share/courses/${courseId}/student_recorders/${recorderId}`, {
      params: {
        source_type: sourceType,
        source_id: sourceId,
        lesson_id: lessonId,
      },
    });
  }

  find(args: ShareArgs) {
    const { sourceType, sourceId, courseId, lessonId } = args;
    return this.request.get<IRecorder>(`/teaching/teacher/courses/${courseId}/teacher_recorder`, {
      params: {
        source_type: sourceType,
        source_id: sourceId,
        lesson_id: lessonId,
      },
    });
  }

  update(args: ShareArgs & { recorder: IRecorder }) {
    const { sourceType, sourceId, courseId, lessonId, recorder } = args;
    return this.request.patch(
      `/teaching/teacher/courses/${courseId}/teacher_recorder`,
      {
        recorder,
      },
      {
        params: {
          source_type: sourceType,
          source_id: sourceId,
          lesson_id: lessonId,
        },
      },
    );
  }
}

export namespace RecorderService {
  export const student = new StudentService();
  export const teacher = new TeacherService();
}
