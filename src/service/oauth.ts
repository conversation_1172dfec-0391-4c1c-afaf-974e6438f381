import { BaseService } from './BaseService';

export class OauthService extends BaseService {
  // 获取token
  static fetchToken() {
    return this.request.post('/oauth2/stiei/puttoken');
  }

  // 获取账号
  static fetchAccount(paramsvalue: string, openid: string) {
    return this.request.post('/oauth2/stiei/get_account', { paramsvalue, openid });
  }

  // 三方认证获取用户信息， 直接返回用户登录信息
  static fetchInfo(paramsvalue: string, openid: string, account: string) {
    return this.request.post('/oauth2/stiei/putuserinfo', { paramsvalue, openid, account });
  }

  // 释放openid
  static destroy(openid: string) {
    return this.request.post('/oauth2/stiei/releaseopenid', { openid });
  }
}
