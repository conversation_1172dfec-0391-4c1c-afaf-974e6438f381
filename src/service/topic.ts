/**
 * 学生只能发论坛，只有老师可以发三种
 * 而且学生创建的时候，是不可以有配置的
 */
import { BaseService } from './BaseService';

interface IUser {
  id: number;
  name: string;
  code: string;
  college_name: string;
  major_name: string;
}

export enum TopicType {
  Discuss = 'Topics::Discuss',
  Forum = 'Topics::Forum',
  Notice = 'Topics::Notice',
  Question = 'Topics::QuestionAnswer',
}

export interface ITopic {
  id?: number;
  created_at?: string;
  updated_at?: string;
  school_id?: number;
  college_id?: number;
  major_id?: number;
  user_type?: string;
  user_id?: number;
  source_type?: string;
  source_id?: number;
  type?: TopicType;
  title?: string;
  body?: string;
  state?: 'published' | 'pending';
  cover_image?: any;
  view_count?: number;
  like_count?: number;
  star_count?: number;
  comment_count?: number;
  deleted_at?: any;
  comment_expire_at?: string; // 评论到期时间
  can_reply?: boolean; // 能否评论
  teach_permit?: string; // 巡课相关权限
  view_permit?: 'all' | 'part'; // 可见权限
  reply_permit?: 'all' | 'part'; // 回复权限
  visible_conf?: 'visible' | 'invisible'; // 互相是否可见
  suggest?: boolean; // 是否置顶
  suggest_at?: any; // 置顶时间
  view_student_ids?: number[];
  reply_student_ids?: number[];
  view_students?: IUser[];
  reply_students?: IUser[];
  operations?: string[];
  is_new?: boolean;
  user?: IUser;
  meta?: IObject;
  attachments?: {
    files: [];
  };
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  topics: ITopic[];
  teach_permit: string;
}

class TeachingTopic extends BaseService {
  get topicUiOptions() {
    return {
      [TopicType.Discuss]: { label: '讨论', value: TopicType.Discuss, type: 'success' },
      [TopicType.Forum]: { label: '论坛', value: TopicType.Forum, type: 'primary' },
      [TopicType.Notice]: { label: '通知', value: TopicType.Notice, type: 'warning' },
      [TopicType.Question]: { label: '试卷', value: TopicType.Question, type: 'danger' },
    } as { [key in TopicType]: { label: string; value: TopicType; type: string } };
  }

  // ======================== teaching ========================
  fetchByCourse(courseId: number, params: IObject) {
    return this.request.get<IResponse>(`/teaching/user/courses/${courseId}/topics`, { params });
  }

  findByCourse(params: { courseId: number; topicId: number }) {
    return this.request.get<ITopic>(`/teaching/user/courses/${params.courseId}/topics/${params.topicId}`);
  }

  createByCourse(params: { courseId: number; topic: ITopic }) {
    return this.request.post<ITopic>(`/teaching/user/courses/${params.courseId}/topics`, {
      topic: params.topic,
    });
  }

  updateByCourse(params: { courseId: number; topic: ITopic }) {
    return this.request.patch(`/teaching/user/courses/${params.courseId}/topics/${params.topic.id}`, {
      topic: params.topic,
    });
  }

  deleteByCourse(params: { courseId: number; topicId: number }) {
    return this.request.delete(`/teaching/user/courses/${params.courseId}/topics/${params.topicId}`);
  }
}

class CommTopic extends BaseService {
  fetch(params: object) {
    return this.request.get<IResponse>('/comm/user/topics', { params });
  }

  find(topicId: number) {
    return this.request.get<ITopic>(`/comm/user/topics/${topicId}`);
  }

  create(topic: ITopic) {
    return this.request.post<ITopic>('/comm/user/topics', {
      topic,
    });
  }

  update(topic: ITopic) {
    return this.request.patch(`/comm/user/topics/${topic.id}`);
  }

  delete(topicId: number) {
    return this.request.delete(`/comm/user/topics/${topicId}`);
  }
}

export namespace TopicService {
  export const teaching = new TeachingTopic();
  export const comm = new CommTopic();
}
