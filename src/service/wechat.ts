import qs from 'qs';
import { BaseService } from './BaseService';

export class WechatService extends BaseService {
  static apiUrl: string = `${process.env.VUE_APP_API_DOMAIN}/soa-auth`;
  static appId: string = process.env.VUE_APP_APPID as string;
  static tag: string = 'stiei';

  /**
   * 绑定微信号
   * 调用接口以前，客户端请确认用户已经登录四维系统.
   * @param code 微信临时授权凭证
   */
  static bind(code: string) {
    return this.request.post(`${this.apiUrl}/wechat/bind`, {
      wechat: this.tag,
      code,
    });
  }

  static bindWechat(openid: string) {
    return this.request.post(`${this.apiUrl}/wechat/bind/bind_wechat`, {
      wechat: this.tag,
      openid,
    });
  }

  /**
   * 调用接口以前，客户端请确认用户已经登录四维系统.
   * @param code 微信临时授权凭证
   */
  static unbind() {
    return this.request.delete(`${this.apiUrl}/wechat/bind`);
  }

  /**
   * 使用微信授权登录
   * @param code 微信临时授权凭证
   */
  static signInWithWechatCode(code: string) {
    return this.request.post(`${this.apiUrl}/wechat/signin`, {
      wechat: this.tag,
      code,
    });
  }

  /**
   * 微信内发起授权请求
   * 微信重定向，并附带 code 参数
   */
  static auth(redirectPath: string) {
    let redirectUrl = '';
    if (redirectPath.substr(0, 4) === 'http') {
      redirectUrl = encodeURI(redirectPath);
    } else {
      let publicPath = process.env.VUE_APP_PUBLIC_PATH || '/';
      publicPath = publicPath.substr(-1) === '/' ? publicPath : `${publicPath}/`;
      const formatPath = redirectPath.charAt(0) === '/' ? redirectPath.substring(1) : redirectPath;
      const formatUrl = `${window.location.origin}${publicPath}${formatPath}`;
      redirectUrl = encodeURI(formatUrl);
    }
    const wechatAuthUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize';
    const params = qs.stringify({
      appid: this.appId,
      redirect_uri: redirectUrl,
      response_type: 'code',
      scope: 'snsapi_base',
      state: 'default',
    });
    window.location.href = `${wechatAuthUrl}?${params}#wechat_redirect`;
  }

  /**
   * 是否是在微信客户端内
   */
  static isWechatClient() {
    const ua = window.navigator.userAgent.toLowerCase();
    return (ua.match(/micromessenger/g) || []).includes('micromessenger');
  }

  static getOpenid(code: string) {
    return this.request.post(`${this.apiUrl}/wechat/openid/get`, {
      code,
    });
  }
}
