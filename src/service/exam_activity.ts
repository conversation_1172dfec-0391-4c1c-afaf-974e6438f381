/**
 * 考试活动
 */
import utils from '@/utils';
import wechatSdk from '@/utils/wechatSdk';
import registerModel from '@/models/comm/register';
import { BaseService } from './BaseService';
import { IQuestionSet } from './question_set';
import { IAnswer } from './answer';

interface Student {
  id: number;
  name: string;
  code: string;
}

export interface IActivity {
  id?: number;
  created_at?: string;
  updated_at?: string;
  creator_type?: string;
  creator_id?: number;
  creator_name?: string;
  creator_code?: string;
  title?: string;
  duration_in_min?: number;
  start_at?: string;
  state?: string;
  student_count?: number;
  students?: Student[];
  paste_student_ids?: number[];
  question_set?: IQuestionSet;
  question_set_id?: number;
  own_answer_sets?: IAnswer;
  answer_set_group_count?: {
    todo: number;
    doing: number;
    done: number;
    checked: number;
  };
  time_infos: {
    to_start: number;
    to_start_off: number;
    to_end: number;
  };
}

interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  activities: IActivity[];
}

export enum StateText {
  todo = '未开始',
  doing = '进行中',
  done = '已完成',
}

export class ExamActivity extends BaseService {
  static fetch(params: object) {
    return this.request.get<IResponse>('/exam/student/activities.json', {
      params,
    });
  }
  static find(id: number) {
    return this.request.get<IActivity>(`/exam/student/activities/${id}.json`);
  }
  static async setLog(id: number) {
    const { cip } = utils.getIp();
    const { longitude, latitude } = await wechatSdk.getLocation();
    const { data } = await registerModel.getAddress({ lon: longitude, lat: latitude });
    return this.request.post(`/exam/student/activities/${id}/log`, {
      activity: {
        address: data.address,
        ip: cip,
      },
    });
  }
}
