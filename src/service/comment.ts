import { BaseService } from './BaseService';
import { TopicType } from './topic';

export interface IComment {
  id?: number;
  created_at?: string;
  updated_at?: string;
  commentable_id?: number;
  commentable_type?: string;
  title?: any;
  body?: string;
  subject?: any;
  user_id?: number;
  user_type?: string;
  parent_id?: any;
  lft?: number;
  rgt?: number;
  user_name?: string;
  attachments?: IObject;
  files?: any[];
}

class TeachingComment extends BaseService {
  /**
   * 获取 topic 评论（通知类型，没有评论）
   * @param args topic 参数
   */
  fetchTopicComments(args: { courseId: number; topicId: number; topicType: TopicType; params: object }) {
    const { courseId, topicId, topicType, params } = args;
    return this.request.get(`/teaching/user/courses/${courseId}/topics/${topicId}/comments`, {
      params: {
        ...params,
        mode: topicType === 'Topics::Discuss' ? 'discuss' : null,
      },
    });
  }

  deleteTopicComment({ courseId, topicId, commentId }: { courseId: number; topicId: number; commentId: number }) {
    return this.request.delete(`/teaching/user/courses/${courseId}/topics/${topicId}/comments/${commentId}`);
  }

  create(comment: IComment) {
    return this.request.post('/comm/user/comments', {
      comment,
    });
  }

  update(comment: IComment) {
    return this.request.patch(`/comm/user/comments/${comment.id}`, {
      comment,
    });
  }
}

export namespace CommentService {
  export const teaching = new TeachingComment();
}
