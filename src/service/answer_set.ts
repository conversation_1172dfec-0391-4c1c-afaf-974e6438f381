import { BaseService } from './BaseService';
import { IAnswer } from './answer';
import { QuestionTypes } from './question';

export interface MetaCatalog {
  name?: string;
  question_type?: QuestionTypes;
  total_score?: number;
  score_mode?: 'none' | 'avg';
  questions: Array<{ id: number; score: number }>;
  _answers: IAnswer[];
  _done: number;
}

export type AnswerSetState = 'done' | 'todo' | 'doing' | 'checked';

export interface IAnswerSet {
  id?: number;
  created_at?: string;
  updated_at?: string;
  type?: any;
  state?: AnswerSetState;
  question_set_id?: number;
  meta?: {
    catalogs: MetaCatalog[];
  };
  answerable_type?: string;
  answerable_id?: number;
  creator_type?: string;
  creator_id?: number;
  answers?: IAnswer[];
  answers_attributes?: Array<{
    id: number;
    value: string;
    meta: IObject;
  }>;
}

export interface IResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  answer_sets: IAnswerSet[];
}

export class UserOwnAnswerSet extends BaseService {
  fetch(params: object) {
    return this.request.get<IResponse>('/comm/user_own/answer_sets', {
      params,
    });
  }
  find(id: number) {
    return this.request.get<IAnswerSet>(`/comm/user_own/answer_sets/${id}`);
  }
  create(answerSet: IAnswerSet) {
    return this.request.post<IAnswerSet>(`/comm/user_own/answer_sets`, {
      answer_set: answerSet,
    });
  }
  update(answerSet: IAnswerSet) {
    return this.request.patch(`/comm/user_own/answer_sets/${answerSet.id}`, {
      answer_set: answerSet,
    });
  }
  delete(id: number) {
    return this.request.delete(`/comm/user_own/answer_sets/${id}`);
  }
}

export namespace AnswerSetService {
  export const UserOwn = new UserOwnAnswerSet();
}
