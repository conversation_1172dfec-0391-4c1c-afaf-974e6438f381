{"name": "campus-mobile", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "format": "pretty-quick", "lint": "vue-cli-service lint", "test": "vue-cli-service test:unit", "test:coverage": "vue-cli-service test:unit --coverage", "ready": "git pull && yarn && npm run lint", "deploy": "npm run ready && vue-cli-service build --mode preproduction && node scripts/deploy.js", "publish": "npm run ready && vue-cli-service build && node scripts/deploy.js", "publish:sync": "node scripts/deploy.js"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "", "pre-push": ""}}, "dependencies": {"@antv/g2": "^3.5.11", "@types/pluralize": "^0.0.29", "@types/crypto-js": "^4.2.2", "async-validator": "^3.2.3", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "better-scroll": "^2.0.0-beta.2", "change-case": "^4.1.2", "core-js": "^3.1.2", "crypto-js": "^4.2.0", "dayjs": "^1.8.17", "file-saver": "^2.0.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.15", "normalize.css": "^8.0.1", "pluralize": "^8.0.0", "qrcode": "^1.4.1", "qs": "^6.7.0", "spark-md5": "^3.0.0", "vant": "^2.9.0", "video.js": "^7.6.6", "vue": "^2.6.10", "vue-class-component": "^7.0.2", "vue-infinite-scroll": "^2.0.2", "vue-pdf": "^4.0.8", "vue-property-decorator": "^8.1.0", "vue-router": "^3.0.3", "vuex": "^3.0.1", "vuex-class": "^0.3.2", "vuex-module-decorators": "^1.0.1", "vuex-persistedstate": "^2.5.4", "vxe-table": "^3.2.0", "weixin-js-sdk": "^1.4.0-test", "xe-utils": "^3.1.11"}, "devDependencies": {"@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@iboying/easy-deploy": "^0.3.0", "@types/file-saver": "^2.0.1", "@types/jest": "^23.1.4", "@types/jsonwebtoken": "^8.3.8", "@types/lodash": "^4.14.136", "@types/qrcode": "^1.3.3", "@types/qs": "^6.5.3", "@types/spark-md5": "^3.0.1", "@types/video.js": "^7.3.3", "@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-plugin-router": "^4.0.0", "@vue/cli-plugin-typescript": "^4.0.0", "@vue/cli-plugin-unit-jest": "^4.0.0", "@vue/cli-plugin-vuex": "^4.0.0", "@vue/cli-service": "^4.0.0", "@vue/eslint-config-airbnb": "^4.0.0", "@vue/eslint-config-typescript": "^4.0.0", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "chalk": "^2.4.2", "eslint": "^5.16.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^5.0.0", "husky": "^3.0.0", "less": "^3.9.0", "less-loader": "^4.1.0", "prettier": "^1.18.2", "pretty-quick": "^1.11.1", "pug-lint": "^2.6.0", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "typescript": "~3.5.3", "vue-cli-plugin-pug": "^1.0.7", "vue-template-compiler": "^2.6.10"}, "postcss": {"plugins": {"autoprefixer": {}}}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/airbnb", "@vue/typescript", "plugin:prettier/recommended"], "rules": {"import/no-cycle": 0, "import/no-extraneous-dependencies": 0, "no-param-reassign": 0, "class-methods-use-this": 0, "lines-between-class-members": 0, "camelcase": 0, "object-curly-newline": 0, "no-underscore-dangle": 0, "import/prefer-default-export": 0, "max-len": ["error", {"code": 120}]}, "parserOptions": {"parser": "@typescript-eslint/parser"}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "browserslist": ["Android >= 4.0", "iOS >= 7"], "jest": {"preset": "@vue/cli-plugin-unit-jest/presets/typescript-and-babel"}, "prettier": {"printWidth": 120, "singleQuote": true, "proseWrap": "always", "trailingComma": "all"}}