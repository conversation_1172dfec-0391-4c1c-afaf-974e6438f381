/* eslint-disable */
const fs = require('fs');
const EasyDeploy = require('@iboying/easy-deploy');
const chalk = require('chalk');

const localPath = 'dist/';
const TARGET = process.env.npm_lifecycle_event;
const isPublishEnv = TARGET.includes('publish');
const deployPath = isPublishEnv ? '/dist' : '/dist-test';
const tips = isPublishEnv ? '【生产应用】' : '【测试应用】';

(async () => {
  try {
    const commitId = await EasyDeploy.shell('git rev-parse head');
    fs.writeFileSync(`${localPath}version.json`, commitId, {
      encoding: 'utf-8',
    });
    const instance = new EasyDeploy({
      username: 'app',
      host: 'stiei2',
      port: 2200,
      localPath: 'dist/',
      remotePath: `/mnt/app/campus-mobile${deployPath}`,
      // username: 'app',
      // host: '************',
      // port: 22,
      // localPath: 'dist/',
      // remotePath: `/home/<USER>/campus-mobile${deployPath}`,
    });
    console.log(chalk.cyan('开始部署应用'));
    await instance.sync('-aI --delete');
    console.log(chalk.green(`部署${tips}成功`));
  } catch (err) {
    console.log(chalk.red(`部署${tips}失败`));
    throw err;
  }
})();
